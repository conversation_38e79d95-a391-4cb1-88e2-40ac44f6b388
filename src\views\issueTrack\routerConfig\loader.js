import {
  issue_506_get_subclass_by_issue_node_query, 
  setting_202_get_team_allusergrp_query, 
  setting_320_get_node_priv_query, 
  setting_407_get_console_selection_list_query,
  team_530_get_obj_node_info_query, 
  team_553_get_front_query_query, 
  team_107_get_query_detail_query,
  issue_512_get_issue_total_query
} from "../../../api/query/query"

export const issueViewLoader = (queryClient, teamId, objNodeId, queryId) => {
  // const setting202Query = setting_202_get_team_allusergrp_query(teamId)
  // const setting320Query = setting_320_get_node_priv_query(teamId, objNodeId)
  // const team530Query = team_530_get_obj_node_info_query(teamId, objNodeId)
  // const issue512Query = issue_512_get_issue_total_query(teamId, objNodeId)
  const setting407Query = setting_407_get_console_selection_list_query(teamId)
  const issue506Query = issue_506_get_subclass_by_issue_node_query(teamId, objNodeId)
  const team553Query = team_553_get_front_query_query(teamId, queryId)

  return ({
    promise: new Promise(async (resolve, reject) => {

      // const setting320Result = await queryClient.fetchQuery(setting320Query);
      // if (setting320Result.resultCode === 203) reject({ pageCode: 203, errorMessage: "资源不存在" })
      // if (setting320Result.resultCode !== 200) reject({ pageCode: 204, errorMessage: "服务器出错了" })
      // if (setting320Result.privRead === 0) reject({ pageCode: 205, errorMessage: "页面无权限" })

      // 接口调用
      // (1) 获取项目自定义表单字段
      // const { projectId, subclassNid, isLoading: isLoadingGetSubclass } = useQueryIssue506_getSubclass(teamId, issueListNodeId);
      const issue506Result = queryClient.getQueryData(issue506Query.queryKey) ?? (await queryClient.fetchQuery({ ...issue506Query, cacheTime: Infinity }));
      // (2) 获取节点信息objInfo及queryObjId
      // const { queryObjId, objInfo, isLoading: isLoadingObjNodeInfo } = useQueryGetObjNodeInfo(teamId, issueListNodeId);
      // const objInfo = queryClient.getQueryData(team530Query.queryKey) ?? (await queryClient.fetchQuery({ ...team530Query, cacheTime: Infinity }));
      // (3) 人员/字典数据列表
      // const { data: userList, isLoading: isLoadingTeamAllUsers } = useQuerySetting202_getTeamAllUsers(teamId); //人员
      // const { data: selectionList, isLoading: isLoadingCodeValueList } = useQuerySetting407_getCodeValueList(teamId); //字典数据
      // const userList = queryClient.getQueryData(setting202Query.queryKey) ?? (await queryClient.fetchQuery({ ...setting202Query, cacheTime: Infinity }));
      const selectionList = queryClient.getQueryData(setting407Query.queryKey) ?? (await queryClient.fetchQuery({ ...setting407Query, cacheTime: Infinity }));
      //（4）自定义表单属性列表
      // const setting409Query = setting_409_get_team_attrgrp_props_query(teamId, objNodeId, issue506Result?.subclassNid);
      // const subclassAttrList = queryClient.getQueryData(setting409Query.queryKey) ?? (await queryClient.fetchQuery({ ...setting409Query, cacheTime: Infinity }));
      // (5) 如果有queryObjId，则必须等到获取过滤条件后再调用issue列表
      //!!queryId为依赖项，依赖于team530_get_obj_node_info接口
      // const { data: _criteriaList, isLoadingGetQueryDetail } = useQueryTeam107_getQueryDetail(teamId, objInfo?.objId, !!objInfo?.objId);
      //（6）定位信息，查询条件及页码
      // const { issueQuery } = useQueryGetFrontQuery(teamId, issueQueryId, !!issueQueryId); // 根据queryId获取查询条件

      // 根据url获取的搜索条件及关键字
      let criteriaList = [];
      let queryKeywords = '';
      // 如果有queryId则不考虑是否存在保存的条件，以queryId条件为主
      if (queryId) {
        const issue553Result = queryClient.getQueryData(team553Query.queryKey) ?? (await queryClient.fetchQuery({ ...team553Query, cacheTime: Infinity }));
        const issueQuery = JSON.parse(issue553Result?.queryJson);
        criteriaList = issueQuery?.criteriaList || []
        queryKeywords = issueQuery?.keywords || ''
      } /* else {
        // 如果存在objId的issue项目为 带有保存的搜索条件
        if (objInfo?.objId) {
          const team107Query = team_107_get_query_detail_query(teamId, objInfo?.nodeId); // query_detail更改为nodeId获取 2023-03-21
          criteriaList = queryClient.getQueryData(team107Query.queryKey) ?? (await queryClient.fetchQuery({ ...team107Query, cacheTime: Infinity }));
        }
      } */

      resolve({
        // setting320Result,
        // objInfo,
        // userList,
        // subclassAttrList,
        issue506Result,
        selectionList,
        queryKeywords,
        criteriaList,
      })
    })
  })
}