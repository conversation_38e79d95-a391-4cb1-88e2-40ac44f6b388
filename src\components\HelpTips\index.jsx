import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer } from "antd";
import React, { useEffect, useState } from "react";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { Link } from "react-router-dom";
import {doc_005_get_doc_detail} from "../../api/http";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { team_1503_get_os_help_list_query } from "../../api/query/query"
import "./index.scss";
import {TEditorPreview} from "../TEditor/TEditor";

/**
 * 操作系统内的问号提示
 * @param type 提示类型：link(链接)、popup(弹窗)、hover(黑色问号,鼠标经过)、click(黄色问号,点击)
 * @param isShowText 是否显示字典文案(默认显示：true)
 * @param manualNo 手册编号，用于从字典中筛选数据(如：10010001)
 * @param leftIcon 文案左侧图标
 * @param rightIcon 文案右侧图标
 * @constructor
 */
export default function HelpTips({type, isShowText=true , manualNo, leftIcon, rightIcon, title}) {
  const queryClient = useQueryClient();
  const team1503Query = team_1503_get_os_help_list_query(process.env.REACT_APP_WORKORDER_TEAMID);
  const [osHelpInfo, setOsHelpInfo] = useState({}); // 帮助文档字典数据
  const [tooltipsDrawerOpen, setTooltipsDrawerOpen] = useState(false); // 弹窗类型提示的详情抽屉状态
  useEffect(() => {
    getOsHelpList(manualNo) // 根据manualNo筛选提示
  },[manualNo])

  const getOsHelpList = async(manualNo) => {
    const result = queryClient.getQueryData(team1503Query.queryKey) ?? (await queryClient.fetchQuery({ ...team1503Query, cacheTime: Infinity }));
    if (result.resultCode == 200) {
      let osHelpInfo_ = result.osHelpList.filter(el => el.manualNo === manualNo)[0]
      setOsHelpInfo({...osHelpInfo_})
    }
  }
  return (
    <>
      {
        type === "popup"
         ? 
          <PopupTips tipsInfo={osHelpInfo} isShowText={isShowText} title={title} leftIcon={leftIcon} rightIcon={rightIcon} openPopup={() => setTooltipsDrawerOpen(true)}/>
         : 
          (
            type === "link"
             ? 
              <LinkTips tipsInfo={osHelpInfo} isShowText={isShowText} title={title} leftIcon={leftIcon} rightIcon={rightIcon}/>
             : 
              (
                type === "hover" 
                 ? 
                  <HoverTips tipsInfo={osHelpInfo} isShowText={isShowText} title={title} leftIcon={leftIcon} rightIcon={rightIcon}/>
                 :
                  <ClickTips tipsInfo={osHelpInfo} isShowText={isShowText} title={title} leftIcon={leftIcon} rightIcon={rightIcon}/>
              )
          )
      }
      <TooltipsDrawer 
        open={tooltipsDrawerOpen} 
        tipsInfo={osHelpInfo} 
        onClose={() => setTooltipsDrawerOpen(false)}
      />
    </>
  );
}

// 弹窗类型文案
function PopupTips({tipsInfo, isShowText, title, openPopup, leftIcon, rightIcon}) {
  return (
    <span 
      onClick={openPopup}
    >
      {leftIcon}
      {isShowText ? tipsInfo.title : title}
      {rightIcon}
    </span>
  )
}

// 链接类型
function LinkTips({tipsInfo, isShowText, title, leftIcon, rightIcon}) {
  const linkTipsDoc = () => {
    let url_ = `${process.env.REACT_APP_BASE_URL}/#/help/doc/${tipsInfo.revNodeId}`
    window.open(url_);
  }
  return (
    <span 
      onClick={linkTipsDoc}
    >
      {leftIcon}
      {isShowText ? tipsInfo.title : title}
      {rightIcon}
    </span>
  )
}

// 黑色问号
function HoverTips({tipsInfo, isShowText, title, leftIcon, rightIcon}) {
  const [docDetail, setDocDetail] = useState({});
  useEffect(() => {
    if(!!tipsInfo.revNodeId) {
      getPortalDocDetail(process.env.REACT_APP_PORTAL_TEAMID, tipsInfo.revNodeId)
    }
  },[tipsInfo.revNodeId])
  // 获取官网文档详情
  const getPortalDocDetail = (teamId, objNodeId) => {
    doc_005_get_doc_detail({teamId: teamId, objNodeId: objNodeId + ''}).then(res => {
      if(res.resultCode === 200) {
        setDocDetail({...res.docDetail})
      } else {
        setDocDetail({})
      }
    })
  }
  return (
    <div className="HoverTips">
      {!!leftIcon && <Tooltip 
        title={
          <div>
            <TEditorPreview content={docDetail?.content}/>
          </div>
        } 
        mouseEnterDelay={0.5}
        trigger="hover"
      >
        <span style={{ cursor: 'pointer' }}>{leftIcon}</span>
      </Tooltip>}
      <span>{isShowText ? tipsInfo.title : title}</span>
      {!!rightIcon && <Tooltip 
        title={
          <div>
            <TEditorPreview content={docDetail?.content}/>
          </div>
        } 
        mouseEnterDelay={0.5}
        trigger="hover"
      >
        <span style={{ cursor: 'pointer' }}>{rightIcon}</span>
      </Tooltip>}
    </div>
  )
}

// 黄色问号
function ClickTips({tipsInfo, isShowText, title, leftIcon, rightIcon}) {
  const [docDetail, setDocDetail] = useState({});
  useEffect(() => {
    if(!!tipsInfo.revNodeId) {
      getPortalDocDetail(process.env.REACT_APP_PORTAL_TEAMID, tipsInfo.revNodeId)
    }
  },[tipsInfo.revNodeId])
  // 获取官网文档详情
  const getPortalDocDetail = (teamId, objNodeId) => {
    doc_005_get_doc_detail({teamId: teamId, objNodeId: objNodeId + ''}).then(res => {
      if(res.resultCode === 200) {
        setDocDetail({...res.docDetail})
      } else {
        setDocDetail({})
      }
    })
  }
  return (
    <div className="ClickTips">
      <span>{isShowText ? tipsInfo.title : title}</span>
      <Tooltip 
        title={
          <div>
            <TEditorPreview content={docDetail?.content}/>
          </div>
        } 
        mouseEnterDelay={0.5}
        trigger="click"
      >
        <QuestionCircleOutlined style={{ marginLeft: 4 }}/>
      </Tooltip>
    </div>
  )
}

// 文档详情抽屉
function TooltipsDrawer({open, tipsInfo, onClose}) {
  const [docDetail, setDocDetail] = useState({}); // 文档详情
  useEffect(() => {
    if(open) {
      getPortalDocDetail(process.env.REACT_APP_PORTAL_TEAMID, tipsInfo.revNodeId)
    }
  },[open, tipsInfo.revNodeId])
  // 获取官网文档详情
  const getPortalDocDetail = (teamId, objNodeId) => {
    doc_005_get_doc_detail({teamId: teamId, objNodeId: objNodeId + ''}).then(res => {
      if(res.resultCode === 200) {
        setDocDetail({...res.docDetail})
      } else {

      }
    })
  }
  return (
    <Drawer
      className="TooltipsDrawer"
      title={tipsInfo.title}
      width={800}
      onClose={onClose}
      open={open}
      destroyOnClose
      footer={false}
    >
      <TEditorPreview content={docDetail?.content}/>
    </Drawer>
  )
}