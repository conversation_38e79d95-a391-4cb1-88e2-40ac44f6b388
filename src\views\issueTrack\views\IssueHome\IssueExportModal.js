import { Button, Checkbox, Input, message, Modal, Spin } from "antd";
import React, { useEffect, useState } from "react";
import { eConsoleNodeId, eConsoleNodeType } from "@/utils/enum";
import { LoadingOutlined } from '@ant-design/icons';
import * as httpIssue from "@/api/http";
import { globalUtil } from "@/utils/globalUtil";

/*unused
//excel导出
function IssueExportModal({ visible, issueListNodeId, criteriaList, subclassAttrList, setExcelExportModalVisibleFlg }) {
    // const { teamId } = useParams();
    const teamId =  process.env.REACT_APP_WORKORDER_TEAMID;
    const [loading, setLoading] = useState(false);
    const [excelTitle, setExcelTitle] = useState(""); //excel导出标题
    const [exportImg, setExportImg] = useState(1); //是否上传图片
    const [customColumnArray, setCustomColumnArray] = useState(); // 勾选数组
    const antIcon = (
        <LoadingOutlined style={{ fontSize: 24, }} spin />
    );

    useEffect(() => {
        if (visible) {
            let array = subclassAttrList.filter(el => el.nodeType == eConsoleNodeType.NodeType_1_Attribute).map(item => item.nodeId)
            setCustomColumnArray(array)
            // 重置数据
            setExportImg(1)
        }
    }, [subclassAttrList, visible])

    // 获取excel导出项
    function getExcelSelect() {
        // setCustomColumnArray(subclassAttrList.filter(el => el.nodeType == eConsoleNodeType.NodeType_1_Attribute).map(item => item.nodeId))
        return subclassAttrList.filter(
            _attr => _attr.nodeType == eConsoleNodeType.NodeType_1_Attribute
        ).map(_attr => {
            if (_attr.nodeId == eConsoleNodeId.Nid_11101_Issue_IssueNo || _attr.nodeId == eConsoleNodeId.Nid_11102_Issue_Title || _attr.nodeId == eConsoleNodeId.Nid_11104_Issue_Assignee) {
                return <Checkbox disabled={true} style={{ margin: 10, width: 130 }} value={_attr.nodeId}>
                    {_attr.nodeName}
                </Checkbox>
            } else {
                return <Checkbox disabled={false} style={{ margin: 10, width: 130 }} value={_attr.nodeId}>
                    {_attr.nodeName}
                </Checkbox>
            }
        })
    }

    // excel导出标题
    function excelTitleChange(e) {
        setExcelTitle(e.target.value);
    }

    // issue excel导出
    function handleExportIssue() {
        setLoading(true)
        // 目前query为字符串提交
        let _criteriaListBackend = criteriaList?.length > 0 ?
            // 先过滤begin为空的数据
            criteriaList.filter(el => el.begin != "").map(_criteria => ({
                ..._criteria,
                // 如果是日期 则为字符串，不需要把数组变成逗号
                begin: _criteria.begin.toString(),
            })
            )
            : null;
        var params = {
            teamId: teamId,
            issuegrpNodeId: issueListNodeId,
            query: _criteriaListBackend,
            // customFileName: excelTitle, //取消导出名
            customColumnArray: customColumnArray,
            exportImg: exportImg,
            pageNum: 1, pageSize: 10
        };

        httpIssue.track_014_issue_excel_export(params)
            .then((res) => {
                if (res.resultCode === 200) {
                    globalUtil.success("导出excel成功！")
                    setLoading(false)
                }
            });
    }

    // 可供导出项
    function checkBoxGroup(checkedValue) {
        setCustomColumnArray(checkedValue);
    }

    // 是否上传图片
    function exportImgChange(e) {
        setExportImg(+e.target.checked); //使用+将true,false转换为1,0
    }

    return <Modal className="tms-modal export-modal"
        title={<div className="fontsize-16">导出Excel</div>}
        open={visible}
        onCancel={() => setExcelExportModalVisibleFlg(false)}
        width={600}
        footer={[
            <Button disabled={loading} style={{ textAlign: "center" }} type="primary" onClick={handleExportIssue}>导出excel</Button>
        ]}
        destroyOnClose={true}
    >
        <Spin indicator={antIcon} spinning={loading} tip="加载中...">
            {/-* <div style={{ margin: 20 }}>
                <span style={{ marginLeft: 40 }}>名称:</span>
                <span>
                    <Input style={{ width: '80%', height: '30px', marginLeft: '40px' }} value={excelTitle} onChange={excelTitleChange} placeholder="请输入名称" />
                </span>
            </div> *-/}
            <div className="export-modal-content" >
                <div >
                    <div style={{ margin: 10 }}>请选择导出字段:</div>
                    <div>
                        <Checkbox.Group onChange={checkBoxGroup} defaultValue={customColumnArray}>
                            {getExcelSelect()}
                        </Checkbox.Group>
                        <Checkbox checked={exportImg} style={{ margin: 10, width: 100 }}
                            onChange={exportImgChange}>
                            附件图片
                        </Checkbox>
                    </div>
                </div>
            </div>
        </Spin>
    </Modal>;
}
*/