import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { Box, Typography, Tab, Ta<PERSON>, Button } from "@mui/material";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import "./index.scss";
import {
  isArrayUtils,
  filterWidgetList,
  formatTreeData,
  portalTextWrapHtml,
} from "../../../../utils/commonUtils";
import { formatVariantColor } from "../../../../utils/muiThemeUtils";
import { skipOS } from "../../../../utils/commonUtils";
import { useAuthContext } from "@/context/AppAuthContextProvider";

export default function WhyChoose(props) {
  const { data } = props;
  const authActions = useAuthContext();
  const navigate = useNavigate();
  const location = useLocation();
  const [snapshotValue, setSnapshotValue] = useState();
  const [moduleData, setModuleData] = useState([]);
  const [currentModuleItem, setCurrentModuleItem] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setSnapshotValue(filterWidgetList(formatData, 5)[0]?.widgetId);
      setModuleData([...formatData]);
      setCurrentModuleItem([
        ...(filterWidgetList(formatData, 5)[0]?.children || []),
      ]);
    }
  }, [data?.widgetList]);

  useEffect(() => {
    if (snapshotValue) {
      let arr_ =
        moduleData.filter((el) => el.widgetId === snapshotValue)[0]?.children ||
        [];
      setCurrentModuleItem([...arr_]);
    }
  }, [snapshotValue]);

  // 切换简介tabs
  const snapshotValueChange = (e, value) => {
    setSnapshotValue(value);
  };
  return (
    <>
      {/* 为什么选择iTeam start */}
      {moduleData.length > 0 && (
        <Box
          className="why-choose"
          style={{
            background:
              filterWidgetList(moduleData, 12)[0]?.value || "transparent",
          }}
        >
          <Box className="why-choose-title">
            {!!filterWidgetList(moduleData, 1)[0]?.value && (
              <Typography variant="h4" sx={{ fontSize: 34 }}>
                {filterWidgetList(moduleData, 1)[0]?.value}
              </Typography>
            )}
          </Box>
          <Tabs
            className="why-choose-tabs tms-portal-tabs"
            value={snapshotValue}
            centered
            onChange={snapshotValueChange}
          >
            {filterWidgetList(moduleData, 5).map((item, index) => (
              <Tab
                key={item.widgetId}
                value={item.widgetId}
                label={item.value}
              />
            ))}
          </Tabs>
          <Box className="why-choose-tabbar__body">
            <Box className="why-choose-tabbar__item">
              <Box className="why-choose-advantages">
                {currentModuleItem.map((item, index) => (
                  <Box
                    key={item.widgetId}
                    className="why-choose-advantages-elem"
                  >
                    <Box className="why-choose-advantage">
                      <div>
                        <CheckCircleOutlineIcon
                          sx={{ fontSize: 60, color: "#0077f2" }}
                        />
                        <Box className="why-choose-advantage__title">
                          {filterWidgetList(item?.children || [], 1)[0]?.value}
                        </Box>
                        <Box className="why-choose-advantage__text">
                          {portalTextWrapHtml(
                            filterWidgetList(item?.children || [], 2)[0]?.value
                          )}
                        </Box>
                      </div>
                      {filterWidgetList(item?.children || [], 4).length > 0 && (
                        <div style={{ textAlign: "center" }}>
                          <Button
                            variant={
                              formatVariantColor(
                                filterWidgetList(item?.children || [], 4)[0]?.style)?.variant
                            }
                            color={
                              formatVariantColor(
                                filterWidgetList(item?.children || [], 4)[0]?.style)?.color}
                            onClick={() => {filterWidgetList(item?.children || [], 4)[0]?.linkUrl === "os"
                            ? skipOS(authActions.isAuthenticated)
                            : navigate(filterWidgetList(item?.children || [], 4)[0]?.linkUrl)}}
                          >
                            {filterWidgetList(item?.children || [], 4)[0]?.value}
                          </Button>
                        </div>
                      )}
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
        </Box>
      )}
      {/* 为什么选择iTeam end */}
    </>
  );
}
