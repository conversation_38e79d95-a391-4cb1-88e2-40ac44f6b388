echo off
set filename=%date:~0,4%%date:~5,2%%date:~8,2%%time:~0,2%%time:~3,2%
set "filename=%filename: =0%"
set REACT_APP_VERSION=%filename%

@REM D环境

echo "D build start" 
call npm run build:dev
echo "D build finish"

echo "delete portal.war start"
del portal.war
echo "delete finish"

echo "tar start"
@REM tar cvf portal.war -C ROOT-DEV .
jar -cvfM portal.war -C ROOT-DEV .
echo "tar finish"

set winpath=%cd%

echo "start Upload files to TMS DEV server"
"E:\software\WinSCP\WinSCP.com" ^
  /log="E:\temp\WinSCP\%filename%.log" ^
  /ini=nul ^
  /command ^
  "open sftp://tomcat:LVRtQ%%3B.2S%%5E%%267YTCNBgzb@*************/ -hostkey=""ssh-ed25519 255 4L+rEb59cMiwhqi8XxjCaISZ4S8UMNHUT4a38WDJV1E""" ^
  "lcd %winpath%" ^
  "cd /uploads/tomcat-webapps" ^
  "put portal.war" ^
  "exit"

echo ***
echo ***
echo ***
echo upload success!

pause
 


