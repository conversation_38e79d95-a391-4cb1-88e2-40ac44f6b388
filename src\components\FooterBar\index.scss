.FooterBar {}

// 页脚简介
.footerBrief {
    p {
        margin-bottom: 20px;
    }
    a {
        margin-top: 10px;
        color: #666;
    }
}

// 隐私政策
.privacy-policy {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;
    background-color: #f1f7fb;
}

// 备案信息
.beian-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 30px;
    .beian-icon {
        width: 20px;
        height: 20px;
        background: url('../../assets/images/beian.png') no-repeat;
        background-size: 100%;
    }
    .beian-link {
        font-size: 14px;
        margin-left: 2px;
        color: #000;
        text-decoration: none;
    }
}