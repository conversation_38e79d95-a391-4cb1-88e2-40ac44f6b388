import React, { useImperativeHandle, useRef, useState, useEffect } from 'react';
import ReactPlayer from 'react-player';

function _VideoJS({url,playing=false,onPlay,onEnded,onPause,...props},ref) {
  const videoRef = useRef();
  const [_playing,setPlaying] = useState(playing);

  useImperativeHandle(ref, () => ({
    pause: pause,
    showPreview: showPreview
  }));

  const pause = () => {
    let orginalElem = videoRef.current.getInternalPlayer()
    if(orginalElem && !orginalElem?.paused){
      orginalElem?.pause()
    }
  }

  const showPreview = () => {
    try {
      videoRef.current?.showPreview()
    } catch (error) {
    }
  }

  return <ReactPlayer 
    ref={videoRef} 
    width="100%" 
    height="100%" 
    // light={""}
    url={url} 
    playing={false}
    onPlay={onPlay} 
    onEnded={onEnded} 
    onPause={onPause}
    {...props}/>
}

const VideoJS = React.forwardRef(_VideoJS)

function VideoJsWrap({setVideoRef,widgetId,isActive,...props}){
  const ref = useRef(null);

  useEffect(()=>{
    if(!isActive){
      ref.current?.pause();
      ref.current?.showPreview();
    }
  },[isActive])

  useEffect(()=>{
    setVideoRef(widgetId,ref);
  },[])

  return <VideoJS ref={ref} {...props} />
}

export {
  VideoJS as default,
  VideoJsWrap
}