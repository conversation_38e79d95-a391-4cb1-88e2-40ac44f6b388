
// // 获取文件className
// export function getFileClassName(item) {
//   const extension = item.name.split(".").pop();
//   const mime = item.type;
//   return item.isFolder ? "folder" : getFileType(extension, mime);
// }

// // 获取文件类型
// export function getFileType(extension, mime = "other") {
//   switch (extension) {
//     case "jpg":
//     case "jpeg":
//     case "gif":
//     case "png":
//     case "bmp":
//     case "tiff":
//     case "pcx":
//     case "svg":
//     case "ico":
//       return "image";
//     case "avi":
//     case "mpg":
//     case "mpeg":
//     case "rm":
//     case "move":
//     case "mov":
//     case "mkv":
//     case "flv":
//     case "f4v":
//     case "mp4":
//     case "3gp":
//     case "wmv":
//     case "webm":
//     case "vob":
//       return "video";
//     case "rar":
//     case "zip":
//     case "tar":
//     case "tgz":
//     case "arj":
//     case "gzip":
//     case "bzip2":
//     case "7z":
//     case "ace":
//     case "apk":
//     case "deb":
//     case "zipx":
//     case "cab":
//     case "tar-gz":
//     case "rpm":
//     case "xar":
//       return "archive";
//     case "xlr":
//     case "xls":
//     case "xlsm":
//     case "xlsx":
//     case "ods":
//     case "csv":
//     case "tsv":
//       return "table";
//     case "doc":
//     case "docx":
//     case "docm":
//     case "dot":
//     case "dotx":
//     case "odt":
//     case "wpd":
//     case "wps":
//     case "pages":
//       return "document";
//     case "wav":
//     case "aiff":
//     case "au":
//     case "mp3":
//     case "aac":
//     case "wma":
//     case "ogg":
//     case "flac":
//     case "ape":
//     case "wv":
//     case "m4a":
//     case "mid":
//     case "midi":
//       return "audio";
//     case "pot":
//     case "potm":
//     case "potx":
//     case "pps":
//     case "ppsm":
//     case "ppsx":
//     case "ppt":
//     case "pptx":
//     case "pptm":
//     case "odp":
//       return "presentation";
//     case "html":
//     case "htm":
//     case "eml":
//       return "web";
//     case "exe":
//       return "application";
//     case "dmg":
//       return "apple";
//     case "pdf":
//     case "ps":
//     case "eps":
//       return "pdf";
//     case "psd":
//       return "psd";
//     case "txt":
//     case "djvu":
//     case "nfo":
//     case "xml":
//       return "text";
//     default:
//       const type = mime.split("/")[0];
//       switch (type) {
//         case "folder":
//           return "folder";
//         case "image":
//           return "image";
//         case "audio":
//           return "audio";
//         case "video":
//           return "video";
//         default:
//           return "other";
//       }
//   }
// }

// 下载文件
export function download(url, name) {
  const a_link = document.createElement("a");  // 生成一个a链接
  // 将链接地址字符内容转变成blob地址
  a_link.href = url;// URL.createObjectURL(blob);
  console.log(a_link.href);
  a_link.download = name; //下载的文件的名字
  document.body.appendChild(a_link);
  a_link.click();
  a_link.remove();

 /*  const link = document.createElement('a');
  link.setAttribute('download', name);
  link.setAttribute('href', url);
  Object.assign(link.style, {
    position: 'absolute',
    top: 0,
    opacity: 0,
  });
  document.body.appendChild(link);
  link.click();
  link.remove(); */
}