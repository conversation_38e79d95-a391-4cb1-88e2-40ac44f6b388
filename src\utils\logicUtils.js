/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2023-05-10 14:03:43
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2023-05-10 14:03:52
 * @Description: 请填写文件内容描述！
 */

// 关键词高亮
export function getSearchValueLabel(searchValue, nodeData) {
  // 有搜索条件时 
  if (!!searchValue) {
   let strTitle = nodeData.label || ""; // 避免存在为null的node节点
   let index = strTitle.toLowerCase().indexOf(searchValue.toLowerCase());
   let beforeStr = strTitle.substring(0, index);
   let _searchValue = strTitle.substring(index, index + searchValue.length);
   let afterStr = strTitle.slice(index + searchValue.length);
   // 高亮颜色
   let label = index > -1 ? (
     <span>
       {beforeStr}
       <span className="color-orange">{_searchValue}</span> 
       {afterStr}
     </span>
   ) : (<span>{strTitle}</span>);
   return label;
 } 
 // 无搜索条件时
 return nodeData.label;
}