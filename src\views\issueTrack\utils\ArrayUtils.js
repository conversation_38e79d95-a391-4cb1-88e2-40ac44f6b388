import * as _ from 'lodash';
import { IssueTypeBugIcon, IssueTypeStoryIcon, IssueTypeChangeIcon, IssueTypeEnhanceIcon, IssueTypeNewDemandIcon, IssueTypeTaskIcon } from '@/components/IconUtil';
/**
 * 匹配407列表数据,获取propValue
 * https://confluence.ficent.com/pages/viewpage.action?pageId=78370031
 * @param list 数据
 * @param _id  selectionId
 * @param type propType
 */
export function getPropValueByIdType(list, _id, type) {
  return ((list || []).find((item) => {
    return item.selectionId == _id && item.propType == type;
  }) || { propValue: "" }).propValue
}
/**
 * 匹配407列表数据,获取图标iconValue
 * @param list 数据
 * @param _id  selectionId
 * @param type propType
 */
export function getIconValueByIdType(list, _id, type) {
  return ((list || []).find((item) => {
    return item.selectionId == _id && item.propType == type;
  }) || { iconValue: "" }).iconValue
}
/**
 * 匹配407列表数据,获取list
 * @param list 数据
 * @param code selectionId
 */
export function getCodeValueListByCode(list, code) {
  return ((list || []).filter((item) => {
    return item.selectionId == code;
  }))
}
/**
 * 匹配202列表数据,获取userName
 * @param list 数据
 * @param _id  userId
 */
export function getUserNameById(list, _id) {
  let _name = (list || []).find(_user => _user.userId == _id)?.userName || "";
  return _name;
}
/**
 * 匹配202列表数据,获取头像
 * @param list 数据
 * @param _id  userId
 */
export function getAvatarById(list, _id) {
  return (list.find((item) => {
    return item.userId == _id
  }) || { userName: "" }).avatar
}

// 格式化svg
export function formatSvg(svgUrl) {
  return <span className="issueSvg" dangerouslySetInnerHTML={{ __html: svgUrl }} />;
}
/**
 * 获取问题类型图标
 * @param data 数据
 */
// 获取图标
export function getIcon(data) {
  switch (+data) {
    case 2:
      return <IssueTypeNewDemandIcon className="issue-list-item-type" />
    case 3:
      return <IssueTypeEnhanceIcon className="issue-list-item-type" />
    case 4:
      return <IssueTypeStoryIcon className="issue-list-item-type" />
    case 5:
      return <IssueTypeChangeIcon className="issue-list-item-type" />
    case 6:
      return <IssueTypeTaskIcon className="issue-list-item-type" />
    case 1:
    default:
      return <IssueTypeBugIcon className="issue-list-item-type" />
  }
}

/**
 * JS 计算两个时间间隔多久（时分秒）
 * @param startTime "2022-07-18 14:40:52"
 * @param endTime "2022-07-18 10:55:37"
 * @return 3时45分15秒
 */
export function twoTimeInterval(startTime, endTime) {
  // 开始时间
  let d1 = startTime.replace(/\-/g, "/");
  let date1 = new Date(d1);

  // 结束时间
  let d2 = endTime.replace(/\-/g, "/");
  let date2 = new Date(d2);

  // 时间相差秒数
  let dateDiff = date2.getTime() - date1.getTime();

  // 计算出相差天数
  let days = Math.floor(dateDiff / (24 * 3600 * 1000));

  // 计算出小时数
  let residue1 = dateDiff % (24 * 3600 * 1000); // 计算天数后剩余的毫秒数
  let hours = Math.floor(residue1 / (3600 * 1000));

  // 计算相差分钟数
  let residue2 = residue1 % (3600 * 1000); // 计算小时数后剩余的毫秒数
  let minutes = Math.floor(residue2 / (60 * 1000));

  // 计算相差秒数
  let residue3 = residue2 % (60 * 1000); // 计算分钟数后剩余的毫秒数
  let seconds = Math.round(residue3 / 1000);

  let returnVal =
    ((days == 0) ? "" : days + "天") +
    ((hours == 0) ? "" : hours + "时") +
    ((minutes == 0) ? "" : minutes + "分") +
    ((seconds == 0) ? "" : seconds + "秒");

  return returnVal;

}


// 截取视图路径
export const getViewModePath = (pathname) => {
  try {
    const view_list = pathname.includes('/list')
    const view_kanban = pathname.includes('/kanban')
    // 是否为长列表
    if (view_list) {
      return 'list'
    }
    // 是否为看板
    if (view_kanban) {
      return 'kanban'
    }
    // 返回短列表
    return ''
  } catch (e) {
    console.warn(e);
  }
}

//比较两个对象是否相等
export const compareObj = (obj, newObj) => {
  return _.isEqual(obj, newObj);
};


/**
 * 传入一个对象，删除对象里面值为null和undefined或者""的这些无效的字段
 * */
export const deleteInvalid = (obj) => {
  Object.keys(obj).forEach((item) => {
    if ((!obj[item] && obj[item] != 0) || obj[item] == "") {
      delete obj[item];
    }
  });
  return obj;
};
