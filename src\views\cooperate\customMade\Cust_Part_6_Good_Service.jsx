import React, { useEffect, useState } from "react";
import { Image } from "antd";
import {
  Box,
  Typography,
  Stack,
} from "@mui/material";
import "./Cust_Part_6_Good_Service.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../utils/commonUtils";

export default function Cust_Part_6_Good_Service({data}) {
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  }, [data?.widgetList]);
  return (
    <>
      {/* 保障和运营-start */}
      <Box className="guarantee-operation" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography variant="h5" sx={{ fontSize: "34px" }}>{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
        {!!filterWidgetList(moduleData, 2)[0]?.value && <Typography sx={{ mt: "10px", color: "#666" }} variant="body2">
          {filterWidgetList(moduleData, 2)[0]?.value}
        </Typography>}
        <Box className="guarantee-operation-stack">
          <Stack
          sx={{ flexWrap: "wrap", mt: "40px" }}
          direction={{ xs: "column", sm: "row" }}
          >
            {filterWidgetList(moduleData, 6).map((item, index) => (
              <Box className="guarantee-operation-stack-li" key={item.widgetId}>
                <Image preview={false} width={50} height={50} src={filterWidgetList(item.children, 3)[0]?.value}/>
                <Box className="guarantee-operation-stack-li-title">
                  <Typography className="fontsize-16" variant="h6">{filterWidgetList(item.children, 1)[0]?.value}</Typography>
                  <Typography className="fontsize-12" sx={{ color: "#999" }} variant="body2">
                    {filterWidgetList(item.children, 2)[0]?.value}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Stack>
        </Box>
      </Box>
      {/* 保障和运营-end */}
    </>
  );
}
