.home {
    height: 100vh;
    overflow: auto;
    overflow-x: hidden;
}

.home::before {
    content: '';
}

.home-TopMenuBar-box {
    position: fixed;
    top: 57px;
    // left: 50%;
    // transform: translate(-50%, 0);
    margin: auto;
    padding: 0 20px;
    width: 100vw;
    left: 0;
    box-sizing: border-box;
    z-index: 1;
    // box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
}

.start_scrolling {
    top: 0 !important;
    box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
    background-color: white;
    transition: all 0.3s 0s;
}
.scroll_to_top {
    top: 57px !important;
    box-shadow: none !important;
    background-color: transparent !important;
    .TMS-AppBar {
        .MuiContainer-root {
            .MuiToolbar-root {
                .MuiBox-root {
                    .MuiGrid-root > .MuiGrid-root:nth-of-type(n+6){
                        .MuiButtonBase-root {
                            color: white !important;
                            .PortalLink  {
                                color: white !important
                            }
                        }
                    }
                    .tms-operating-system {
                        color: white !important;
                    }
                }
            }
        }
    }
}

.home-head__bubble {
    position: absolute;
    top: -360px;
    left: 110px;
    width: 1468px;
    border-radius: 50%;
    background: linear-gradient(180deg,#1876d2 0,#03a9f4 99.18%);
    z-index: -1;
}

.home-head__bubble::before {
    content: '';
    padding-top: 61.4441416894%;
    display: block;
}

// 查看更多产品
.see-more-products-btn {
    padding: 16px 0 !important;
    width: 100%;
    text-align: center;
    background-color: #f1f7fb !important
}

// @media screen and (min-width:1200px){
//     .home {
//         overflow-x: hidden;
//     }

//     .home-TopMenuBar-box {
//         position: fixed;
//     }

//     .home-head__bubble {
//         position: absolute;
//         top: -360px;
//         right: -800px;
//         width: 1468px;
//         border-radius: 50%;
//         background: linear-gradient(180deg,#1876d2 0,#03a9f4 99.18%);
//         z-index: -1;
//     }
    
//     .home-head__bubble::before {
//         content: '';
//         padding-top: 61.4441416894%;
//         display: block;
//     }
// }
