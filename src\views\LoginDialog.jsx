import {Button,Layout,Modal,Form,Input,Checkbox, Image} from "antd";
import { QqOutlined, WechatOutlined } from "@ant-design/icons";
import React, { useEffect, useState, useRef } from "react";
import "./LoginDialog.scss";
import { useLocation } from "react-router-dom";
import * as toolUtil from "../utils/toolUtil";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {team_619_user_info_query, team_628_get_login_register_method_query} from "../api/query/query"
import {globalUtil} from "../utils/globalUtil";
import qs from "qs"
import * as http from "../api/http_login";
import { globalEventBus } from "../utils/eventBus";
import HelpTips from "../components/HelpTips"; // 帮助文档提示
import TLoading from "@/components/TLoading";

let num = 1;
const { Search } = Input;

export default function LoginDialog() {
  const [form] = Form.useForm();
  const [time, setTime] = useState(60);
  const location = useLocation();
  const queryClient = useQueryClient();
  const [btnDisabled, setBtnDisabled] = useState(false);
  const [titleStyle, setTitleStyle] = useState(false);
  const [rememberChecked, setRememberChecked] = useState(true)
  const [loadingFlag, setLoadingFlag] = useState(false);
  const [loginOpen, setLoginOpen] = useState(false);
  const [loginArgs, setLoginArgs] = useState({});

  const { data: {loginMethod}={loginMethod:""}, refetch: team618Refetch } = useQuery({
    ...team_628_get_login_register_method_query(),
    enabled: false
  })

  let timer;

  useEffect(() => {
    globalEventBus.on("openLoginModal", openLoginModalEvent);
    return () =>{
      globalEventBus.off("openLoginModal",openLoginModalEvent)
    }
  }, []);

  const openLoginModalEvent = (target, args) => {
    setLoginOpen(true)
    setLoginArgs({...args})
    loading()
  }

  useEffect(() => {
    if(location.search) {
      try {
        let {demoName,demoPassword} = qs.parse(location.search?.slice(1)??"") || {};
        if(!!demoName && !!demoPassword) {
          userLogin({mobileOrEmail: demoName, password: demoPassword});
        }
      } catch (error) {
        
      }
    }
  },[location.search])

  // 加载登录页面数据
  const loading = () => {
    team618Refetch()
    qqLogin()
    try {
      let userLoginInfo = localStorage.getItem("userLoginInfo");
      if (userLoginInfo) {
        form.setFieldsValue({
          ...JSON.parse(userLoginInfo),
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const qqLogin = () => {
    var obj_ = new window.QC.Login({
      btnId:"qqLoginBtn",	//插入按钮的节点id
      size: 'C_S',
    });
  }

  // 获取QQ登录参数
  const qqAuthLogin = () => {
    let url_ = `${window.location.origin}/tms_team/api/qq_auth_login`
    window.location.href = url_
  }

  // 返回设备类型
  const getDeviceType = () => {
    if(toolUtil.isMobile()) {
      if(toolUtil.isAndroid()) {
        console.log('Android');
        return 'Android'
      } else {
        if(toolUtil.isIOS()) {
          console.log('iOS');
          return 'iOS'
        } else {
          console.log('未知设备');
          return null
        }
      }
    } else {
      console.log('PC端');
      return 'PC'
    }
  }

  // 登录
  const userLogin = (value) => {
    setLoadingFlag(true)
    let request = {
      loginType: titleStyle ? 2 : 1,
      ...value,
      loginBrowser: toolUtil.getExplorerInfo().type, // 浏览器类型
      deviceType: getDeviceType(), // 设备类型(PC、Android、iOS)
      deviceOs: toolUtil.getDevice().os, // 设备系统类型
      deviceVersion: toolUtil.getDevice().dev, // 设备系统版本号
      browserVersion: toolUtil.getExplorerInfo().version, // 浏览器版本号
      osVersion: process.env.REACT_APP_VERSION, // 系统版本号
      userAgent: navigator.userAgent.toLowerCase(), // 设备及浏览器完整信息
    };
    http.team_618_user_login(request).then((result) => {
        if (result.resultCode == 200) {
          num = 40
          if (rememberChecked && !titleStyle) {
            localStorage.setItem("userLoginInfo", JSON.stringify(request));
          } else {
            localStorage.removeItem("userLoginInfo")
          }
          queryClient.fetchQuery(team_619_user_info_query())
            .then((res) => {
              setLoginOpen(false)
              globalUtil.success('登录成功',"",1)
              queryClient.invalidateQueries();
              if(loginArgs.type === "os") {
                window.open(`${process.env.REACT_APP_TMS_URL}/`);
              }
              setLoadingFlag(false)
            })
        } else {
          setLoadingFlag(false)
        }
      })
      .catch((err) => {
        globalUtil.error("登录失败");
        setLoadingFlag(false)
      });
  };

  // 验证码定时器
  useEffect(()=>{
    if(time < 60 && time){
      if(!timer)timer = setInterval(() => setTime((pre) => pre-1), 1000);
    }else{
      if(timer){
        clearInterval(timer);
        timer = null;
      }
      setBtnDisabled(false);
      setTime(60);
    }
    return ()=>{
      if(timer){
        clearInterval(timer);
        timer = null;
      }
    }
  },[time])

  // 获取验证码
  const getIdentitycode = (value, event) => {
    let formMobileOrEmail = form.getFieldValue("mobileOrEmail");
    if (btnDisabled) {
      return;
    }
    if (formMobileOrEmail == null) {
      globalUtil.warning("请输入手机号/邮箱！");
      return;
    }
    let request = {
      mobileOrEmail: formMobileOrEmail,
      opType: 2
    };
    http.team_521_get_security_code(request).then((result) => {
      if (result.resultCode == 200) {
        setTime(59)
        setBtnDisabled(true);
        globalUtil.success("发送成功");
      } 
    });
  };

  // 登录方式切换
  const titleStyleChange = (type) => {
    setTitleStyle(type);
    if(!type && localStorage.getItem("userLoginInfo")) {
      form.setFieldsValue({
        ...JSON.parse(localStorage.getItem("userLoginInfo")),
      });
    } else {
      form.setFieldsValue({
        password: null,
      });
    }
  };

  // 更新记住密码checkbox
  const rememberFlagChange = (e) => {
    setRememberChecked(!rememberChecked)
  }

  const getLoginMethodTitle = (method) => {
    let title = ''
    if(method.includes('1') && method.includes('2')) {
      title = '手机号/邮箱'
    } else {
      if(method.includes('1')) {
        title = '手机号'
      } else if(method.includes('2')) {
        title = '邮箱'
      }
    }
    return title
  }

  // 重置密码
  const resetPassword = () => {
    let url_ = `${process.env.REACT_APP_TMS_URL}/forgotuserpassword`
    window.open(url_)
  }

  // 立即注册
  const register = () => {
    let url_ = `${process.env.REACT_APP_TMS_URL}/register`
    window.open(url_)
  }

  const turnRedirect = () => {
    queryClient.refetchQueries(team_619_user_info_query())        
    // result.userInfo 用户信息
    setLoginOpen(false);
    globalUtil.success('登录成功',"",1)
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }
  
  let is_visible_wxlogin = loginMethod.includes("3");
  let is_visible_qqlogin = loginMethod.includes("4");
  let is_visible_leftbox = is_visible_wxlogin || is_visible_qqlogin;
  return (
    <Modal 
      className="login-Dialog" 
      centered
      maskClosable={false}
      footer={null}
      open={loginOpen} 
      onCancel={() => setLoginOpen(false)}
    >
      <Layout className="Login">
        <div className="Login-box">
          <div 
          className="Login-leftContent" 
          style={{ 
            display: is_visible_leftbox ? 'flex': 'none'
          }}>
            <div 
            className="Login-leftContent-wechatbox"
            style={{ 
              visibility: is_visible_wxlogin? 'visible' : 'hidden' 
            }}>
              <div className="Login-leftContent-wechatbox-title">
                <WechatOutlined className="wechatIcon"/>
                微信扫码&nbsp;登录/注册
              </div>
              <div style={{ marginTop: 10 }}>打开微信APP&nbsp;右上角“扫一扫”</div>
            </div>
            {is_visible_wxlogin && 
              <Wxlogin 
              loginMethod={loginMethod} 
              turnRedirect={turnRedirect} 
             />}
            <div 
              className="Login-leftContent-Qqbox"
              style={{ 
                visibility: is_visible_qqlogin ? 'visible' : 'hidden' 
              }}>
              <div className="Qqline"/>
              {/* <QqOutlined className="QqIcon"/> */}
              <a href={`${window.location.origin}/tms_team/api/qq_auth_login`} target="_blank" onClick={qqAuthLogin}>
                <QqOutlined title="使用QQ登录" className="QqIcon"/>
                <span style={{ display: 'none' }} title="使用QQ登录" id="qqLoginBtn" className="qqLoginBtn"></span>
              </a>

              <div className="Qqline"/>
            </div>
          </div>
          <div 
          className="Login-rightContent" 
          style={{
            width: is_visible_leftbox ? 400: 500
          }}>
            <div className="Login-rightContent-box">
              <div className="Login-rightContent-box-title">
                <a
                  style={{ color: titleStyle ? "#282828" : "#40a9ff", fontWeight: titleStyle ? 500 : 600 }}
                  onClick={() => titleStyleChange(false)}
                >
                  账密登录
                </a>
                <a
                  style={{ color: titleStyle ? "#40a9ff" : "#282828", fontWeight: titleStyle ? 600 : 500 }}
                  onClick={() => titleStyleChange(true)}
                >
                  短信登录
                </a>
              </div>
                <Form
                  className="Login-rightContent-box-form"
                  name="Login"
                  form={form}
                  onFinish={userLogin}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                  autoComplete="off"
                >
                  <Form.Item
                    name="mobileOrEmail"
                    rules={[
                      {
                        required: true,
                        message: `${getLoginMethodTitle(loginMethod)}不能为空！`,
                      },
                    ]}
                  >
                    <Input
                      className="Login-rightContent-box-form-inp"
                      allowClear
                      autoComplete="off"
                      placeholder={`请输入${getLoginMethodTitle(loginMethod)}`}
                    />
                  </Form.Item>

                  <Form.Item
                    name="password"
                    rules={[
                      {
                        required: true,
                        message: titleStyle?"验证码不能为空！":"密码不能为空！",
                      },
                    ]}
                    getValueFromEvent={(event) => {
                      return event.target.value.replace(/[\u4e00-\u9fa5]/ig,'','')
                    }}
                  >
                    {titleStyle ? (
                      <Search
                        className="Login-rightContent-box-form-search"
                        placeholder="请输入验证码"
                        allowClear
                        enterButton={!btnDisabled ? "获取验证码" : `${time}s后重发`}
                        onSearch={getIdentitycode}
                      />
                    ) : (
                      <Input.Password
                        className="Login-rightContent-box-form-inp"
                        placeholder="请输入8-16位字母或数字组合密码"
                        onChange={e => (e.target.value).replace(/[^\d|chun]/g,'')}
                        allowClear
                      />
                    )}
                  </Form.Item>

                  <Form.Item style={{ marginBottom: 10 }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      style={{
                        marginTop: 20,
                        width: 358,
                        height: 50,
                        borderRadius: 50,
                      }}
                      loading={loadingFlag}
                    >
                      {titleStyle ? '登录/注册' : '登录'}
                    </Button>
                  </Form.Item>
                </Form>
                {/* <div className="Login-rightContent-box-a" style={{ opacity: !titleStyle?1:0 }}> */}
                <div className="Login-rightContent-box-a">
                  <Checkbox className="fontsize-12" defaultChecked={rememberChecked} onChange={rememberFlagChange}>记住密码</Checkbox>
                  <div>
                    <a className="fontsize-12" onClick={resetPassword}>重置密码</a>
                    <span style={{ margin: '0 10px', color: '#666' }}>|</span>
                    <a className="fontsize-12" onClick={register}>立即注册</a>
                  </div>
                </div>
                <div style={{ marginTop: 30, width: 358, textAlign: 'left', fontSize: 12 }}>
                  <span style={{ color: '#999' }}>
                    未注册手机验证后自动登录，注册即代表同意
                    <a>
                      <HelpTips 
                        type="link" 
                        isShowText={false}
                        title={'iTeam用户隐私保护协议'}
                        leftIcon={"《"}
                        rightIcon={"》"} 
                        manualNo={10010001}
                      />
                    </a>
                    <a>
                      <HelpTips 
                        type="link" 
                        isShowText={false}
                        title={'云团队知识用户付费协议'}
                        leftIcon={"《"}
                        rightIcon={"》"} 
                        manualNo={10010002}
                      />
                    </a>
                  </span>
                </div>
              </div>
          </div>
        </div>
      </Layout>
    </Modal>
  );
}

// 微信二维码登录
function Wxlogin({turnRedirect, /* initializeAppData */}) {
  // const dispatch = useDispatch();
  // const navigate = useNavigate();
  const timeoutRef = useRef(null)
  const [qrCodeExpired, setQrCodeExpired] = useState(false);
  const [team650Enabled, setTeam650Enabled] = useState(false);
  const { data: team649Result, dataUpdatedAt: team649UpdatedAt, isLoading: team649Loading, refetch: team649Refetch } = useQuery({
    queryFn: () => http.team_649_mp_create_qrcode({"sceneStr": "test"}),
    queryKey: ['team_649_mp_create_qrcode']
  }) 

  const {data: team650Result, dataUpdatedAt: team650UpdatedAt, isLoading: team650Loading} = useQuery({
    queryFn: () => http.team_650_mp_get_wx_notice_state({ticket: team649Result?.ticket}),
    queryKey: ['team_650_mp_get_wx_notice_state'],
    refetchInterval: 3000,
    enabled: team650Enabled,
  })

  useEffect(() => {
    if(team649Result?.resultCode === 200) {
      setTeam650Enabled(true)
      setQrCodeExpired(false)
      timeoutRef.current = setTimeout(() => {
        setQrCodeExpired(true);
        timeoutRef.current = null;
        setTeam650Enabled(false);
      }, team649Result.expireSeconds * 1000)
    }

    return () => {
      if(timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null;
        setTeam650Enabled(false);
        setQrCodeExpired(false);
      }
    }
  }, [team649UpdatedAt])


  useEffect(() => {
    if(team650Result?.resultCode === 200) {
      turnRedirect()
      // initializeAppData();
    }
  },[team650UpdatedAt])

  const refreshQrCode = () => {
    team649Refetch();
  }

  // let wxTimer;
  // useEffect(() => {
  //   sysGetWxLoginParam()
  // },[]);

  // const opTMSwxLogin = (wxLoginParams) => {
  //   var href = "data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7d2lkdGg6IDEzOHB4O30KLmltcG93ZXJCb3ggLnRpdGxlIHtkaXNwbGF5OiBub25lO30KLmltcG93ZXJCb3ggLmluZm8ge3dpZHRoOiAxMzhweDt9Ci5pbXBvd2VyQm94IC53cnBfY29kZSAucXJjb2Rle3dpZHRoOiAxMzhweDttYXJnaW4tdG9wOiAwO2JvcmRlcjogbm9uZTt9Ci5zdGF0dXNfaWNvbiB7ZGlzcGxheTogbm9uZX0KLmltcG93ZXJCb3ggLnN0YXR1cyB7ZGlzcGxheTogbm9uZX0uaW1wb3dlckJveCAuaW5mb3tkaXNwbGF5OiBub25lfQouaW1wb3dlckJveHt3aWR0aDogMTM4cHg7fQo="
  //   var obj = new window.WxLogin({
  //     self_redirect:true,
  //     id:"wxLoginUrl",
  //     appid: wxLoginParams.appId, 
  //     scope: wxLoginParams.scope, 
  //     redirect_uri: wxLoginParams.redirectUri,
  //     state: wxLoginParams.state,
  //     style: "",
  //     href: href,
  //     fast_login: 0
  //   })
  // }

  // // 获取微信登录参数
  // const sysGetWxLoginParam = () => {
  //   http.team_614_get_wx_login_param().then((res) => {
  //     if (res.resultCode == 200) {
  //       num = 1
  //       opTMSwxLogin(res.wxLoginParams)
  //       sysGetWxLoginState(res.wxLoginParams.clientId)
  //     }
  //   });
  // }

  // // 获取微信登录扫码状态
  // const sysGetWxLoginState = (clientId) => {
  //   http.team_616_get_wx_login_state({clientId}).then((res) => {
  //     if (res.resultCode == 200) {
  //       turnRedirect()
  //       initializeAppData();
  //     } else if(res.resultCode == 10012) {
  //       globalUtil.warning(res.resultMessage)
  //       dispatch(setLoginParam({...res, clientId: clientId}))
  //       clearTimeout(wxTimer)
  //       wxTimer = null
  //       navigate(`/bindAccount/${res.wxOpenid}/wx`)
  //     } else {
  //       if(num >= 40) {
  //         setQrCodeExpired(true)
  //         console.log('二维码过期请刷新');
  //         return
  //       } else {
  //         wxTimer = setTimeout(() => sysGetWxLoginState(clientId), 3000)
  //         num++
  //       }
  //     }
  //   });
  // }

  // // 刷新二维码
  // const refreshQrCode = () => {
  //   num = 1;
  //   setQrCodeExpired(false)
  //   sysGetWxLoginParam()
  // }
  
  return (
    <div 
      className={(qrCodeExpired || team649Loading) ?"erweimaExpired":"Login-leftContent-erweima"}
    >
      <div className="erweima_Image">
        {qrCodeExpired && !team649Loading && <div className="erweima_Image-refresh">
          <Button style={{ fontSize: 12, borderRadius: 4 }} type="primary" size="small" onClick={refreshQrCode}>
            二维码已过期
            <span className="fontsize-12 iconfont shuaxin1" style={{ lineHeight: '22px' }}/>
          </Button>
        </div>}
        { team649Loading && <div className="erweima_Image-loading"><TLoading /> </div>}
        <div id="wxLoginUrl" style={{ height: 138, overflow: 'hidden' }}>
          {!!team649Result?.qrCodeUrl && <Image src={team649Result?.qrCodeUrl} preview={false} style={{height: 138}}/>}
        </div>
      </div>
      <div className="tips_Image"></div>
    </div>
  )
}