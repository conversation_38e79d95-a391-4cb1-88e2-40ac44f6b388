import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import * as httpCommon from "@/api/http";
import TCommentList, { TCommentEditorHeader } from "./CustomComment";
import { useQuery } from "@tanstack/react-query";
import { team_504_get_comment_list_query } from "@/api/query/query"
import { globalEventBus } from "@/utils/eventBus";
import { useAuthContext } from "@/context/AppAuthContextProvider";

/**
 * @description 评论 & 评论列表
 * @param {object} uploadParams 上传评论参数 { teamId: params.teamId, moduleName: "reports", objType: objType }
 * @param {object} uploadFileCallBack 上传文件回调
 */
export default function CommonComment({ uploadParams, uploadFileCallBack, totalCallBack, initData }) {

  const authActions = useAuthContext();

  // const [commentList, setCommentList] = useState([]);
  const {data: { commentList = [] } = { commentList:[] },refetch: team504Refresh,dataUpdatedAt} = useQuery({
    ...team_504_get_comment_list_query(uploadParams.teamId, uploadParams.nodeId),
    initialData: initData,
    staleTime: 1000
  })

  console.log("commentList", commentList);

  useEffect(()=>{
    totalCallBack && totalCallBack(commentList?.length)
  },[dataUpdatedAt])

  // 提交评论
  const onSaveComment = (args) => {
    if(!args.comment) return new Promise((resolve, reject)=> resolve(false));

    return new Promise((resolve, reject) => {
      let request = {
        teamId: uploadParams.teamId,
        nodeId: uploadParams.nodeId,
        objType: uploadParams.objType,
        comment: args.comment,
        commentType: 1
      }
      loadAddComment(request, resolve)
    })

  }

  // 对评论进行评论
  const onSaveCommentFromComment = (args) => {
    if(!args.comment) return new Promise((resolve, reject)=> resolve(false));

    return new Promise((resolve, reject) => {
      let request = {
        teamId: uploadParams.teamId,
        nodeId: uploadParams.nodeId,
        objType: uploadParams.objType,
        comment: args.comment,
        commentType: 1,
        parentId: args.parent.id
      }
      loadAddComment(request, resolve)
    })
  }

  // ajax 提交评论
  const loadAddComment = (request, resolve) => {
    httpCommon.team_509_add_comment(request).then(result => {
      if (result.resultCode == 200) {
        resolve && resolve(true)
        team504Refresh();
      } else {
        resolve && resolve(false);
      }
    })
  }

  // 对评论进行编辑
  const onEditCommentFromComment = (args) => {
    if(!args.comment) return new Promise((resolve, reject)=> resolve(false));
    return new Promise((resolve, reject) => {
      let request = {
        teamId: uploadParams.teamId,
        commentId: args.parent.id,
        bodyId: args.parent.bodyId,
        body: args.comment,
        nodeId: args.parent.nodeId,
      }
      loadEditComment(request, resolve)
    })
  }

  // ajax 编辑评论
  const loadEditComment = (request, resolve) => {
    httpCommon.team_525_edit_comment(request).then(result => {
      if (result.resultCode == 200) {
        resolve && resolve(true)
        team504Refresh();
      } else {
        resolve && resolve(false);
      }
    })
  }

  // 删除评论
  const delteComment = (args) => {
    let request = {
      teamId: uploadParams.teamId,
      ...args
    }
    httpCommon.team_524_delete_comment(request).then(result => {
      if (result.resultCode == 200) {
        team504Refresh();
      }
    })
      .catch((e) => {

      });
  }

  // 获取评论列表
  const getCommentList = (_commentList) => {
    return _commentList.map(item => {
      return {
        author: item.userName,
        avatar: item.avatar,
        content: item.body,
        datetime: item.updateDt,
        parentId: item.parentId,
        ...item
      }
    })
  }

  // 点赞、不喜欢、取消点赞
  const onLikeDisliskClick = (args) => {
    let request = {
      teamId: uploadParams.teamId,
      ...args
    }
    httpCommon.team_531_comment_social_op(request).then(result => {
      if (result.resultCode == 200) {
        team504Refresh()
      }
    })
  }

  //TODO: moduleName作用
  return (
    <>
      <TCommentEditorHeader onSaveComment={onSaveComment} uploadParams={uploadParams} uploadFileCallBack={uploadFileCallBack} onLikeDisliskClick={onLikeDisliskClick} />
      {commentList?.length > 0 &&
        <TCommentList
          uploadParams={uploadParams}
          dataSource={getCommentList(commentList)}
          commentNumber={commentList?.length}
          onSaveComment={onSaveCommentFromComment}
          onEditComment={onEditCommentFromComment}
          onDelComment={delteComment}
          uploadFileCallBack={uploadFileCallBack}
          onLikeDisliskClick={onLikeDisliskClick}
          onRefresh={()=> team504Refresh()}/>
      }
    </>
  )
}
