import React, { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Box,
  Typography,
  Link,
  Paper,
} from "@mui/material";
import { Image } from "antd";
import { team_1201_get_menu_list_query } from "../../api/query/query"
import "./index.scss";
import PortalLink from "../PortalLink";
import Icon from '@ant-design/icons';
import { ReactComponent as LogoFooter } from "../../assets/svg/logoFooter.svg";
import supportQrCode from '../../assets/images/support.png';
import iteamQrCode from '../../assets/images/iteam_qrcode.png';

// 页脚
export default function FooterBar() {
  const team1201Result = useQuery(team_1201_get_menu_list_query(6666))
  const [footMenuList, setFootMenuList] = useState([]);
  useEffect(() => {
    if(Array.isArray(team1201Result.data?.footMenuList)) {
      setFootMenuList([...team1201Result.data.footMenuList])
    }
  },[team1201Result.data?.footMenuList])

  const formatTreeData = (cur=[],arr=[]) => {
    // 生成根目录
    if(cur.length == 0){
      // 格式化数据格式
      arr = arr.map(item => {
        return {
          key: item.id,
          ...item
        }
      })
      cur = arr.filter(item => arr.every(itemx => itemx.id != item.parentId));
    }
    cur.forEach(item => {
      let childs = arr.filter(itemx => itemx.parentId == item.id);
      if(childs.length){
        let turnChilds = formatTreeData(childs,arr);
        item.children = turnChilds;
      }
    });
    return cur;
  }
  return (
    <>
      {footMenuList.length > 0 && <div className="FooterBar">
        {/* 页脚start */}
        <Box sx={{ width: "100%", backgroundColor: "white" }}>
          <Paper
            sx={{
              display: "flex",
              justifyContent: "space-between",
              position: "relative",
              margin: "auto",
              padding: "40px",
              maxWidth: 1360,
              boxShadow: "none",
              color: "#333",
              textAlign: "center",
              backgroundColor: "white",
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              backgroundPosition: "center",
            }}
          >
            <Box
              className="footerBrief"
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "start",
              }}>
              <Typography>
                <Icon component={LogoFooter}  style={{ fontSize: 140, color: '#333' }} />
              </Typography>
            </Box>

            {formatTreeData([], footMenuList).map((menu, index) => (
              <Box
                key={menu.key}
                className="footerBrief"
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "start",
                }}
              >
                <Typography className="fontsize-14" sx={{ fontWeight: "bold" }}>{menu.name}</Typography>
                {!!menu?.children && menu.children.map((item, index) => (
                  <PortalLink className="fontsize-14" key={item.key} to={item.menuUrl} name={item.name} state={{menuUrl: item.menuUrl}}/>
                ))}
              </Box>
            ))}
            <Box sx={{display: "flex"}}>
              <div style={{marginRight: 20}}>
                <Image width={150} src={supportQrCode} />
                {/* <div style={{marginTop: 2, fontSize: 14, textAlign: 'center'}}>iTeam客服</div> */}
              </div>
              <div>
                <Image width={150} src={iteamQrCode} />
                {/* <div style={{marginTop: 2, fontSize: 14, textAlign: 'center'}}>关注iTeam，使用操作系统</div> */}
              </div>
            </Box>
          </Paper>
        </Box>
        {/* 页脚end */}
        <TMSRecord/>
      </div>}
    </>
  );
}


// 备案信息
export function TMSRecord() {
  const team1201Result = useQuery(team_1201_get_menu_list_query(6666))
  const [copyrightMenuList, setCopyrightMenuList] = useState([]);
  useEffect(() => {
    if(Array.isArray(team1201Result.data?.copyrightMenuList)) {
      setCopyrightMenuList([...team1201Result.data.copyrightMenuList])
    }
  },[team1201Result.data?.copyrightMenuList])
  return (
    <Box className="privacy-policy">
      <Box>
        {copyrightMenuList.map((link, index) => (
          <span key={link.id}>
            <Link sx={{ color: '#333' }} className="fontsize-14" href={link.menuUrl} underline="hover">{link.name}</Link>
            {index < copyrightMenuList.length - 1 && <span style={{ margin: '0 6px' }}>/</span>}
          </span>
        ))}
      </Box>
      {/* <Box className="fontsize-14">copyright@2023&nbsp;All&nbsp;rights&nbsp;reserved.</Box> */}
      <Box className="beian-box">
        <div className="beian-icon"/>
        <Link className="beian-link" href="https://beian.miit.gov.cn/" target="_blank">{process.env.REACT_APP_FILING}</Link>
      </Box>
    </Box>
  )
}