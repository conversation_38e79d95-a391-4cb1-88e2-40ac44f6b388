import React from "react";
import { Pagination, Layout, Row, Col, Space, Tag } from 'antd';
import { EyeOutlined, LikeOutlined } from "@ant-design/icons";
import { Box } from "@mui/material";
import TopMenuBar from "../../components/TopMenuBar";
import FooterBar from "../../components/FooterBar";
import "./index.scss";

export default function HelpVideo(){

  return <Layout className="helpVideo">
    <Box sx={{ height: 90 }} />
    <Box sx={{ width: "100%", padding: "0 100px" }}>
      <Box className="helpVideo-TopMenuBar-box">
        <TopMenuBar />
      </Box>
    </Box>
    <Layout style={{display:"flex", alignItems:"center", flex: 1}}>
      <Layout style={{maxWidth: "1000px"}}>
        <div style={{padding: "20px", background:"#fff"}}>
          <div style={{display:"flex"}}>
            <div>分类：</div>
            <Space wrap style={{flex: 1, marginLeft: 20}}>
              <a>全部</a>
              <a>操作系统</a>
              <a>在线运行</a>
              <a>接口测试</a>
              <a>文档/文档库</a>
              <a>Excel</a>
              <a>甘特图/工时登记</a>
              <a>提醒事项/日程</a>
              <a>工作报告</a>
              <a>搜索/报表/仪表板/订阅</a>
              <a>考试/培训管理</a>
              <a>编程设计工具</a>
              <a>问题跟踪</a>
            </Space>
          </div>
          <div style={{display:"flex", marginTop: 20}}>
            <div>排序：</div>
            <Space wrap style={{flex: 1, marginLeft: 20}}>
              <a>最新上传</a>
              <a>浏览量</a>
            </Space>
          </div>
        </div>
        <Layout style={{padding: "20px 0"}}>
          <Row gutter={[0,16]}>
            {Array(20).fill(0).map((_, i) => <Col span={6}><VideoCard /></Col>)}
          </Row>
          <div style={{display: "flex", justifyContent:"center", margin: 20}}>
            <Pagination defaultCurrent={20} total={200} />
          </div>
        </Layout>
      </Layout>
    </Layout>
    <FooterBar/>
  </Layout>
}

export function VideoCard() {
  return <div style={{width: 230, background: "#fff",borderRadius: 2,overflow:"hidden"}}>
    <div>
      <img width={"100%"} alt="example" src="https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png"/>
    </div>
    <div style={{padding: "10px 20px"}}>
      <h3>TMS项目进度管理</h3>
      <Tag>甘特图</Tag>
    </div>
    <div style={{padding: "10px 20px", borderTop: "1px solid #0000000f"}}>
      <Space size={20}>
        <span><EyeOutlined/> 851</span>
        <span><LikeOutlined />127</span>
      </Space>
    </div>
  </div>
}