import React from "react";
import { <PERSON> } from "react-router-dom";
import "./index.scss";

/**
 * @description: 统一样式Link
 * @param {*} className 自定义类名
 * @param {*} to 相对路径
 * @param {*} name link名称
 * @return {*}
 */
export default function PortalLink({className, style, to, target, name, state}) {
  return <Link className={['PortalLink', className].join(' ')} style={style} to={to} target={target} state={state}>{name}</Link>
}
