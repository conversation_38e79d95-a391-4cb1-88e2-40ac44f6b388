import { globalEventBus } from "../utils/eventBus";
import { globalUtil } from "@/utils/globalUtil";
import { eNotLoginMsg } from "@/utils/toast";

// 跳转TMS操作系统
export const skipOS = (isLogin) => {
  if(isLogin) {
    window.open(`${process.env.REACT_APP_TMS_URL}/`);
  } else {
    globalEventBus.emit("openLoginModal", "",{type: "os"});
  }
}

// 校验是否登录
export const validateAuthActions = (authActions) =>{
  if(!!authActions.isAuthenticated) { 
    return true;
  }
  globalUtil.warning(eNotLoginMsg);
  globalEventBus.emit("openLoginModal", "",); //登录成功后页面会自动刷新
}

// 判断是否为数组
export const isArrayUtils = (arr) => Array.isArray(arr)

// 官网页面UI模板数据过滤
export const filterWidgetList = (data, type) => {
  return (data || []).filter(el => el.widgetType === type)
}

export const findWidget = (data, type, parentId) => {
  return (data || []).find(el => el.widgetType === type && el.parentId === parentId)
}

// 数据树状化
export const formatTreeData = (cur=[],arr=[]) => {
  // 生成根目录
  if(cur.length == 0){
    // 格式化数据格式
    arr = arr.map(item => {
      return {
        key: item.widgetId,
        ...item
      }
    })
    cur = arr.filter(item => arr.every(itemx => itemx.widgetId != item.parentId));
  }
  cur.forEach(item => {
    let childs = arr.filter(itemx => itemx.parentId == item.widgetId);
    if(childs.length){
      let turnChilds = formatTreeData(childs,arr);
      item.children = turnChilds;
    }
  });
  return cur;
}

// 换行
export const portalTextWrapHtml = (value) => {
  return <div style={{ whiteSpace: "pre-line" }} dangerouslySetInnerHTML={{ __html: decodeURIComponent(value || "") }}/>
}