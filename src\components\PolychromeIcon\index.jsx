import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import "./index.scss";

/**
 * @description 阿里矢量图标，彩色图标使用
 * @param {string | number} size // 大小，如：20
 * @param {string} value // iconfont彩色图标的样式名称，如：react
 * @param {obj} style // 追加样式 如：{ color: 'red', margin: 20 }
 */
export default function PolychromeIcon({size, value, style={}}) {
  return (
    <svg style={{ fontSize: `${size}px`, ...style }} className="icon" aria-hidden="true">
      <use xlinkHref={`#${value}`}/>
    </svg>
  );
}