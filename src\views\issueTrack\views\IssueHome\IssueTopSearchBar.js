import DefaultAvatar from "@/components/DefaultAvatar";
import TMoreSelect from "@/components/TMoreSelect";
import { eConsolePropId, eConsoleUiControl, eIssueEmptySelectionId } from "@/utils/enum";
import { useIssueSearchParams } from "@/views/issueTrack/service/issueSearchHooks";
import { Button, Checkbox, DatePicker, Dropdown, Input, Menu, Row, Space, Tabs } from "antd";
import moment from "moment";
import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useImmer } from "use-immer";
import { getCodeValueListByCode } from "../../utils/ArrayUtils";

const { Search } = Input;
const { RangePicker } = DatePicker;
const dateFormat = "YYYY-MM-DD";
const { TabPane } = Tabs;

export default function IssueTopSearchBar({
    subclassAttrList: _subclassAttrList, subclassNid,
    selectionList,/*  setting320Result, userList, objInfo, */ projectId,
    keywords, setKeywords, setPageNo, _criteriaList, refechCodeValueList,
    criteriaList, updateQuery, setCountType
}) {
    const teamId =  process.env.REACT_APP_WORKORDER_TEAMID;
    const nodeId =  process.env.REACT_APP_WORKORDER_NODEID;
    const location = useLocation();
    const { issueNodeId, issueQueryId, setIssueSearchParams } = useIssueSearchParams(); //issue路由配置，页面刷新
    const [_keywords, _setKeywords] = useState(keywords); // 搜索栏显示的关键字
    const [subclassAttrList, setSubclassAttrList] = useImmer(_subclassAttrList);

    useEffect(() => {
        if (_subclassAttrList?.length > 0) {
            let _list = _subclassAttrList.filter(_attr => {
                let queryableProp = _attr.propertyList.find(el => el.propType == eConsolePropId.Prop_59_queryable);
                let uiControlProp = _attr.propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control);
                return queryableProp?.propValue == 1 && (uiControlProp?.propValue == eConsoleUiControl.ListBox || uiControlProp?.propValue == eConsoleUiControl.Date)
            })
            setSubclassAttrList(_list);
        }
    }, [_subclassAttrList]);

    // 关键字回显处理
    useEffect(() => {
        _setKeywords(keywords)
    }, [keywords])

    // 勾选显示
    function checkedVisible(_index, e) {
        setSubclassAttrList(draft => {
            draft[_index].hiddenFlg = !e.target.checked
        });
    }

    // 关键字变更
    function keywordsChange(e) {
        _setKeywords(e.target.value);
    }

    // 关键字搜索
    function onSearchKeyWordsChange(value, event) {
        setKeywords(value);
        _saveQuery(value)
    }

    function _saveQuery(value) {
        let _criteriaListBackend = criteriaList?.length > 0 ?
            // 先过滤begin为空的数据
            criteriaList.filter(el => el.begin != "").map(_criteria => ({
                ..._criteria,
            })
            )
            : null;
        // 如果没有查询条件，则不生成queryId
        if ((!_criteriaListBackend || _criteriaListBackend.length == 0) && (value === "" || value === undefined)) {
            // 取消原queryId
            // setIssueSearchParams(issueNodeId, null)
        }
        // 配置入参
        let objJson = {
            criteriaList: criteriaList,
            keywords: keywords,
        };
        if (value != undefined) {
            objJson.keywords = value
        }
        setCountType(undefined);
        updateQuery(issueNodeId, objJson)
    }

    // 勾选或设置查询条件
    function onSearchCriteriaChanged(type, attrNid, attrVal/*单个元素的数组，或多个元素的数组*/) {
        let criteriaUi = type == eConsoleUiControl.Date ? {
            col: attrNid,
            begin: attrVal ? moment(attrVal[0]?._d).format(dateFormat) : "",
            end: attrVal ? moment(attrVal[1]?._d).format(dateFormat) : ""
        }
            : { col: attrNid, begin: attrVal };
        let idx = criteriaList.findIndex(el => el.col == attrNid);
        if (idx > -1) {
            criteriaList[idx] = criteriaUi
        } else {
            criteriaList.push(criteriaUi)
        }

        _saveQuery()
    }

    // 日期选择，发生变化
    function onRangePickerChanged(attrNid, dateList) {
        onSearchCriteriaChanged(eConsoleUiControl.Date, attrNid, dateList)
    }

    // 获取顶部查询条件option
    function getSearchSelectData(type, property_listId) {
        if (type == eConsoleUiControl.ListBox) {
           /*  if (property_listId == "-701") {
                let _userList = [...userList]
                // 负责人增加一个空选项
                _userList.push({
                  userId: eIssueEmptySelectionId,
                  userName: "空值",
                  avatar: null,
                });
                return _userList?.map(_user => ({
                    ..._user,
                    propType: _user.userId ? _user.userId.toString() : "",
                    propValue: _user.userName,
                    _icon: <DefaultAvatar avatarSrc={_user.avatar} avatarFlg={1} />
                }))
            } else { */
                let selectList = JSON.parse(JSON.stringify(getCodeValueListByCode(selectionList, property_listId))) //不可变更源数据
                // FIXME: 前端默认增加一个空选项,和bill沟通，后端无法配置 by walt 20230307
                selectList.push({
                  selectionId: property_listId,
                  selectionName: "",
                  propType: eIssueEmptySelectionId,
                  propValue: "空值",
                });
                selectList.map(item => {
                    if (item.iconValue) {
                        item._icon = <span className="issueSvg" dangerouslySetInnerHTML={{ __html: item.iconValue }} />;
                    }
                })
                return selectList
            // }
        }
        return [];
    }

    const queryableAttrListMenu = (
        <Menu>
            {subclassAttrList.map((_attr, index) => {
                let _index = index;
                return (
                    <Menu.Item key={index}>
                        <Checkbox className="tms-checked" checked={!_attr.hiddenFlg} onChange={(e) => checkedVisible(_index, e)}>
                            {_attr.name}
                        </Checkbox>
                    </Menu.Item>
                )
            })}
        </Menu>
    );

    return <div className="issuehome-header">
        <Row justify="space-between" align="middle">
            <span className="issuehome-header-title">{/* objInfo?.name */ "工单列表"}</span>
        </Row>
        <div style={{ display: "flex", alignItems: "baseline", justifyContent: "space-between" }}>
            <div style={{ flex: "auto", marginBottom: "10px" }}>
                {/*搜索条: 第1行*/}
                <Row justify="space-between" align="middle">
                    <Space size={20} className="issue-searchlist" wrap>
                        {subclassAttrList.filter(_attr => _attr.type == eConsoleUiControl.ListBox && !_attr.hiddenFlg)
                            .map((_attr, index) => {
                                return <div className="fontsize-12" key={_attr.nodeId}>
                                    <span style={{ color: "#666" }}>{_attr.name}：</span>
                                    <TMoreSelect
                                        key={index}
                                        items={getSearchSelectData(_attr.type, _attr.property_listId)?.map(el => ({
                                            label: el.propValue,
                                            value: el.propType,
                                            icon: el._icon
                                        }))}
                                        defaultValues={criteriaList?.find(_criteria => _criteria.col == _attr.nodeId)?.begin}
                                        onValuesChange={(values/*数组*/) => onSearchCriteriaChanged(eConsoleUiControl.ListBox, _attr.nodeId, values)}
                                    />
                                </div>
                            })
                        }
                         {subclassAttrList.filter(_attr => _attr.type == eConsoleUiControl.Date && !_attr.hiddenFlg)
                            .map((_attr, index) => {
                                return <div className="fontsize-12" key={_attr.nodeId}>
                                    {/* 右上角关闭，目前暂不使用*/}
                                    {/* <Badge count={<a className="iconfont shanchu1" onClick={closeSelectDate(items)} />}> */}
                                    <span style={{ color: "#666" }}>{_attr.name}：</span>
                                    <RangePicker
                                        placeholder={["开始时间", "结束时间"]}
                                        style={{ width: 200, borderRadius: 5 }}
                                        value={
                                            criteriaList?.find(_criteria => _criteria.col == _attr.nodeId)?.begin ?
                                                [moment(criteriaList?.find(_criteria => _criteria.col == _attr.nodeId)?.begin),
                                                moment(criteriaList?.find(_criteria => _criteria.col == _attr.nodeId)?.end)] : null
                                        }
                                        format={dateFormat}
                                        onChange={(dateList) => onRangePickerChanged(_attr.nodeId, dateList)}
                                    />
                                    {/* </Badge> */}
                                </div>
                            })}
                        <Space size={10}>
                            <Dropdown overlay={queryableAttrListMenu} trigger={["click"]} >
                                <Button>
                                    <Space size={4}>
                                        <span style={{ color: "#666" }}>更多</span>
                                        <span className="iconfont youjiantou_huaban fontsize-12" style={{ color: "#999999", transform: "rotate(90deg)" }} />
                                    </Space>
                                </Button>
                            </Dropdown>
                            <Search value={_keywords}
                                placeholder="编号或标题"
                                className='tms-search-input'
                                onSearch={onSearchKeyWordsChange}
                                allowClear={true}
                                // onPressEnter={searchQuery}
                                onChange={keywordsChange}
                            />
                        </Space>
                    </Space>
                </Row>
            </div>
        </div>
    </div>;
}