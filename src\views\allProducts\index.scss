.allProducts {
    height: 100vh;
}

.allProducts-TopMenuBar-box {
    position: fixed;
    top: 0;
    margin: auto;
    padding: 0 20px;
    // max-width: 1260px !important;
    z-index: 1;
    background-color: white;
    box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
    width: 100vw;
    left: 0;
}

// 操作系统
.home-snapshot {
    position: relative;
    padding: 50px 20px;
    margin: auto;
    width: 1260px;
    &-title {
        width: 100%;
        text-align: center;
        font-size: 34px;
    }
    &-content {
        // padding: 0 20px;
        margin: auto;
        min-width: 1200px;
        &-tabs {
            margin-top: 10px;
            .MuiTabs-scroller {
                .MuiButtonBase-root {
                    font-size: 14px;
                    font-weight: bold;
                    color: #333;
                }
                .Mui-selected {
                    color: #1976d2;
                }
            }
        }
        &-tabsBody {
            // padding: 0 40px;
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            &-left {
                width: 360px;
                .tabsBody-left-title {
                    font-weight: bold;
                }
                .tabsBody-left-textList {
                    margin-top: 20px;
                    .tabsBody-left-text {
                        display: flex;
                        align-items: center;
                        margin-top: 10px;
                    }
                }
            }
            &-right {
                width: 800px;
                box-shadow: 0 4px 4px 0 rgb(77 77 77 / 1%), 0 2px 8px 0 rgb(77 77 77 / 7%);
                &-topbar {
                    position: relative;
                    padding: 8px 80px;
                    height: 30px;
                    background-color: #f2f2f2;
                    box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
                    .white-bar {
                        margin: auto;
                        width: 100%;
                        height: 100%;
                        background-color: white;
                    }
                    .dot-list {
                        position: absolute;
                        display: flex;
                        align-items: center;
                        top: 50%;
                        left: 10px;
                        transform: translate(0,-50%);
                        .dot-li {
                            margin-right: 14px;
                            width: 8px;
                            height: 8px;
                            border-radius: 50%;
                        }
                        .dot-red {
                            background-color: #ff5f57;
                        }
                        .dot-yellow {
                            background-color: #ffbe2e;
                        }
                        .dot-green {
                            background-color: #28c940;
                        }
                    }
                }
            }
        }
    }
}

// 单个产品
.products-li {
    text-align: center;
    padding: 50px 0;
    background-color: #f1f7fb;
    &-page {
        margin: auto;
        width: 600px;
        height: 400px;
        background-color: transparent !important;
        box-shadow: 1px 1px 1px -1px rgb(0 0 0 / 5%), 0px 1px 1px 0px rgb(0 0 0 / 4%), 0px 1px 3px 1px rgb(0 0 0 / 6%) !important;
    }
    .products-li-btn {
        margin-top: 40px;
        .MuiButtonBase-root {
            padding: 8px 34px;
            box-sizing: border-box;
            line-height: normal;
            font-size: 12px;
            border-radius: 20px;
        }
        .getting-started {
            color: white;
            background-color: #2bcd6a;
        }
        .learn-more {
            margin-left: 20px;
        }
    }
}
.products-li:nth-child(even) {
    background-color: white;
}

// 操作系统居左（默认模板）
.home-snapshot-left {
    .home-snapshot-content {
        &-tabsBody {
            flex-direction: row !important;
        }
    }
}

// 操作系统居右
.home-snapshot-right {
    .home-snapshot-content {
        &-tabsBody {
            flex-direction: row-reverse !important;
        }
    }
}