/**
 * axios封装
 * 请求拦截、响应拦截、错误统一处理
 */
import axios from "axios";
import JSONbig from "json-bigint";
import { JsonTurnUrl, UrlTurnJson } from "./toolUtil";
import { globalUtil } from "./globalUtil";
import { globalEventBus } from "./eventBus";
import sm4 from "./sm4";

const CancelToken = axios.CancelToken;
let source = CancelToken.source();

const APi_Timeout = Number(process.env.REACT_APP_API_TIMEOUT);

/**
 * 提示函数
 */
const showTip = (msg,showTips) => {
	// 提示
	if(showTips !== false){
		globalUtil.error(msg);
	}
};

/**
 * 跳转登录页
 */
const toLogin = () => {
	let team619Result = globalUtil.getQueryClient().getQueryData(["team_619_user_info"]);
	if(team619Result && team619Result.resultCode == 200){
		globalUtil.getQueryClient().removeQueries({ queryKey: [], exact: true })
	};
}
	

/**
 * item错误
 */
const openErrorModal = (response) => {
	globalEventBus.emit("openErrorModalEvent", "", {
    response: response,
  });
}

/**
 * 请求失败后的错误统一处理
 * @param {Number} status 请求失败的状态码
 */
const errorHandle = (status, other, response) => {
	// 状态码判断
	switch (status) {
		// 登录失效
		case -5: 
			// 如果当前含有 noRedirect
			try {
				let searchParams = UrlTurnJson(window.location.href)
				if(searchParams?.["noRedirect"] === "1"){
					break;
				}
			} catch (error) {
				console.log(error);
			}
			// source.cancel("登录信息失效");
			// source = CancelToken.source();
			// globalUtil.getQueryClient()?.clear();
			// 清除所有useQuery cache data
			toLogin();
			break;
		case -55:
			showTip(`${other}`,response?.config?.showTips);
			// toEditDB(response);
			break;
    case -1:  // iteam错误
      // showTip(`${other}`,response?.config?.showTips);
      openErrorModal(response);
      break;
		case 200:
			break;
		// axios cancel取消的报错信息，不需要提示，目前只有登录信息失效会返回
		// https://confluence.ficent.com/pages/viewpage.action?pageId=98415425&focusedCommentId=98415676#comment-98415676
		case -10: // 团队不存在（团队解散，无效团队）
		case -11: // 用户被团队移除
		case -12: // 团队被禁用
		case -13: // 用户被团队禁用
			// toTeamErrorPage(status,`${other}`);
			break;
		case 404:
			showTip(`资源不存在`,response?.config?.showTips);
			break;
		case 900001: 
			break;
		default:
			showTip(`${other}`,response?.config?.showTips);
			break;
	}
};

// 创建axios实例
const httpBase = axios.create({
  timeout: 1000 * APi_Timeout,
	withCredentials:true
});

/**
 * 请求拦截器
 * 每次请求前，如果存在token则在请求头中携带token
 * // ot-token
// bypass-token
 */
httpBase.interceptors.request.use(
	config => {
		if(Object.prototype.toString.call(config.headers) !== '[object Object]') {
			config.headers = {};
		}
		config.headers["source"] = "portal";
		config.cancelToken = source.token;
		// 从 bypass-token 判断是否需要加密
		let bypass_token = localStorage.getItem("bypass-token");
		if(bypass_token){
			config.headers["bypass-token"] = bypass_token;
			return config;
		}
		if(config.headers && config.headers["Content-Type"] === "application/json"){
			// 从 ot-token 判断是否需要加密
			let ot_token = localStorage.getItem("ot-token");
			if(ot_token){
				config.headers["ot-token"] = ot_token;
				config.headers["Content-Type"] = "text/plain";
				config.data = sm4.encrypt(config.data, ot_token,{padding: "pkcs#5"})
			}
		}
		return config;
	},
	error => Promise.error(error)
	
);

// 响应拦截器
httpBase.interceptors.response.use(
	res => { // 请求成功
		let ot_token = res.headers["ot-token"];
		// save sm4 encode key
		if(res.headers && res.headers["ot-token"] != undefined && res.headers["ot-token"] != null){
			localStorage.setItem("ot-token", res.headers["ot-token"])
		}
		// remove sm4 encode
		if(res.headers && res.headers["encrypt"] === "e=b"){
			localStorage.removeItem("ot-token")
		}
		// sm4 decode
		if(res.headers && res.headers["encrypt"] === "e=a"){
			// bypass-token 失效，remove bypass-token
			localStorage.removeItem("bypass-token")
			res.data = sm4.decrypt(res.data, ot_token,{padding: "pkcs#5"})
		}
		try{
			res.data = JSONbig.parse(res.data);
		}catch (err) {
			try {
				res.data = JSON.parse(res.data);
			} catch (error) {}
		}
		console.log(res);
		// 当前接口是use_info不需要做额外的事情
		if(res?.config?.ignore !== "true"){
			errorHandle(res.data.resultCode,res.data.resultMessage,res);
		}
		return res.status === 200 ? Promise.resolve(res.data) : Promise.reject(res);
	},
	error => { // 请求失败
		console.log("调用出错啦!!!!!",error)
		try {
			// 本地取消的调用
			if(axios.isCancel(error)){ 
				return Promise.resolve({
					resultCode: 900001,
					resultMessage: error?.message
				})
			}
	
			if(axios.isAxiosError(error)){
				const { response,message } = error;
				if(response){
					// 浏览器有返回状态 404 等情况
					if(response?.status != undefined && response.status != 200){
						errorHandle(response.status, (response.statusText || message),{config: {showTips: true}});
						return Promise.resolve({
							resultCode: response.status,
							resultMessage: response.statusText
						}) 
					}
	
					// 浏览器有返回状态 200，但接口返回状态非200
					if(response?.status != undefined && response.status && response.status == 200){
						errorHandle(response.data.status, response.data.message,{config: {showTips: true}});
						return Promise.resolve({
							resultCode: response.data.status,
							resultMessage: response.data.message
						})
					}
				}
	
				if(message){
					errorHandle(900000, message,{config: {showTips: true}});
					return Promise.resolve({
						resultCode: 900000,
						resultMessage: message
					})
				}
			}
	
			errorHandle(900003,"未知错误："+ error?.toString(),{config: {showTips: true}});
			return Promise.resolve({
				resultCode: 900003,
				resultMessage: `未知错误：${error?.toString()}`
			})
		} catch (err) {
			errorHandle(900004,"未知错误local",{config: {showTips: true}});
			return Promise.resolve({
				resultCode: 900004,
				resultMessage: `未知错误local`
			})
		}
	}
);

/**
 * @param url 调用地址
 * @param data 没有可以传null
 * @param header 请求头
 * @param config [AxiosRequestConfig] 可以自定义，注：只可以传递Map数据，如果 config = {ignore: true},则不处理返回错误的状态码异常信息
 */
export function get(url,data,headers,showTips = true,config = {}){
	config.headers = headers;
	config.showTips = showTips;
	if(data){
		let params = JsonTurnUrl(data);
		if(params!="")url += "?"+params;
	}
	return httpBase.get(url,config)
}

/**
 * @param url 调用地址
 * @param data 请求参数
 * @param header 请求头
 * @param config [AxiosRequestConfig] 可以自定义，注：只可以传递Map数据，如果 config = {ignore: true},则不处理返回错误的状态码异常信息
 */
export function post(url,data,headers={"Content-Type":"application/json"},showTips = true,config = {}){
	config.headers = headers;
	config.showTips = showTips;
	if(headers["Content-Type"] == 'application/x-www-form-urlencoded'){
		data = data
	}else{
		data = JSON.stringify(data)
	}
	return httpBase.post(url,data,config)
}


/**
 * @param url 调用地址
 * @param data 请求参数
 * @param header 请求头
 * @param config [AxiosRequestConfig] 可以自定义，注：只可以传递Map数据，如果 config = {ignore: true},则不处理返回错误的状态码异常信息
 */
export function downloadExcel(url,data,headers={"Content-Type":"application/json"},config={}){
	config.headers = headers;
	data = JSON.stringify(data)

	const _httpBase = axios.create({
		timeout: 1000 * APi_Timeout,
		responseType:"blob"
	});
	return new Promise((resolve,reject)=>{
		_httpBase.post(url,data,config)
		.then((res) => {
			// 切割出文件名
			const fileNameEncode = res.headers['filename']
			// 解码
			const fileName = decodeURIComponent(fileNameEncode)
			// 设置type类型
			const blob = new Blob([res.data], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; application/octet-stream'
			})
			if (window.navigator.msSaveOrOpenBlob) {
				navigator.msSaveBlob(blob, fileName);
			} else {
				var link = document.createElement("a");
				link.href = window.URL.createObjectURL(blob);
				link.download = fileName;
				link.click(); //释放内存
				window.URL.revokeObjectURL(link.href);
			}
			resolve({resultCode:200,resultMessage:''})
		}).catch((err) => {
			console.log(err);
			resolve({resultCode:601,resultMessage:'文件下载失败'})
		});
	})
}

export * from "./apiConfig";

