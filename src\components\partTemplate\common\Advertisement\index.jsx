import React, { useEffect, useState } from "react";
import Slider from "react-slick";
import {
  <PERSON>,
  Typography,
  Button
} from "@mui/material";
import { Image } from "antd";
import { skipOS } from "../../../../utils/commonUtils";
import { useAuthContext } from "@/context/AppAuthContextProvider";
import { isArrayUtils, filterWidgetList, formatTreeData, portalTextWrapHtml } from "../../../../utils/commonUtils";
import { formatVariantColor } from "../../../../utils/muiThemeUtils";

import "./index.scss";

// Banner
export default function Advertisement(props) {
  const { data, isBgFlag } = props;
  const authActions = useAuthContext();
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  },[data?.widgetList])

  // 获取模板排列位置
  const getDemoPosition = (type) => {
    let className_ = "";
    switch (type) {
      case "left":
      case "right":
        className_ = "home-recommend-" + type
        break;    
      default:
        break;
    }
    return className_
  }

  return (
    <>
      {(moduleData.length > 0) && <Box 
        className={[
          "home-recommend", 
          getDemoPosition(filterWidgetList(moduleData, 11)[0]?.value)
        ].join(" ")}
        style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}
      >
        <Box className="home-recommend-content">
          <Box className="home-recommend-content-left">
            <div style={{ fontWeight: '600', fontSize: '40px' }}>{filterWidgetList(moduleData, 1)[0]?.value}</div>
            <Typography sx={{ margin: '20px 0' }} variant="subtitle1">
              {portalTextWrapHtml(filterWidgetList(moduleData, 2)[0]?.value)}
            </Typography>
            <Button
              sx={{ width: 150, borderRadius: 20, overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}
              variant={formatVariantColor(filterWidgetList(moduleData, 4)[0]?.style)?.variant}
              color={formatVariantColor(filterWidgetList(moduleData, 4)[0]?.style)?.color}
              onClick={() => skipOS(authActions.isAuthenticated)}
            >
              {filterWidgetList(moduleData, 4)[0]?.value}
            </Button>
          </Box>
          <Box className="home-recommend-content-right">
            <Slider {
              ...{
                dots: true,
                infinite: true,
                slidesToShow: 1,
                slidesToScroll: 1,
                autoplay: true,
                speed: 1000,
                autoplaySpeed: 4000,
                touchMove: false,
                arrows: false
              }
            }
            className="portal-slider">
              {filterWidgetList(moduleData, 3).map((step, index) => (
                <div key={step.widgetId}>
                  <Image 
                    src={step.value}
                    alt={step.widgetId}
                    style={{
                      display: 'block', 
                      width: '100%', 
                      height: '400px', 
                      overflow: 'hidden'
                    }}
                  />
                </div>
              ))}
            </Slider>
            {isBgFlag && <div className="home-head__bubble" />}
          </Box>
        </Box>
      </Box>}
    </>
  );
}

