// https://codesandbox.io/s/ji-ben-antd-4-24-10-forked-ckhftj 高度问题demo，待跟进
.common-sider {
  border-right: 1px solid #f1f1f1;
  z-index: 0;

  // 给拖拽到顶部和顶部增加padding以便于显示拖动条
  .ant-tree-list-holder-inner {
    padding: 5px 0 15px 0;
  }

  /* 顶部搜索栏 */
  .common-sider-toolbar {
    display: flex;
    margin: 12px 0px 12px 12px;

    // 搜索
    &-search {
      flex: auto;
      background: #f2f2f2;
      border-radius: 5px;
      display: flex;
      align-items: center;
    }

    // 新建
    &-create {
      display: flex;
      align-items: center;
      justify-content: right;
    }
  }

  // 左侧树搜索时不换行
  .common-sider-search {
    .ant-tree-title {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;
    }
  }

  // 左侧tree
  .common-sider-tree {
    flex: auto;
    color: #ffffff;
    z-index: 0;

    .ant-tree-node-content-wrapper,
    .ant-tree-node-content-wrapper.ant-tree-node-selected {
      background-color: unset !important;
    }

    .ant-tree-treenode {
      padding: 0;

      &:hover {
        background: #EDEDED !important;
      }

      &:active {
        background: #F2F5FD !important;
      }
    }

    // hover颜色
    .ant-tree-treenode.ant-tree-treenode-selected:hover {
      background: #EDEDED !important;
    }

    // 选中颜色
    .ant-tree-treenode.ant-tree-treenode-selected {
      background: #F2F5FD !important;
    }

    .ant-tree-iconEle.ant-tree-icon__customize {
      // width: auto !important; // 宽度自适应，解决post和get等图标大小不一样的问题
      display: inline-block;
      width: 24px;
      height: 24px;
      line-height: 24px;
      min-width: 24px; //要设置最小宽度，否则会被右侧超出长度的title挤压
      text-align: center;
      margin-right: 4px;

      .iconfont {
        font-size: 14px !important;
        line-height: 14px !important;
        color: #999;
      }

    }

    .ant-tree-title {
      color: #333;
    }

    .ant-tree-treenode-switcher-open {
      .ant-tree-switcher_open {
        transform: rotate(90deg);
      }
    }

    .ant-tree-switcher {
      width: 14px;
      line-height: 14px;
      align-items: center;
      display: flex;
      margin-right: 3px;
      margin-left: 5px;

      .ant-tree-switcher-icon {
        font-size: 14px;
      }
    }

    .ant-tree-node-content-wrapper {
      // padding-top: 0;
      // padding-bottom: 0; 
      min-height: 30px;
      line-height: 30px;
      display: flex;
      align-items: center;
      .ant-tree-title {
        flex: auto;
      }
    }

  }

  // /* 隐藏 antd 树形组件的滚动条 */
  // .ant-tree {
  //   overflow: hidden;
  // }

  // /* 隐藏 antd 树形组件的滚动条轨道 */
  // .ant-tree .ant-tree-scrollbar {
  //   display: none;
  // }

  // 空白页
  .blank-page {
    text-align: center;
    padding-top: 200px;

    &-title {
      color: #999;
      font-size: 18px;
      padding-bottom: 10px;
    }

    &-des {
      font-size: 12px;
      color: #666;

      a {
        color: #0077f2;
      }
    }
  }
}

/* .ant-tree-list {
  &-holder {
      &-inner {
          .ant-tree-treenode {
              .ant-tree-node-content-wrapper {
                padding: 0px;
              }
          }
      }
  }
} */