import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Box
} from "@mui/material";
import "../index.scss";
import {
  team_1301_get_page_part_widget,
} from "../../../api/http_common";
import TopMenuBar from "../../../components/TopMenuBar";
import FooterBar from "../../../components/FooterBar";
import CooperateAdvert from "./CooperateAdvert"; // 招商合作-头部广告
import WinFuture from "./WinFuture"; // 招商合作-携手iTeam共赢未来
import GiveEverything from "./GiveEverything"; // 招商合作-倾其所有
import CooperateCondition from "./CooperateCondition"; // 招商合作-合作条件
import BottomAdvert from "./BottomAdvert"; // 招商合作-底部广告
import ContactUsModal from "../../../components/ContactUsModal";
import ApplyCooperationDrawer from "../../../components/ApplyCooperationDrawer"; // 代理商合作
import {partTypeEnum} from 'src/utils/enum';

export default function Cooperate() {
  const location = useLocation();
  const [partList, setPartList] = useState([]);
  const [contactUsOpen, setContactUsOpen] = useState(false);
  const [applyOpen, setApplyOpen] = useState(false);

  useEffect(() => {
    getPagePartWidget(6666, location.pathname)
  },[])

  const getPagePartWidget = (teamId, menuUrl) => {
    team_1301_get_page_part_widget({teamId, menuUrl}).then(res => {
      if(res.resultCode == 200){
        setPartList([...res.partList])
      }
    });
  }

  // 打开联系我们弹窗
  const openContactUsModal = () => setContactUsOpen(true)

  // 关闭联系我们弹窗
  const cancelContactUsModal = () => setContactUsOpen(false)

  // 打开招商合作弹窗
  const openApplyDrawer = () => setApplyOpen(true)

  // 关闭招商合作弹窗
  const cancelApplyDrawer = () => setApplyOpen(false)

  // 招商合作-内容渲染
  const getCooperateContent = (type, data, index) => {
    switch (type) {
      case partTypeEnum.part_type_51_coop_partner_banner :
        return <CooperateAdvert key={index} data={data} openContactUsModal={openContactUsModal} openApplyDrawer={openApplyDrawer}/>

      case partTypeEnum.part_type_52_coop_partner_winwin :
        return <WinFuture key={index} data={data}/>

      case partTypeEnum.part_type_53_coop_partner_devotion :
        return <GiveEverything key={index} data={data}/>

      case partTypeEnum.part_type_54_coop_partner_cooperation :
        return <CooperateCondition key={index} data={data}/>

      case partTypeEnum.part_type_55_coop_partner_footer :
        return <BottomAdvert key={index} data={data} openContactUsModal={openContactUsModal} openApplyDrawer={openApplyDrawer}/>
    
      default:
        return <></>
    }
  }

  return (
    <div className="cooperate">
      <Box sx={{ height: 76 }} />
      <Box sx={{ width: "100%", padding: "0 100px" }}>
        <Box className="cooperate-TopMenuBar-box">
          <TopMenuBar />
        </Box>
      </Box>

      {partList.map((item, index) => {
        return getCooperateContent(item.partType, item, index)
      })}

      <FooterBar />
      <ContactUsModal open={contactUsOpen} onCancel={cancelContactUsModal}/>
      <ApplyCooperationDrawer open={applyOpen} onCancel={cancelApplyDrawer}/>
    </div>
  );
}
