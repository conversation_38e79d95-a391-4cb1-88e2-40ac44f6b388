import React, { useEffect, useRef, useState } from "react";
import { Row, Button, Input, Space, Select, DatePicker, Cascader, Image, InputNumber, } from "antd";
import { getAvatarById, formatSvg, getIconValueByIdType } from "@/views/issueTrack/utils/ArrayUtils";
import moment from 'moment';
import "./IssueDetail.scss"
import TEditor from "@/components/TEditor/TEditor";
import DefaultAvatar from "@/components/DefaultAvatar";
import { eConsoleNodeId, eConsoleUiControl } from "@/utils/enum";
import { errorPng } from "@/utils/error";
import { TEditorPreview } from "@/components/TEditor/TEditor";
import {isEmpty} from "@/utils/ArrayUtils";
import {isBase64} from "@/utils/toolUtil";

/**
 * onContentChanged(value) 输入内容回调
 * editable: 是否可编辑
 */
export default function EditText({ teamId, issueNodeId, issueId, editType, format, attrNid, _contentValue,/*  userList,  */_onContentChanged, selectionId, selectionList, _width, children, editable, onRefresh }) {
  const [editing, setEditing] = useState(false);
  const [contentValue, setContentValue] = useState(_contentValue || "");
  const [funModuleData, setFunModuleData] = useState([]);
  const [showDateTime, setShowDateTime] = useState(false);
  let editRef = useRef(null);
  let inputRef = useRef(null);
  const dateFormat = 'YYYY-MM-DD';
  var time = "HH:mm:ss"
  useEffect(() => {
    if (selectionId == 1930) {
      formatFunModuleData()
    }
  }, [selectionId])
  useEffect(() => {
    if (editType == eConsoleUiControl.Date && format) {
      let showtime = format.indexOf(time)
      if (showtime == -1) {
        setShowDateTime(false)
      } else {
        setShowDateTime(true)
      }
    }
  }, [format])
  // 格式化功能模块下拉框数据
  const formatFunModuleData = () => {
    let arr_ = []
    let children_ = []
    arr_ = selectionList.filter(el => el.selectionId == '1930')
    for (let i = 0; i < arr_.length; i++) {
      children_.push(...(selectionList.filter(el => el.selectionId == arr_[i].propType)))
    }
    setFunModuleData(formatTreeData([], [...arr_, ...children_]))
  }
  const formatTreeData = (cur = [], arr = []) => {
    // 生成根目录
    if (cur.length == 0) {
      // 格式化数据格式
      arr = arr.map(item => {
        return {
          key: item.propValue,
          value: item.propValue,
          label: item.propValue,
          ...item
        }
      })
      cur = arr.filter(item => arr.every(itemx => itemx.propType != item.selectionId));
    }
    cur.forEach(item => {
      let childs = arr.filter(itemx => itemx.selectionId == item.propType);
      if (childs.length) {
        let turnChilds = formatTreeData(childs, arr);
        item.children = turnChilds;
      }
    });
    return cur;
  }

  // 处理提交传入的系统模块级联数据
  const cascaderChange = (value, selectedOptions) => {
    if (selectedOptions.length > 0) {
      setContentValue([...selectedOptions.map((item, index) => { return item.propType })])
    } else {
      setContentValue([])
    }
  }
  // 系统模块失去焦点
  function onCascaderBlur() {
    let value = ""
    if (_contentValue) {
      _contentValue.map(item => {
        if (value) {
          value = value + "/" + item
        } else {
          value = item
        }
      })
    }
    setEditing(false);
    _onContentChanged && _onContentChanged(value);
  }
  // 数据回调
  function onContentChanged(e) {
    setEditing(false);
    // 没有修改无需提交接口；避免调用接口上传以及刷新，以及邮件通知 by walt 202-03-09
    if(_contentValue != e.target.value) _onContentChanged && _onContentChanged(e.target.value);
  }

  // 编辑器回调
  function onContentBlur() {
    let value = editRef.current.getContent()
    setEditing(false);
    _onContentChanged && _onContentChanged(value);
  }

  // 点击icon进行编辑
  function onEditIconClick() {
    setEditing(true)
    try {
      if (editType == eConsoleUiControl.RichTextBox) {
        setTimeout(() => {
          editRef && editRef.current.setContent(_contentValue)
          editRef.current.focus(); //点击图标自动获取焦点 -fix tmsbug-1473
        }, 100);
      } else if (editType == eConsoleUiControl.TextBox && attrNid != eConsoleNodeId.Nid_11102_Issue_Title) {
        setTimeout(() => {
          inputRef.current.focus()
        }, 100);
      } else if (editType == eConsoleUiControl.TextBox && attrNid == eConsoleNodeId.Nid_11102_Issue_Title) {
        setTimeout(() => {
          setContentValue(contentValue);
          inputRef.current.focus()
        }, 100);
      }
    } catch (error) {
      console.log(error)
    }
  }

  // 文本编辑
  function onTextChange(e) {
    setContentValue(e.target.value);
  }

  // 文本数字编辑
  function onTexNumtChange(e) {
    setContentValue(e);
  }

  // 日期编辑
  function onDateChange(data, dataString) {
    setEditing(false);
    setContentValue(dataString);
    _onContentChanged && _onContentChanged(dataString);
  }

  // 下拉选择编辑
  function selectChange(data) {
    setEditing(false);
    setContentValue(data);
    _onContentChanged && _onContentChanged(data);
  }

  // 暂不使用
  function uploadFileCallBack(data) {
    let link = data.link
    let name = link.substring(link.lastIndexOf("/") + 1)
    let fileObj = { objId: data.id, objType: 23, objName: name, seqNo: 1 }
  }

  // 取消
  function onCancelClick() {
    setEditing(false);
  }
  return <>
    {/* 文本框 */}
    {editing && editType == eConsoleUiControl.TextBox && (format != "整数" && format != "数字") &&
      <Input style={{ fontSize: 12, width: _width }}
        id="box"
        maxLength={50}
        defaultValue={_contentValue}
        onChange={(e) => onTextChange(e)}
        ref={inputRef}
        onBlur={(e) => onContentChanged(e)}
        onPressEnter={(e) => onContentChanged(e)}
      />
    }
    {editing && editType == eConsoleUiControl.TextBox && format == "整数" &&
      <InputNumber type={"number"}
        id="box"
        defaultValue={_contentValue}
        precision={0}
        onChange={(e) => onTexNumtChange(e)}
        ref={inputRef}
        onBlur={(e) => onContentChanged(e)}
        onPressEnter={(e) => onContentChanged(e)}
      />
    }
    {editing && editType == eConsoleUiControl.TextBox && format == "数字" &&
      <InputNumber type={"number"}
        id="box"
        defaultValue={_contentValue}
        onChange={(e) => onTexNumtChange(e)}
        ref={inputRef}
        onBlur={(e) => onContentChanged(e)}
        onPressEnter={(e) => onContentChanged(e)}
      />
    }
    {/* 系统模块 */}
    {editing && editType == eConsoleUiControl.CascadeListBox &&
      <Cascader popupClassName="funModuleData-popup" changeOnSelect
        options={funModuleData}
        placeholder="请选择功能模块"
        onChange={cascaderChange}
        onBlur={(e) => onCascaderBlur(e)}
      />
    }
    {editing && editType == eConsoleUiControl.RichTextBox &&
      <>
        {/* 修复编辑框超长的问题 */}
        <div style={{ width: "100%" }}>
          <TEditor
            ref={editRef}
            placeholderText=""
            // blur={(e) => onContentBlur(e)}
            uploadParams={{ teamId, nodeId: issueNodeId, moduleName: "issues", objId: issueId, objType: "31704" }}
            uploadCallback={uploadFileCallBack.bind(this)} />
          <Row justify="end" className="comment-input-footer">
            <Space size={10} direction="horizontal" >
              <Button type="default" onClick={(e) => onCancelClick(e)}>取消</Button>
              <Button type="primary" onClick={(e) => onContentBlur(e)}>保存</Button>
            </Space>
          </Row>
        </div>
      </>
    }
    {editing && editType == eConsoleUiControl.ListBox && selectionId != "-701" &&
      <Select defaultValue={_contentValue != null ? _contentValue.toString() : ""}
        showSearch
        autoFocus={true}
        style={{ width: 120, borderRadius: 3, fontSize: 12 }}
        onChange={selectChange.bind(this)}
        onBlur={() => onCancelClick()}
        filterOption={(input, option) =>
          (option.children || "").toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
      >
        {selectionList.filter(item => item.selectionId == selectionId)
          .map((item, index) => {
            if (item.iconValue) {
              return <Select.Option key={index} value={item.propType} >
                <div style={{ display: "flex", alignItems: "center" }}>
                  {formatSvg(item.iconValue)}
                  <span style={{ paddingLeft: 5 }}>{item.propValue}</span>
                </div>
              </Select.Option>
            } else {
              return <Select.Option key={index} value={item.propType}>{item.propValue}</Select.Option>
            }
          })
        }
      </Select>
    }
   {/*  {editing && editType == eConsoleUiControl.ListBox && selectionId == "-701" &&
      <Select defaultValue={_contentValue}
        style={{ width: 180, borderRadius: 3, fontSize: 12 }}
        autoFocus={true}
        showSearch     //select选择框搜索
        filterOption={(input, option) => {
          return (option.children[1].props.children || "").toLowerCase().indexOf(input.toLowerCase()) >= 0
          // return (option.children || "").toLowerCase().indexOf(input.toLowerCase()) >= 0
        }}
        onChange={selectChange.bind(this)}
        onBlur={() => onCancelClick()}>
        {userList.map((item, index) => {
          return <Select.Option key={index} value={item.userId}>
            <DefaultAvatar avatarSrc={item.avatar} avatarFlg={1} />
            <span style={{ marginLeft: 5 }}>{item.userName}</span>
          </Select.Option>
        })}
      </Select>
    } */}
    {/* 有格式 */}
    {editing && editType == eConsoleUiControl.Date && format &&
      <DatePicker allowClear={false}
        showTime={showDateTime}
        format={format}
        autoFocus={false}
        defaultValue={contentValue ? moment(contentValue, dateFormat) : null}
        placeholder="请选择时间"
        onChange={onDateChange.bind(this)}
        onBlur={(e) => onContentChanged(e)} />
    }
    {/* 无格式 组件自带格式*/}
    {editing && editType == eConsoleUiControl.Date && !format &&
      <DatePicker allowClear={false}
        autoFocus={false}
        defaultValue={contentValue ? moment(contentValue, dateFormat) : null}
        placeholder="请选择时间"
        onChange={onDateChange.bind(this)}
        onBlur={(e) => onContentChanged(e)} />
    }
    {/* selectionId为1908的数据，为其添加icon */}
    {/* fix tmsbug-1456 */}
    {!editing && attrNid == eConsoleNodeId.Nid_11109_Issue_Type && _contentValue &&
      <div style={{ paddingRight: 5 }}>
        {formatSvg(getIconValueByIdType(selectionList, selectionId, _contentValue))}
      </div>
    }
    {/* selectionId为-701的数据，为其添加头像 */}
    {/*  {!editing && selectionId == "-701" && _contentValue &&
      <div style={{ paddingRight: 5, marginLeft: -3 }}>
        <DefaultAvatar avatarSrc={getAvatarById(userList, _contentValue)} avatarFlg={1} />
      </div>
    } */}
    {/* 页面截图回显 */}
    {!editing && editType == eConsoleUiControl.Image &&
      <div style={{ paddingRight: 5, marginLeft: -3 }}>
        {isEmpty(_contentValue) ? "无页面截图" :
          <Space size={30}>
            {
              /* 兼容base64字符串文件 */
              isBase64(_contentValue) ? <Image className="issue-detail-image" height={100} src={_contentValue} fallback={errorPng}/> :
              _contentValue?.split(",").map((src, index) => <Image className="issue-detail-image" key={index} height={100} src={src} fallback={errorPng}/>)
            }
          </Space>
        }
      </div>
    }
    {
      !editing && editType == eConsoleUiControl.RichTextBox &&
      <div className="issue-detail-editor-preview showIcon">
        <TEditorPreview content={_contentValue} isedit={editable} uploadParams={{ teamId }} onRefresh={() => onRefresh && onRefresh()} />
      </div>
    }

    {/* {!editable && editType != eConsoleUiControl.Image && format == "%Y-%m-%d" && children.slice(0, 10)} */}
    {/* 页面截图不可编辑及回显数据 */}
    {!editing && editType != eConsoleUiControl.Image && editType != eConsoleUiControl.RichTextBox && children}
    {/* 工单不可编辑 */}
    {!editing && editable &&
      <Button onClick={onEditIconClick.bind(this)}
        icon={<span className="iconfont bianjishuru fontsize-16 issue-edit-btn" style={{ color: "#999999" }}></span>}
        type="text" />
    }
  </>

}