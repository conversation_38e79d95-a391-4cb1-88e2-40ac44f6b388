import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import {Box} from "@mui/material";
import "../index.scss";
import {
  team_1301_get_page_part_widget,
} from "../../../api/http_common";
import TopMenuBar from "../../../components/TopMenuBar";
import FooterBar from "../../../components/FooterBar";
import Cust_Part_0_Banner from "./Cust_Part_1_Banner"; // 定制合作-头部广告
import Cust_Part_2_Multi_Tech from "./Cust_Part_2_Multi_Tech"; // 定制合作-多端多场景
import Cust_Part_3_Industries from "./Cust_Part_3_Industries"; // 解决方案
import Cust_Part_4_Full_Customization from "./Cust_Part_4_Full_Customization"; // 深度定制
import Cust_Part_5_Cluster_Solution from "./Cust_Part_5_Cluster_Solution"; // 部署方案
import Cust_Part_6_Good_Service from "./Cust_Part_6_Good_Service"; // 保障运营
import Cust_Part_7_Deliver_Quality from "./Cust_Part_7_Deliver_Quality"; // 交付流程
import Cust_Part_8_Footer from "./Cust_Part_8_Footer"; // 底部广告
import ApplyCooperationDrawer from "../../../components/ApplyCooperationDrawer"; // 代理商合作
import ContactUsModal from "../../../components/ContactUsModal";
import {partTypeEnum} from 'src/utils/enum';

export default function Cooperate() {
  const location = useLocation();
  const [partList, setPartList] = useState([]);
  const [applyOpen, setApplyOpen] = useState(false);
  const [contactUsOpen, setContactUsOpen] = useState(false);
  useEffect(() => {
    getPagePartWidget(6666, location.pathname)
  },[])

  const getPagePartWidget = (teamId, menuUrl) => {
    team_1301_get_page_part_widget({teamId, menuUrl}).then(res => {
      if(res.resultCode == 200){
        setPartList([...res.partList || []])
      }
    });
  }


  const openApplyDrawer = () => setApplyOpen(true) // 打开联系我们弹窗
  const cancelApplyDrawer = () => setApplyOpen(false) // 关闭联系我们弹窗
  const openContactUsModal = () => setContactUsOpen(true) // 打开联系我们弹窗
  const cancelContactUsModal = () => setContactUsOpen(false) // 关闭联系我们弹窗

  // 定制合作-内容渲染
  const getCustomMadeContent = (type, data, index) => {
    switch (type) {
      case partTypeEnum.part_type_61_coop_customize_banner :
        return <Cust_Part_0_Banner key={index} data={data}/>

      case partTypeEnum.part_type_62_coop_customize_scenario :
        return <Cust_Part_2_Multi_Tech key={index} data={data}/>

      case partTypeEnum.part_type_63_coop_customize_solution :
        return <Cust_Part_3_Industries key={index} data={data}/> //行业解决方案，暂未开启(关闭状态)

      case partTypeEnum.part_type_64_coop_customize_digitalize :
        return <Cust_Part_4_Full_Customization key={index} data={data}/>

      case partTypeEnum.part_type_65_coop_customize_deploy :
        return <Cust_Part_5_Cluster_Solution key={index} data={data}/> //集群，部署方案

      case partTypeEnum.part_type_66_coop_customize_service :
        return <Cust_Part_6_Good_Service key={index} data={data}/>

      case partTypeEnum.part_type_67_coop_customize_deliver :  //交付流程，暂未开启(关闭状态)
        return <Cust_Part_7_Deliver_Quality key={index} data={data}/>

      case partTypeEnum.part_type_68_coop_customize_footer :
        return <Cust_Part_8_Footer key={index} data={data}
                                   openApplyDrawer={openApplyDrawer}  //申请合作
                                   openContactUsModal={openContactUsModal}/>  //咨询客服
    
      default:
        return <></>
    }
    
  }
  
  return (
    <div className="cooperate">
      <Box sx={{ height: 76 }} />
      <Box sx={{ width: "100%", padding: "0 100px" }}>
        <Box className="cooperate-TopMenuBar-box">
          <TopMenuBar/>
        </Box>
      </Box>

      {partList.map((item, index) => {
        return <React.Fragment key={index}>
          {getCustomMadeContent(item.partType, item, index)}
        </React.Fragment>
      })}
      
      <FooterBar />
      <ContactUsModal open={contactUsOpen} onCancel={cancelContactUsModal}/>
      <ApplyCooperationDrawer open={applyOpen} onCancel={cancelApplyDrawer}/>
    </div>
  );
}
