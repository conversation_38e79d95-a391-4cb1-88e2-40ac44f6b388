import React, { useEffect } from "react";
import { Button, Image, Space } from "antd";
import { useAsyncError } from "react-router-dom";
import NoPage from "../NoPage";

const PAGECODE_NORESOURCE = 203; // 资源不存在
const PAGECODE_PAGEERROR = 204; // 页面出错了
const PAGECODE_NOPRIV = 205; // 资源无权限

export default function AppErrorElement(props) {

  const error = useAsyncError();
  // const navigate = useNavigate();
  // const location = useLocation();

  const onRefreshClick = () => {   
    // navigate({
    //   pathname: location.pathname,
    //   search: location.search,
    //   hash: location.hash
    // },{
    //   replace: true,
    //   state:{
    //     refresh: true
    //   }
    // })
    window.location.reload()
  }

  useEffect(() => {
    console.log("AppErrorElement",error,props)
  },[])

  const NOPRIV = <div style={{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",background:"#fff"}}>
    <Space size={10} direction="vertical" align="center">
      <Image src={require("../../assets/images/noPermission.png")} preview={false} style={{width:"250px",marginBottom:"10px"}}/>
      <div style={{fontSize:"16px",color:"#666"}}>当前页面您暂无权限查看</div>
    </Space>
  </div>

  // 资源不存在
  if(error?.pageCode === PAGECODE_NORESOURCE || props?.pageCode === PAGECODE_NORESOURCE) return <NoPage text={"抱歉，找不到页面"}/> 

  // 页面无权限
  if(error?.pageCode === PAGECODE_NOPRIV || props?.pageCode === PAGECODE_NOPRIV) return NOPRIV

  // 接口有返回非200的情况
  const errorElement = <div style={{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",background:"#fff"}}>
    <Space size={10} direction="vertical" align="center">
      <Image src={require("../../assets/images/pageError.jpg")} preview={false} style={{width:"250px",marginBottom:"20px"}}/>
      {/* <div style={{fontSize:"20px",color:"#333"}}>出错啦</div> */}
      <div style={{fontSize:"14px",color:"#999"}}>不好意思，页面貌似出错了</div>
      <Button type="primary" onClick={onRefreshClick}>刷新看看</Button>
    </Space>
  </div>

  return errorElement
}