// 多端多场景覆盖
.multiEnd-multiScenario {
    padding: 50px 0;
    text-align: center;
    background-color: #f9f9f9;
    .multiEnd-multiScenario-stack {
        margin: auto;
        padding: 0 20px;
        width: 1260px;
        .multiEnd-multiScenario-stack-li {
            display: flex;
            flex-direction: column;
            justify-content: baseline;
            align-items: center;
            margin-bottom: 32px;
            padding: 30px 10px;
            width: 190px;
            background-color: white;
            border: 1px solid #0077f2;
            border-radius: 4px;
        }
        .multiEnd-multiScenario-stack-li:nth-child(6n) {
            margin-left: 0;
        }
    }
}