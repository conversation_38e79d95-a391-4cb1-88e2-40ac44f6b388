import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import SwipeableViews from "react-swipeable-views";
import { autoPlay } from "react-swipeable-views-utils";
import { useTheme } from "@mui/material/styles";
import {
  Box,
  MobileStepper,
  Typography,
  Paper,
  Button,
} from "@mui/material";
import "./index.scss";
import KeyboardArrowLeft from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import {VersionDetailDrawer} from "../../views/versionHistory"

const AutoPlaySwipeableViews = autoPlay(SwipeableViews);

export default function SwipeableTextMobileStepper({data}) {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeStep, setActiveStep] = useState(0);
  const [newDetailOpen, setNewDetailOpen] = useState(false);
  const newInfoRef = useRef();

  const handleStepChange = (step) => {
    setActiveStep(step);
  };

  const advertisementClick = (item) => {
    if(!item.subTitle) {
      if(!item.linkUrl) {
        newInfoRef.current = item.id
        setNewDetailOpen(true)
      } else {
        window.open(item.linkUrl)
      }
    } else {
      navigate({
        pathname: item.linkUrl,
        search: `couponCode=${item.subTitle}`
      })
    }
  }

  if(!data || !data.length) return <></>

  return (
    <Box className="SwipeableTextMobileStepper">
      <AutoPlaySwipeableViews
        index={activeStep}
        onChangeIndex={handleStepChange}
        enableMouseEvents
      >
        {data.map((step, index) => (
          <div className="AutoPlaySwipeableViews-item" key={step.id}>
            {Math.abs(activeStep - index) <= 2 ? (
              <Box
              sx={{ color: '#0077f2', cursor: 'pointer' }}
              className="fontsize-14"
              onClick={() => advertisementClick(step)}
              >{step.title}</Box>
            ) : null}
          </div>
        ))}
      </AutoPlaySwipeableViews>
      <MobileStepper
        className="SwipeableTextMobileStepper-MobileStepper"
        steps={data.length}
        position="static"
        activeStep={activeStep}
        variant="dots"
      />
      <VersionDetailDrawer newsId={newInfoRef.current} open={newDetailOpen} onClose={() => setNewDetailOpen(false)}/>
    </Box>
  );
}