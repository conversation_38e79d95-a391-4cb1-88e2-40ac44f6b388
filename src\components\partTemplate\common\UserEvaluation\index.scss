// 用户评价
.user-evaluation {
    padding: 50px 20px;
    min-width: 1200px;
    margin: auto;
    &-title {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
    }
    &-container {
        &-content {
            padding-top: 56px;
            display: flex;
            justify-content: center;
            align-items: center;
            .user-evaluation-swiper {
                padding: 20px;
                width: 1048px;
                height: 343px;
                border-radius: 4px;
                box-shadow: 0 4px 16px 0 rgb(77 77 77 / 12%), 0 1px 2px 0 rgb(77 77 77 / 6%);
                background-color: #fff;
                overflow: hidden;
                .user-evaluation-swiper-text {
                    height: 196px;
                    color: #666;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                .user-evaluation-avatar {
                    display: flex;
                    align-items: center;
                    margin-top: 10px;
                    &-name {
                        display: flex;
                        flex-direction: column;
                        margin-left: 10px;
                        span:first-child {
                            font-size: 14px;
                            font-weight: 700;
                        }
                        span:last-child {
                            font-size: 12px;
                            color: '#999'
                        }
                    }
                }
            }
        }
    }
}

.HomeCarousel {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px 0;
    .HomeCarousel-btn {
        padding: 20px;
        font-size: 26px;
        color: #0077f2;
    }
    .HomeCarousel-Carousel {
        padding: 20px;
        width: 1048px;
        border-radius: 4px;
        box-shadow: 0 4px 16px 0 rgb(77 77 77 / 12%), 0 1px 2px 0 rgb(77 77 77 / 6%);
        .HomeCarousel-Carousel-li {
            &-icon {
                font-size: 50px;
                color: #0077f2;
                transform: rotate(180deg);
            }
            .HomeCarousel-Carousel-li-icon-ending {
                font-size: 50px;
                color: #0077f2;
                // transform: rotate(180deg);
            }
            &-text {
                height: 130px;
                font-size: 16px;
                color: #333;
                display: -webkit-box;
                -webkit-line-clamp: 5;
                -webkit-box-orient:vertical;
                overflow:hidden;
                text-overflow: ellipsis;
                line-height: 2;
            }
            &-avatar {
                display: flex;
                align-items: center;
                margin-top: 10px;
                &-name {
                    display: flex;
                    flex-direction: column;
                    margin-left: 10px;
                    span:first-child {
                        font-weight: bold;
                    }
                }
            }
        }
    }
}