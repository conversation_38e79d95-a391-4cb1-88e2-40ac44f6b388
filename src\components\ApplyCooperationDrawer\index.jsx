import { Button,Drawer,message,Form,Input,Modal,Checkbox,Image, Avatar, Carousel,Upload } from "antd";
import React, { useEffect, useState, useRef, useMemo } from "react";
import { PictureOutlined, CameraOutlined } from '@ant-design/icons';
import { useParams } from "react-router-dom";
import "./index.scss";
import ImgCrop from "antd-img-crop";
import {
  team_1402_save_company_info,
} from "../../api/http_common";
import { 
  team_521_get_security_code,
} from "../../api/http_login";
import { eFileObjId } from "@/utils/enum";

const { Search } = Input;
const { Dragger } = Upload;

let timer;
export default function ApplyCooperationDrawer({open, onCancel}) {
  const [form] = Form.useForm();
  const [uploadId, setUploadId] = useState();
  const [btnDisabled, setBtnDisabled] = useState(false);
  const [time, setTime] = useState(60);
  useEffect(() => {
    if(open) {
      form.resetFields()
    }
  },[open])

  // 验证码定时器
  useEffect(()=>{
    if(time < 60 && time){
      if(!timer)timer = setInterval(() => setTime((pre) => pre-1), 1000);
    }else{
      if(timer){
        clearInterval(timer);
        timer = null;
      }
      setBtnDisabled(false);
      setTime(60);
    }
    return ()=>{
      if(timer){
        clearInterval(timer);
        timer = null;
      }
    }
  },[time])

  const getUploadId = (id) => {
    setUploadId(id)
  }

  // 发送
  const ApplyCooperationFinish = (value) => {
    team_1402_save_company_info({ teamId: 6666, ...value, licenseAttachmentId: uploadId }).then((res) => {
      if(res.resultCode === 200) {
        message.success(res.resultMessage)
        onCancel()
      } else {
        //message.error(res.resultMessage) 20240315 Jim Song,注释掉，否则弹出2次
      }
    })
  }

  // 获取验证码
  const getSecurityCode = () => {
    if (btnDisabled) {
      return;
    }
    if (!form.getFieldValue('mobileNo')) {
      message.warning("请输入手机号！")
      return;
    }
    let request = {
      mobileOrEmail: form.getFieldValue('mobileNo'),
      opType: 6
    };
    team_521_get_security_code(request).then((result) => {
      if (result.resultCode == 200) {
        setTime(59)
        setBtnDisabled(true);
        message.success("发送成功");
      } 
    });
  };
  return (
    <Drawer 
    className="ApplyCooperationDrawer"
    width={600} 
    title="代理商申请" 
    placement="right" 
    onClose={onCancel} 
    open={open}>
      <Form 
      className="ApplyCooperationDrawer-form"
      form={form} 
      wrapperCol={{ offset: 1 }}
      onFinish={ApplyCooperationFinish}>
        <Form.Item label="营业执照" name="licenseAttachmentId"
        rules={[
          {required: true, message: '营业执照不能为空！'}
        ]}>
          <BusinessLicenseItem getUploadId={getUploadId}/>
        </Form.Item>
        <Form.Item label="公司名称" name="companyName"
        rules={[
          {required: true, message: '公司名称不能为空！'}
        ]}>
          <Input style={{ width: 410 }}/>
        </Form.Item>

        <Form.Item label="法人代表" name="name"        
        rules={[
          {required: true, message: '法人代表不能为空！'}
        ]}>
          <Input style={{ width: 410 }}/>
        </Form.Item>

        <Form.Item label="公司地址" name="companyAddress"        
        rules={[
          {required: true, message: '公司地址不能为空！'}
        ]}>
          <Input style={{ width: 410 }}/>
        </Form.Item>

        <Form.Item label="公司规模" name="companySize"        
        rules={[
          {required: true, message: '公司规模不能为空！'}
        ]}>
          <Input style={{ width: 410 }}/>
        </Form.Item>

        <Form.Item label="服务区域" name="serviceArea"        
        rules={[
          {required: true, message: '服务区域不能为空！'}
        ]}>
          <Input style={{ width: 410 }}/>
        </Form.Item>

        <Form.Item label="电子邮箱" name="email"        
        rules={[
          {required: true, message: '电子邮箱不能为空！'}
        ]}>
          <Input style={{ width: 410 }}/>
        </Form.Item>

        <Form.Item label="手机号" required>
          <Form.Item name="mobileNo"
          rules={[
          {required: true, message: '手机号不能为空！'}
          ]} noStyle>
            <Input style={{ width: 300 }}/>
          </Form.Item>
          <Button 
          style={{ marginLeft: 10 }}
          onClick={getSecurityCode}>{!btnDisabled ? "获取验证码" : `${time}s后重发`}</Button>
        </Form.Item>

        <Form.Item label="验证码" name="securityCode"
        rules={[
          {required: true, message: '验证码不能为空！'}
        ]}>
          <Input style={{ width: 410 }}/>
        </Form.Item>

        <Form.Item style={{ textAlign: 'center' }}>
          <Button style={{ width: 200 }} htmlType="submit" type="primary">立即申请</Button>
        </Form.Item>
      </Form>
    </Drawer>
  );
}


// 上传头像
function BusinessLicenseItem({value,onChange,getUploadId}){
  const [isModalVisible, setIsModalVisible] = useState(false); // 编辑状态

  // 上传头像
  const licenseUpload = (file) => {
    getUploadId(file.id)
    onChange?.(file.link)
  }

  const image_src = useMemo(()=> {
    if(value) return value;
    return "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjk4OTE2MDM4ODI2IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjY4MjA1IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiPjxwYXRoIGQ9Ik0wIDBtMTAyLjQgMGw4MTkuMiAwcTEwMi40IDAgMTAyLjQgMTAyLjRsMCA4MTkuMnEwIDEwMi40LTEwMi40IDEwMi40bC04MTkuMiAwcS0xMDIuNCAwLTEwMi40LTEwMi40bDAtODE5LjJxMC0xMDIuNCAxMDIuNC0xMDIuNFoiIGZpbGw9IiNGNkY2RjYiIHAtaWQ9IjY4MjA2Ij48L3BhdGg+PHBhdGggZD0iTTUxMiAzMDcuMmEyMC40OCAyMC40OCAwIDAgMSAyMC40OCAyMC40OHYxNjMuODRoMTYzLjg0YTIwLjQ4IDIwLjQ4IDAgMSAxIDAgNDAuOTZINTMyLjQ4djE2My44NGEyMC40OCAyMC40OCAwIDEgMS00MC45NiAwVjUzMi40OEgzMjcuNjhhMjAuNDggMjAuNDggMCAxIDEgMC00MC45NmgxNjMuODRWMzI3LjY4YTIwLjQ4IDIwLjQ4IDAgMCAxIDIwLjQ4LTIwLjQ4eiIgZmlsbD0iI0JCQkJCQiIgcC1pZD0iNjgyMDciPjwvcGF0aD48L3N2Zz4=";
  },[value])

  return <>
    <div className="license-form">
      <a title="点击上传营业执照">
        <Image
          preview={false}
          style={{ width: 200 }}
          fallback="data:image/png;base64,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"
          src={image_src}
          onClick={(event) => setIsModalVisible(true)}/>
        </a>
    </div>
    <Modal
      title="上传营业执照"
      className="licenseUpload-modal"
      open={isModalVisible}
      onCancel={() => setIsModalVisible(false)}
      footer={null}>
      <ImgUpload licenseUpload={licenseUpload} onCancel={() => setIsModalVisible(false)}/>
    </Modal>
  </>
}

// 图片上传
function ImgUpload(props) {
  const {licenseUpload,onCancel} = props
  const dataSource = {
    maxCount: 1,
    name: "file",
    multiple: false,
    showUploadList: false,
    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`,
    data: {
      teamId: 6666,
      moduleName: "",
      nodeId: eFileObjId,
      objType: "888"
    },
    beforeUpload: (file) => {
      const isPNG = file.type === "image/png" || file.type === 'image/jpeg';
      if (!isPNG) {
        message.error(`${file.name}不是图片格式`);
      }
      return isPNG || Upload.LIST_IGNORE;
    },
    onChange(info) {
      onCancel()
      const { status, response } = info.file;
      if (status == "uploading") {
        console.log(info.file, info.fileList);
      }
      if (status === "done") {
        if(response.resultCode == 200) {
          licenseUpload(response)
          message.success('上传成功');
        } else {
          message.error("上传失败")
        }
      } else if (status === "error") {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
  };
  
  // 预览/裁剪图片
  const onPreview = async (file) => {
    let src = file.url;
    if (!src) {
      src = await new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj);
        reader.onload = () => resolve(reader.result);
      });
    }
    const image = new Image();
    image.src = src;
    const imgWindow = window.open(src);
    imgWindow.document.write(image.outerHTML);
  };
  
  return (
    <ImgCrop rotate modalTitle={"编辑图片"} modalOk="确认" modalCancel="取消">
      <Dragger {...dataSource} onPreview={onPreview}>
        <p className="ant-upload-drag-icon">
          <PictureOutlined />
        </p>
        <p className="ant-upload-text">点击或拖动图片至此处</p>
      </Dragger>
    </ImgCrop>
  );
}