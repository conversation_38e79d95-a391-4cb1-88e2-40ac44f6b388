@import "./assets/variables.scss";

.fontsize-12 {
    font-size: 12px !important;
}
.fontsize-14 {
    font-size: 14px !important;
}
.fontsize-16 {
    font-size: 16px !important;
}
.fontsize-18 {
    font-size: 18px !important;
}
.fontsize-20 {
    font-size: 20px !important;
}

.fontsize-22{
  font-size: 22px!important;
}

.fontsize-24{
  font-size: 24px!important;
}

.fontcolor-light{
  color: #999999;
}

.fontcolor-normal{
  color: #666666;
}

.fontcolor-bold{
  color: #333333;
}

.fontcolor-white{
  color: #fff;
}

.fontweight-bold{
  font-weight: bold;
}

// 搜索选中字体颜色
.color-orange {
  color: $searchColor;
}

.color-yellow{
  color:#fcd53f !important;
}

/* 按钮通用绿色 */
.btn-2ac465 {
    background-color: #2ac465 !important;
}

.sidebar-menu-header {
  height: 116px;
}

// 隐藏全局编辑器底部广告
.fr-second-toolbar {
  display: none;
}

// 自定义表格样式
.custome-table {
  .ant-table-thead > tr > th {
    padding: 10px 16px;
    background: none;
    border-bottom: none;
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 12px;
  }
  .ant-table-tbody > tr > td,
  .ant-table tfoot > tr > th,
  .ant-table tfoot > tr > td {
    padding: 10px 16px;
    border-bottom: none;
    font-size: 12px;
    font-weight: 400;
    color: #333333;
    line-height: 12px;
    // tmsbug-2698：处理表格英文自动换行的问题
    word-wrap: normal;
    word-break: break-all;
  }

  .ant-table-thead
    > tr
    > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
    background-color: #fff;
  }
}

// modal全局
.tms-modal {
  .ant-modal-content {
    //6px圆角边框
    border-radius: 5px !important;
    //去除上边线
    .ant-modal-header {
      border: unset;
      border-radius: 5px !important;
      // modal标题字号16加粗色号333333
      .ant-modal-title {
        font-size: 16px;
        font-weight: bold;
        color: #333333;
      }
    }
    //去除下边线
    .ant-modal-footer {
      border: unset;
      text-align: right; //按钮右对齐
      // 圆角按钮
      button {
        border-radius: 5px;
      }
    }
  }
}
//Drawer全局
.tms-drawer {
  .ant-drawer-wrapper-body {
    //左上角关闭按钮变为右上角
    .ant-drawer-header-title {
      display: unset;
      .ant-drawer-close {
        float: right;
      }
      .ant-drawer-title {
        font-size: 16;
        font-weight: bold;
      }
    }
  }
}

// 调整全局icon大小
.iconfont {
  font-size: 28px;
  line-height: 28px;
}

// 默认按钮 通用
.defaultBtn {
  font-size: 12px;
  height: 28px;
  border-radius: 5px !important;
  background-color: #3279fe !important;
  border-color: #3279fe !important;
  vertical-align: middle;
}
// 默认按钮 浅色
.defaultBtn_light {
  font-size: 12px;
  height: 28px;
  border-radius: 5px !important;
  background-color: #e6f2fe !important;
  border-color: #3279fe5c !important;
  color: #3279fe !important;
}

// 默认按钮 浅色
.defaultBtn_text {
  font-size: 12px;
  height: 28px;
  border-radius: 5px !important;
  background-color: #f2f2f2 !important;
  border-color: #666 !important;
  color: #666 !important;
}

// 默认链接按钮
.defaultLinkBtn {
  font-size: 14px;
  height: 28px;
  vertical-align: middle;
}

// 竖向出现滚动条
.flex-column-parent {
  display: flex;
  flex-flow: column;
}

.flex-column-child {
  flex: auto;
  overflow-y: auto;
  height: 0;
}

.tooltip {
  position: relative;
  display: flex;
}

.tooltip .tooltiptext {
  visibility: hidden;
  // width: 100px;
  // background-color: black;
  // color: #fff;
  // text-align: center;
  // border-radius: 6px;
  // padding: 5px 0;
  // position: absolute;
  // z-index: 1;
  // bottom: 500;
  // left: 50%;
  margin-left: 20px;
  opacity: 0;
  transition: opacity 5s;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

// 滚动条样式
.section,
.section-dark,
.contexify .contexify_submenu {
  &::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }

  &::-webkit-scrollbar-track {
    // background-color: #f5f5f5;
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ededed;
    border-radius: 10px;
  }
}
.section-dark::-webkit-scrollbar-thumb {
  background: #2e3341;
  border-radius: 10px;
}

/* .tms-tabs  .ant-tabs {
  &-nav {
    margin: 0;
    &-wrap {
      .ant-tabs-nav-list {
        .ant-tabs-tab-active{
          .ant-tabs-tab-btn{
            color: $themeBlue !important;
          }
        }
        .ant-tabs-tab:first-child {
          margin-left: 20px;
        }
        .ant-tabs-ink-bar {
          background: $themeBlue;
        }
      }
    }
  }
} */

// tab tabpane 占用剩余空间
.tab-height {
  flex: auto;
  height: 100%;
  & > .ant-tabs-content-holder {
    & > .ant-tabs-content {
      height: 100%;
      & > .ant-tabs-tabpane-active {
        flex: auto;
      }
    }
  }
}

// tab tabpane 滚动
.tab-scroll {
  & > .ant-tabs-content-holder > .ant-tabs-content {
    display: flex;
    flex-flow: column;
    & > .ant-tabs-tabpane-active {
      flex: auto;
      overflow-y: auto;
      height: 0;
    }
  }
}

// 拖拽宽度
.custom-box-right {
  position: relative;
  .ia-splitter-handle {
    height: 100%;
    position: absolute;
    right: -8px;
    width: 11px;
    z-index: 11;
    cursor: col-resize;
    top: 0;
  }
  .ia-splitter-handle-highlight {
    background-position: right center;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    left: 4px;
    bottom: 0;
    width: 6px;
  }
}

.custom-box-up {
  position: relative;
  .ia-splitter-handle {
    width: 100%;
    position: absolute;
    top: -8px;
    height: 11px;
    z-index: 11;
    cursor: row-resize;
    left: 0;
  }
  .ia-splitter-handle-highlight {
    background-position: center center;
    background-repeat: no-repeat;
    position: absolute;
    top: 4px;
    left: 0;
    bottom: 0;
    height: 6px;
  }
}

// checked选中效果
.tms-checked {
  .ant-checkbox-checked {
    .ant-checkbox-inner {
      background-color: #fff !important;
      &::after {
        border: 2px solid #3279fe !important;
        border-top: 0 !important;
        border-left: 0 !important;
      }
    }
  }
}

// input 带搜索按钮
.tms-search-input{
  width: 100%;
  border-radius: 5px;
  height: 28px;
  .ant-input-group {
    .ant-input-affix-wrapper {
        height: 28px;
        &:not(:last-child) {
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
        } 
        input {
            font-size: 12px;
        }
    }
    .ant-input-affix-wrapper-focused{
        box-shadow: none;
    }
    .ant-input-group-addon{
        border-radius: 5px !important;
        .ant-btn-primary {
          text-shadow:none;
          box-shadow: none;
          border: none !important;
          border-color: #3279fe;
          background-color: #3279fe;
          padding: 4px 10px !important;
        }
        .ant-input-search-button {
            height: 28px;
            border-radius: 0 5px 5px 0 !important;
        }
    }
  }
}

.flexaotu{
  flex: auto;
}

// iconfont图标，蓝色通用按钮
.iconfont-blue-btn {
  display: flex;
  align-items: center;
  height: 28px !important;
  line-height: 28px !important;
  padding: 0 8px;
  border: 1px solid #d7d7d7;
  border-radius: 5px;
  & span:last-of-type {
    font-size: 12px;
    margin-left: 6px;
  }
  // 悬浮时更改为蓝色
  &:hover {
    background-color: #3279fe;
  }
  // 悬浮时图标和字体为白色
  &:hover span {
    color: #fff;
  }
}

// 列表item选中效果
.tms-item-checked {
  border-left: 2px solid #3279fe;
  background-color: #f2f5fd;
}
.tms-item-checked:hover {
  background-color: #f2f5fd !important;
}

// 自定义Dropdown下拉
.pspop-root{
  width: 245px;
  background-color: #fff;
  background-clip: padding-box;
  border-radius: 5px;
  outline: none;
  box-shadow: 0 3px 6px -4px rgb(0 0 0 / 12%), 0 6px 16px rgb(0 0 0 / 8%), 0 9px 28px 8px rgb(0 0 0 / 5%);
  overflow: hidden;
}
.pspop-root .pspop {
  font-size: 12px;
  padding: 5px 8px ;
}
.pspop-root .pspop .pspop-item {
  padding: 5px 0;
}
.pspop-root .pspop .pspop-item:not(:last-child){
  border-bottom: 1px solid #e5e5e5;
}
.pspop-root .pspop .pspop-item .pspop-item-btn {
  cursor: pointer;
  color: #666;
  padding: 0px 7px;
  line-height: 28px;
}
.pspop-root .pspop .pspop-item .pspop-item-btn:hover {
  background-color: #f5f5f5;
  border-radius: 5px;
}
.pspop-root .pspop .pspop-item .pspop-item-btn>.pspop-link{
  width: 100%;
  display: inline-block;
  color: inherit;
}
.pspop-root .pspop .pspop-item .pspop-item-btn>.pspop-link:hover {
  color: inherit;
}

// 官网tabs样式统一调整
.tms-portal-tabs {
  .MuiTabs-scroller {
    .MuiTabs-flexContainer {
      .Mui-selected {
        color: #0077f2;
      }
    }
    .MuiTabs-indicator {
      background-color: #0077f2;
    }
  }
}

// slider通用样式
.portal-slider {
  .slick-dots {
    li {
      height: 2px !important;
      background-color: #999;
      button {
        height: 2px !important;
      }
      button::before {
        display: none;
      }
    }
    .slick-active {
      background-color: #0077f2;
      box-shadow: none;
      button {
        height: 2px !important;
      }
      button::before {
        display: none;
      }
    }
  }
}