/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2025-04-07 14:55:39
 * @LastEditors: Walt <EMAIL>
 * @LastEditTime: 2025-05-13 10:32:31
 * @Description: api_name
 */

const tmsTeamApis = {
  team_120_get_obj_favorite_status: "get_obj_favorite_status",
  team_029_obj_social_op: "obj_social_op",
  team_503_get_attachment_list: "get_attachment_list",
  team_504_get_comment_list: "get_comment_list",
  team_505_get_obj_list: "get_obj_list",
  team_506_upload_file: "upload_file",
  team_509_add_comment: "add_comment",
  team_524_delete_comment: "delete_comment",
  team_525_edit_comment: "edit_comment",
  team_530_get_obj_node_info: "get_obj_node_info",
  team_531_comment_social_op: "comment_social_op",
  team_545_select_table_fields: "select_table_fields",
  team_552_save_front_query: "save_front_query",
  team_553_get_front_query: "get_front_query",
  team_619_user_info: "user_info",
  team_630_user_log_out: "user_log_out",
  team_564_load_obj_list: "load_obj_list",
  team_017_move_obj_node: "move_obj_node",

  team_1301_get_page_part_widget: "get_page_part_widget",
  team_1302_get_price_solution: "get_price_solution",
  team_1401_save_contact_info: "save_contact_info",
  team_1402_save_company_info: "save_company_info",
  team_1201_get_menu_list: "get_menu_list",
  team_1503_get_os_help_list: "get_os_help_list",

  portal_1101_get_news_list: "get_news_list",
  portal_1102_get_news_detail: "get_news_detail",
  team_521_get_security_code: "get_security_code",
  // team_614_get_wx_login_param: "get_wx_login_param",
  // team_616_get_wx_login_state: "get_wx_login_state",
  team_618_user_login: "user_login",
  team_628_get_login_register_method: "get_login_register_method",
  team_649_mp_create_qrcode: "mp_create_qrcode",
  team_650_mp_get_wx_notice_state: "mp_get_wx_notice_state",
  setting_105_get_team_detail: "get_team_detail",
  team_701_get_product_list: "get_product_list",
  team_702_get_team_package_duration_rebate: "get_team_package_duration_rebate",
  team_703_calc_price: "calc_price",
  team_706_get_free_team_count_by_user: "get_free_team_count_by_user",
  team_710_get_team_validity_package: "get_team_validity_package",
  team_734_get_user_coupon_list: "get_user_coupon_list",
  team_735_bind_user_coupon: "bind_user_coupon",
  team_736_unbind_user_coupon: "unbind_user_coupon",
  team_107_get_query_detail: "get_query_detail",
  // team_113_favorite_obj: "favorite_obj",
  // team_114_cancel_favorite_obj: "cancel_favorite_obj",
  // team_511_del_obj_rel: "del_obj_rel",
  team_512_del_attachment: "del_attachment",
  team_544_get_table_field_list: "get_table_field_list",
  setting_202_get_team_allusergrp: "get_team_allusergrp",
  setting_227_save_team_user_tree_width: "save_team_user_tree_width",
  setting_228_get_team_user_tree_width: "get_team_user_tree_width",
  setting_320_get_node_priv: "get_node_priv",
  setting_402_update_attr_seq_no: "update_attr_seq_no",
  setting_404_update_attr_prop_value: "update_attr_prop_value",
  setting_407_get_console_selection_list: "get_console_selection_list",
  setting_408_get_console_attrgrp_props: "get_console_attrgrp_props",
  setting_409_get_team_attrgrp_props: "get_team_attrgrp_props",
  team_501_trash_obj_node: "trash_obj_node",
  team_1303_get_team_demo: "get_team_demo",
  team_571_get_space_valid_user_list: "get_space_valid_user_list",

}

const tmsWorkApis = {
  // fix tmsbug-12249:官网版本历史，看不到详情内容
  portal_1102_get_news_detail: "get_news_detail",
  doc_004_get_tutorial_info: "get_tutorial_info",
  doc_005_get_doc_detail: "get_doc_detail",
  task_306_update_user_daily_task_finish_flag: "update_user_daily_task_finish_flag",
}

const tmsToolsApis = {
  search_003_full_search: "full_search",
}

const tmsSipApis = {
  track_007_edit_issue: "edit_issue",
  track_006_create_issue: "create_issue",
  track_011_get_issue_info: "get_issue_info",
  track_008_get_issue_list: "get_issue_list",
  track_014_issue_excel_export: "issue_excel_export",
  // track_012_get_issue_view: "get_issue_view",
  // track_015_get_phase_view: "get_phase_view",
  track_005_get_subclass_by_issue_node: "get_subclass_by_issue_node",
  // issue_510_save_issue_query: "save_issue_query",
  track_010_get_page_with_id: "get_page_with_id",
  track_009_get_issue_total: "get_issue_total",
  // issue_513_get_issue_partition_detail: "get_issue_partition_detail",
}

//4个包合并后的 api
export default {
  ...tmsTeamApis,
  ...tmsWorkApis,
  ...tmsToolsApis,
  ...tmsSipApis,
}
