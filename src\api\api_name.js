/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2025-04-07 14:55:39
 * @LastEditors: Walt <EMAIL>
 * @LastEditTime: 2025-07-11 18:12:50
 * @Description: api_name
 */

import * as storageConstant from "@/utils/storageConstant";

const tmsTeamApis = {
  team_120_get_obj_favorite_status: "get_obj_favorite_status",
  team_029_obj_social_op: "obj_social_op",
  team_503_get_attachment_list: "get_attachment_list",
  team_504_get_comment_list: "get_comment_list",
  team_505_get_obj_list: "get_obj_list",
  team_506_upload_file: "upload_file",
  team_509_add_comment: "add_comment",
  team_524_delete_comment: "delete_comment",
  team_525_edit_comment: "edit_comment",
  team_530_get_obj_node_info: "get_obj_node_info",
  team_531_comment_social_op: "comment_social_op",
  team_545_select_table_fields: "select_table_fields",
  team_552_save_front_query: "save_front_query",
  team_553_get_front_query: "get_front_query",
  team_619_user_info: "user_info",
  team_630_user_log_out: "user_log_out",
  team_564_load_obj_list: "load_obj_list",
  team_017_move_obj_node: "move_obj_node",

  team_1301_get_page_part_widget: "get_page_part_widget",
  team_1302_get_price_solution: "get_price_solution",
  team_1401_save_contact_info: "save_contact_info",
  team_1402_save_company_info: "save_company_info",
  team_1201_get_menu_list: "get_menu_list",
  team_1503_get_os_help_list: "get_os_help_list",

  portal_1101_get_news_list: "get_news_list",
  portal_1102_get_news_detail: "get_news_detail",
  team_521_get_security_code: "get_security_code",
  team_571_get_space_valid_user_list: "get_space_valid_user_list",
  // team_614_get_wx_login_param: "get_wx_login_param",
  // team_616_get_wx_login_state: "get_wx_login_state",
  team_618_user_login: "user_login",
  team_628_get_login_register_method: "get_login_register_method",
  team_649_mp_create_qrcode: "mp_create_qrcode",
  team_650_mp_get_wx_notice_state: "mp_get_wx_notice_state",
  setting_105_get_team_detail: "get_team_detail",
  team_701_get_product_list: "get_product_list",
  team_702_get_team_package_duration_rebate: "get_team_package_duration_rebate",
  team_703_calc_price: "calc_price",
  team_706_get_free_team_count_by_user: "get_free_team_count_by_user",
  team_710_get_team_validity_package: "get_team_validity_package",
  team_734_get_user_coupon_list: "get_user_coupon_list",
  team_735_bind_user_coupon: "bind_user_coupon",
  team_736_unbind_user_coupon: "unbind_user_coupon",
  team_107_get_query_detail: "get_query_detail",
  // team_113_favorite_obj: "favorite_obj",
  // team_114_cancel_favorite_obj: "cancel_favorite_obj",
  // team_511_del_obj_rel: "del_obj_rel",
  team_512_del_attachment: "del_attachment",
  team_544_get_table_field_list: "get_table_field_list",
  setting_202_get_team_allusergrp: "get_team_allusergrp",
  setting_227_save_team_user_tree_width: "save_team_user_tree_width",
  setting_228_get_team_user_tree_width: "get_team_user_tree_width",
  setting_320_get_node_priv: "get_node_priv",
  setting_402_update_attr_seq_no: "update_attr_seq_no",
  setting_404_update_attr_prop_value: "update_attr_prop_value",
  setting_407_get_console_selection_list: "get_console_selection_list",
  setting_408_get_console_attrgrp_props: "get_console_attrgrp_props",
  setting_409_get_team_attrgrp_props: "get_team_attrgrp_props",
  team_501_trash_obj_node: "trash_obj_node",
  team_1303_get_team_demo: "get_team_demo",

}

const tmsWorkApis = {
  // fix tmsbug-12249:官网版本历史，看不到详情内容
  portal_1102_get_news_detail: "get_news_detail",
  doc_004_get_tutorial_info: "get_tutorial_info",
  doc_005_get_doc_detail: "get_doc_detail",
  task_306_update_user_daily_task_finish_flag: "update_user_daily_task_finish_flag",
}

const tmsToolsApis = {
  search_003_full_search: "full_search",
}

const tmsSipApis = {
  track_007_edit_issue: "edit_issue",
  track_006_create_issue: "create_issue",
  track_011_get_issue_info: "get_issue_info",
  track_008_get_issue_list: "get_issue_list",
  track_014_issue_excel_export: "issue_excel_export",
  // track_012_get_issue_view: "get_issue_view",
  // track_015_get_phase_view: "get_phase_view",
  track_005_get_subclass_by_issue_node: "get_subclass_by_issue_node",
  // issue_510_save_issue_query: "save_issue_query",
  track_010_get_page_with_id: "get_page_with_id",
  track_009_get_issue_total: "get_issue_total",
  // issue_513_get_issue_partition_detail: "get_issue_partition_detail",
}

// 加密，次文件由后端维护提供
const apiNameEncrypt = {
	
	insp_033_get_org_rel_region_list:"s1oxv4xf", //(tms_sip) 组织节点关联的检查区域列表
	insp_061_save_building_region_project:"s1oxv5ge", //(tms_sip) 新建楼栋及区域项目
	track_018_create_issue_partition:"s1oxv63y", //(tms_sip) 创建分区
	insp_flow_079_set_latest_version:"s1oxv6me", //(tms_sip) 设为最新发布
	dbd_002_get_project_issue_list:"s1oxv7rr", //(tms_sip) 获取问题跟踪的项目列表和问题列表
	track_008_get_issue_list:"s1oxv85o", //(tms_sip) 获取issue列表
	insp_002_operate_chkpoint:"s1oxv8be", //(tms_sip) 操作检查项
	insp_039_import_org_tree_region:"s1oxv9so", //(tms_sip) 组织架构负责人
	insp_071_save_insp_processgrp:"s1oxv9yh", //(tms_sip) 新建巡检模块
	track_004_del_issue_group:"s1oxvadp", //(tms_sip) 删除项目/阶段
	insp_022_get_org_tree:"s1oxvcxp", //(tms_sip) 获取组织架构中树列表
	gantt_019_remove_gantt_warning_config:"s1oxvdac", //(tms_sip) 移除预警配置
	insp_096_get_process_task_list:"s1oxvdnv", //(tms_sip) 流程任务节点列表
	gantt_002_update_gantt_project:"s1oxvejm", //(tms_sip) 修改gantt项目
	insp_097_get_region_chklist:"s1oxvf0z", //(tms_sip) 根据巡检项目下的巡检流程 拿取到检查清单项
	track_011_get_issue_info:"s1oxvfze", //(tms_sip) 获取问题详细信息
	insp_016_import_chkpoint_item:"s1oxvgmg", //(tms_sip) 导入检查项字典
	dbd_003_get_team_info:"s1oxvh4u", //(tms_sip) 获取团队信息，人员统计信息，操作权限
	insp_001_save_chklist:"s1oxvhaw", //(tms_sip) 保存检查清单
	insp_026_save_org_region_list:"s1oxvi1x", //(tms_sip) 组织节点关联检查区域
	report_004_get_report_info:"s1oxvikb", //(tms_sip) 获取报表详情
	insp_105_get_form_attr_list:"s1oxvjim", //(tms_sip) 获取表单的字段属性
	gantt_016_get_gantt_import_config_list:"s1oxvjum", //(tms_sip) 获取字段匹配设置
	insp_034_get_usergrp_list:"s1oxvkfx", //(tms_sip) 获取用户组列表
	insp_069_del_region:"s1oxvljv", //(tms_sip) 检查区域删除
	report_002_save_report_condition:"s1oxvlqb", //(tms_sip) 保存报表条件
	insp_103_get_form_detail:"s1oxvn7n", //(tms_sip) 获取表单详情
	insp_009_operate_chklist_rel:"s1oxvo4l", //(tms_sip) 操作检查清单场景（启用禁用、删除）
	insp_027_save_org_level:"s1oxvot1", //(tms_sip) 保存组织架构设置，职级维护
	insp_flow_077_get_flow_version_list:"s1oxvpav", //(tms_sip) 获取流程图版本列表
	track_001_create_issue_project:"s1oxvpsa", //(tms_sip) 创建项目
	insp_065_get_building_region_tree:"s1oxvqj1", //(tms_sip) 获取楼层区域详情
	dbd_007_get_dashboard_info:"s1oxvrf8", //(tms_sip) 获取仪表板详情信息
	insp_021_export_chkpoint_rel:"s1oxvsxg", //(tms_sip) 清单绑定模板导出
	insp_031_move_org_level:"s1oxvtzf", //(tms_sip) 组织层级/职级拖拽排序
	insp_067_export_building_region_tree:"s1oxvvci", //(tms_sip) excel导出
	insp_090_save_insp_project:"s1oxvwid", //(tms_sip) 新建巡检项目
	gantt_014_save_gantt_import_config:"s1oxvwuq", //(tms_sip) 任务导入字段匹配设置
	insp_014_save_chk_project:"s1oxvxpm", //(tms_sip) 新建检查清单管理节点
	insp_101_save_form:"s1oxvymi", //(tms_sip) 新建表单
	team_106_get_query_list:"s1oxvz9g", //(tms_sip) 加载搜索列表
	insp_013_operate_chk:"s1oxvzyp", //(tms_sip) 操作检查清单
	gantt_007_move_gantt_task:"s1oxvarr", //(tms_sip) 移动gantt任务
	insp_011_get_insp_usrgrp_list:"s1oxvb74", //(tms_sip) 巡检小组列表
	insp_053_export_drawing:"s1oxvcee", //(tms_sip) 图纸导出
	insp_010_get_insp_issuegrp_list:"s1oxvcyz", //(tms_sip) 巡检项目列表
	insp_092_get_process_inst_list:"s1oxvdka", //(tms_sip) 获取巡检项目所有流程实例
	gantt_003_operate_gantt_tasks_and_links:"s1oxvegc", //(tms_sip) 创建甘特任务及连接
	insp_064_get_building_tree_with_region_tree:"s1oxvexi", //(tms_sip) 获取所有楼栋详情信息及其区域树
	insp_043_get_drawing_list:"s1oxvfxw", //(tms_sip) 获取图纸库列表
	dbd_004_get_taskgrp_batch_result_list:"s1oxvge9", //(tms_sip) 获取我的批次任务得分列表
	insp_025_get_org_node:"s1oxvhoa", //(tms_sip) 获取组织架构节点
	insp_036_import_org_tree:"s1oxvima", //(tms_sip) 组织架构导入
	insp_095_get_process_history_list:"s1oxvj5i", //(tms_sip) 流程操作历史
	report_003_get_report_tree_list:"s1oxvjfg", //(tms_sip) 获取报表树形列表
	insp_046_restore_drawing_version:"s1oxvkei", //(tms_sip) 图纸版本恢复
	insp_019_list_chkpoint_item:"s1oxvl1x", //(tms_sip) 获取检查项字典
	insp_037_export_org_tree:"s1oxvlux", //(tms_sip) 组织架构模版导出
	insp_017_export_chkpoint_item:"s1oxvmdi", //(tms_sip) 导出检查项字典
	insp_086_deploy_flow:"s1oxvmto", //(tms_sip) 部署电子流
	insp_104_get_form_used_flow:"s1oxvngf", //(tms_sip) 被流程引用列表
	insp_052_operate_drawing_version:"s1oxvoeh", //(tms_sip) 图纸版本操作
	insp_mgt_073_get_insp_template_list:"s1oxvoqq", //(tms_sip) 获取巡检管理下模板列表
	track_019_get_issue_partition_detail:"s1oxvphq", //(tms_sip) 问题跟踪分区明细
	gantt_018_get_gantt_warning_config_list:"s1oxvqzm", //(tms_sip) 获取预警配置
	insp_003_import_chkpoint:"s1oxvrvv", //(tms_sip) 导入检查项
	insp_082_get_flow_tree:"s1oxvses", //(tms_sip) 获取电子流中树
	insp_038_save_org_tree:"s1oxvtxm", //(tms_sip) 批量保存组织架构节点
	team_119_get_all_query_list:"s1oxvuxb", //(tms_sip) 查询所有高级搜索
	gantt_015_remove_gantt_import_config:"s1oxvviu", //(tms_sip) 移除项目字段匹配
	track_006_create_issue:"s1oxvwm9", //(tms_sip) 新增问题
	team_109_run_dynamic_obj_query:"s1oxvx1c", //(tms_sip) 添加/修改某搜索的显示字段
	insp_072_get_insp_processgrp_nodes:"s1oxvxom", //(tms_sip) 根据任意巡检管理下node_id, 获取巡检管理下固定节点id集合
	insp_045_get_drawing_version_list:"s1oxvylv", //(tms_sip) 获取图纸版本列表
	insp_083_get_flow_detail:"s1oxvz8o", //(tms_sip) 获取电子流详情
	report_006_get_report_list:"s1oxvzie", //(tms_sip) 获取报表列表
	gantt_012_get_op_gantt_list:"s1oxw0n4", //(tms_sip) 获取甘特操作历史
	insp_032_del_org_node:"s1oxw12w", //(tms_sip) 组织节点删除
	insp_051_preview_drawing:"s1oxw1zk", //(tms_sip) 图纸预览
	gantt_017_save_gantt_warning_config:"s1oxw2w8", //(tms_sip) 添加预警配置
	track_010_get_page_with_id:"s1oxw3by", //(tms_sip) issue所在页码
	ide_021_clone_project:"s1oxw4i4", //(tms_sip) 
	team_118_edit_advance_query:"s1oxw4kt", //(tms_sip) 编辑高级搜索
	dbd_008_del_dashboard:"s1oxw5m2", //(tms_sip) 删除仪表板
	insp_021_save_org:"s1oxw5ze", //(tms_sip) 新建组织架构
	insp_999_get_all_chk_point:"s1oxw6yr", //(tms_sip) 根据检查项获取所有的检查项列表
	gantt_021_save_gantt_task_sub:"s1oxw7hr", //(tms_sip) 保存子任务
	gantt_005_delete_gantt_tasks_links:"s1oxw7xu", //(tms_sip) 删除任务或连接
	insp_048_get_drawing_region_list:"s1oxw8bl", //(tms_sip) 图纸区域列表
	insp_111_get_insp_kpi_list:"s1oxw9ae", //(tms_sip) 获取巡检任务
	track_007_edit_issue:"s1oxw9w3", //(tms_sip) 维护问题
	dbd_010_update_issue_project:"s1oxwasz", //(tms_sip) 修改我的问题跟踪中呈现的项目
	insp_094_get_process_page_with_id:"s1oxwb4v", //(tms_sip) 通过流程id获取流程图列表页码
	dbd_005_save_dashboard:"s1oxwbcg", //(tms_sip) 保存仪表板条件
	search_003_full_search:"s1oxwcsb", //(tms_sip) 全局搜索
	insp_023_get_org_detail:"s1oxwdm0", //(tms_sip) 获取组织架构图中所有节点信息
	report_005_del_report:"s1oxwe8j", //(tms_sip) 删除报表
	gantt_023_get_gantt_task_sub_list:"s1oxweq6", //(tms_sip) 拿取子任务列表
	report_001_get_aggregate_result:"s1oxwg3f", //(tms_sip) 获取聚合统计结果
	track_020_modify_issue_partition:"s1oxwgnb", //(tms_sip) 编辑问题跟踪分区
	dbd_009_get_issue_project_list:"s1oxwhue", //(tms_sip) 获取我的issue项目列表
	track_002_get_issuegrp_info:"s1oxwiam", //(tms_sip) 查询 grp 详情 
	insp_004_export_chkpoint:"s1oxwiim", //(tms_sip) 导出检查项
	gantt_001_create_gantt_project:"s1oxwjlz", //(tms_sip) 创建gantt项目
	insp_023_batch_import_chklist:"s1oxwjmo", //(tms_sip) 批量导入检查清单
	gantt_006_get_gantt_project:"s1oxwkck", //(tms_sip) 获取gantt项目
	insp_093_get_flow_list:"s1oxwkux", //(tms_sip) 获取流程图列表
	insp_091_get_insp_project_detail:"s1oxwlyg", //(tms_sip) 获取巡检项目详情
	team_533_preview_advance_query:"s1oxwmlw", //(tms_sip) 高级搜索预览
	insp_050_save_drawing_project:"s1oxwnhn", //(tms_sip) 新建图纸管理项目
	insp_028_get_org_level_list:"s1oxwnue", //(tms_sip) 获取组织架构层级及职级
	insp_112_get_insp_stop_task_detail:"s1oxwoct", //(tms_sip) 获取巡检任务停止详情
	insp_081_save_flow:"s1oxwp8y", //(tms_sip) 电子流保存
	dbd_001_get_obj_list:"s1oxwqe2", //(tms_sip) 概览页面中工作报告，文章，在线作图，在线运行列表数据
	insp_066_import_building_region_tree:"s1oxwqkx", //(tms_sip) excel导入
	insp_098_get_project_process:"s1oxwrgw", //(tms_sip) 根据巡检项目下的节点，获取可操作的流程
	insp_063_get_building_tree:"s1oxwrg2", //(tms_sip) 获取楼层中间树列表
	insp_024_save_org_node:"s1oxwteu", //(tms_sip) 保存组织架构节点
	insp_068_bind_region_with_drawing_or_path:"s1oxwus5", //(tms_sip) 楼层绑定图纸或检查区域
	insp_102_get_form_tree_list:"s1oxwvfa", //(tms_sip) 获取表单中树列表
	insp_100_get_region_user_list:"s1oxwvjl", //(tms_sip) 根据region获取到区域的负责人
	team_532_get_subclass_by_node_and_type:"s1oxwwla", //(tms_sip) 根据业务类型获取表单编号
	insp_012_get_region_list:"s1oxwx1f", //(tms_sip) 检查区域列表
	insp_008_save_chklist_rel_detail:"s1oxwxcf", //(tms_sip) 保存检查清单应用场景
	insp_047_deploy_drawing:"s1oxwyqe", //(tms_sip) 图纸发布
	gantt_013_get_op_gantt_list_total:"s1oxwyzk", //(tms_sip) 获取甘特操作历史总数
	insp_044_get_drawing_detail:"s1oxwzel", //(tms_sip) 获取图纸详情
	insp_007_get_chklist_rel_list:"s1oxwao4", //(tms_sip) 检查清单应用场景列表
	track_009_get_issue_total:"s1oxwaqm", //(tms_sip) 获取"工单"总数
	insp_024_get_import_chklist:"s1oxwbs1", //(tms_sip) 
	search_007_get_product_piv:"s1oxwcck", //(tms_sip) 获取产品权限
	dbd_011_get_myhome_dashboard_list:"s1oxwcxn", //(tms_sip) 获取我的(桌面)仪表板列表
	insp_099_get_stop_task_list:"s1oxwdru", //(tms_sip) 获取任务停止所有流程实例
	dbd_006_get_dashboard_tree_list:"s1oxwepx", //(tms_sip) 获取仪表板列表
	ide_024_get_project_info:"s1oxwff3", //(tms_sip) 项目信息
	insp_049_save_region_path:"s1oxwgbv", //(tms_sip) 保存区域路径，绑定检查区域
	oa_001_save_oa_mgt:"s1oxwgsv", //(tms_sip) 新建OA模块
	ide_020_get_project_list:"s1oxwhge", //(tms_sip) 
	insp_015_get_chklist_rel_detail:"s1oxwiaz", //(tms_sip) 检查清单应用场景详情
	ide_040_ide_init_try:"s1oxwiw2", //(tms_sip) 试一试，克隆项目
	gantt_011_get_gantt_tasks_links_list:"s1oxwkao", //(tms_sip) 获取甘特任务及连接列表
	insp_089_get_process_start_form:"s1oxwkkb", //(tms_sip) 获取流程发起表单、获取流程详情
	gantt_020_node_import_gantt:"s1oxwlvi", //(tms_sip) 导入任务字段匹配
	team_115_save_advance_query:"s1oxwmjy", //(tms_sip) 保存高级搜索
	track_005_get_subclass_by_issue_node:"s1oxwmzm", //(tms_sip) 根据issue节点获取自定义表单
	insp_062_save_building_region_tree:"s1oxwnhf", //(tms_sip) 保存楼层及区域
	insp_088_save_deploy_process:"s1oxwone", //(tms_sip) 保存并发布电子流
	task_316_get_insp_kpi_detail_list:"s1oxwp9n", //(tms_sip) 
	insp_035_get_regiongrp_list:"s1oxwpax", //(tms_sip) 获取责任区域列表
	insp_087_submit_process:"s1oxwqbq", //(tms_sip) 发起电子流 或提交流程
	insp_flow_078_del_flow:"s1oxwqqa", //(tms_sip) 删除流程图
	insp_029_del_org_level:"s1oxwrwl", //(tms_sip) 删除组织架构层级及职级
	team_107_get_query_detail:"s1oxwsez", //(tms_sip) 获取搜索详情信息
	insp_030_save_org_project:"s1oxwt0c", //(tms_sip) 新建组织架构管理节点
	insp_020_import_chkpoint_rel:"s1oxwtwl", //(tms_sip) 清单绑定导入
	track_003_update_issue_group:"s1oxwumf", //(tms_sip) 更新项目/类型/阶段组
	track_013_save_issue_query:"s1oxwvpm", //(tms_sip) issue查询保存
	insp_041_save_drawing:"s1oxww4l", //(tms_sip) 保存图纸
	insp_018_operate_chkpoint_item:"s1oxwwsf", //(tms_sip) 保存/编辑检查项字典
	insp_042_get_drawing_tree:"s1oxwyfu", //(tms_sip) 获取图纸库中树列表
	track_014_issue_excel_export:"s1oxwych", //(tms_sip) issue导出
	insp_085_get_flow_form:"s1oxwzos", //(tms_sip) 获取流程图表单
	insp_006_get_chklist_detail:"s1oxwzvm", //(tms_sip) 检查清单详情
	track_012_get_issue_view:"s1oxx0ab", //(tms_sip) 获取issue看板信息
	insp_022_del_chkpoint_item:"s1oxx1hu", //(tms_sip) 删除检查字典项
	insp_084_get_flow_usergrp:"s1oxx22p", //(tms_sip) 获取流程图候选成员组
	insp_task_126_get_task_completion_detail:"s1oxx2qh", //(tms_sip) 获取任务发现项详情
	insp_task_125_get_task_partition:"s1oxx3el", //(tms_sip) 获取批次任务过滤条件
	spot_10_get_spot_region_chklist:"s1oxx3mz", //(tms_sip) 支持根据检查区域，检查模版，检查周期过滤
	track_021_get_issue_status_list:"s1oxx4pu", //(tms_sip) 根据id获取issue状态
	insp_task_124_get_completion_config_list:"s1oxx5ff", //(tms_sip) 获取完成率设置列表和详情
	insp_task_120_save_task_stop_flow:"s1oxx68p", //(tms_sip) 任务停止模版设置后保存
	insp_task_121_get_completion_dimension:"s1oxx6im", //(tms_sip) 获取完成率统计维度列表
	spot_07_import_spot_chklist:"s1oxx7g5", //(tms_sip) 自定义检查清单导入
	spot_03_save_spot_chklist:"s1oxx7ty", //(tms_sip) 保存自定义点检清单
	insp_task_122_get_org_region_list:"s1oxx92q", //(tms_sip) 获取部门，责任区域和负责人列表
	insp_mgt_075_check_insp_data:"s1oxxath", //(tms_sip) 现场巡检向导-校验一下
	insp_task_127_get_completion_table_list:"s1oxxauw", //(tms_sip) 获取完成率表格设置的数据
	insp_026_get_import_task_status:"s1oxxbxg", //(tms_sip) 用于前端轮询导入状态的接口
	spot_04_get_spot_chklist_detail:"s1oxxc5z", //(tms_sip) 获取自定义点检清单详情
	spot_02_get_chklist_tpl_list:"s1oxxcoh", //(tms_sip) 获取清单模版列表
	insp_flow_105_restore_status_config:"s1oxxdwa", //(tms_sip) 移动端主板状态恢复初始化设置
	insp_115_get_usergrp_and_org_level:"s1oxxe9f", //(tms_sip) 获取自定义组和组织架构
	insp_task_119_get_task_stop_template:"s1oxxegs", //(tms_sip) 获取任务停止流程模版用户组
	insp_040_save_insp_usergrp:"s1oxxhph", //(tms_sip) 创建巡检用户组
	spot_05_del_chklist_tpl:"s1oxxi5h", //(tms_sip) 自定义检查清单模板删除
	insp_005_get_chklist_tree:"s1oxxire", //(tms_sip) 检查清单中间树列表
	insp_116_save_grp_usergrp:"s1oxxjo1", //(tms_sip) 保存自定义组
	insp_117_get_grp_usergrp_detail:"s1oxxkfd", //(tms_sip) 获取自定义组详情
	gantt_024_save_gantt_mapping:"s1oxxkq9", //(tms_sip) 保存任务字段匹配
	spot_21_operate_form_layout:"s1oxxlnd", //(tms_sip) 修改表单页面布局
	spot_22_get_form_layout:"s1oxxm2j", //(tms_sip) 获取表单页面布局
	spot_24_get_frequency_by_chklist:"s1oxxmjg", //(tms_sip) 根据检查类型，获取检查频率
	insp_flow_103_get_insp_project_list:"s1oxxnaw", //(tms_sip) 获取巡检项目(团队)下所有分区列表
	insp_task_118_get_insp_task_statistics:"s1oxxp9o", //(tms_sip) 
	insp_mgt_074_export_insp_all_tpl:"s1oxxpub", //(tms_sip) 导出巡检模块所有Excel模版
	insp_task_123_save_completion_config:"s1oxxqru", //(tms_sip) 保存完成率配置
	insp_flow_108_get_duplicate_list_of_chkpoint:"s1oxxqoy", //(tms_sip) 获取发现项重复历史
	spot_08_export_spot_chklist:"s1oxxrfe", //(tms_sip) 自定义检查清单导出
	insp_106_get_init_form_list:"s1oxxs4i", //(tms_sip) 获取关联一个发起表单的所有表单字段列表
	insp_flow_104_get_proc_button_form:"s1oxxsba", //(tms_sip) 获取流程中操作按钮对应表单详情
	insp_chk_027_get_chk_point:"s1oxxt1b", //(tms_sip) 获取检查项信息
	gantt_025_get_gantt_mapping:"s1oxxtmh", //(tms_sip) 获取任务字段匹配
	spot_23_save_chkpoint_record:"s1oxxudx", //(tms_sip) 检查项打钩
	oa_002_save_oa_form:"s1oxxuwh", //(tms_sip) 新建OA表单
	tpm_001_save_tpm_mgt:"s1oxxvqi", //(tms_sip) 新建tpm模块
	tpm_006_get_customer_list:"s1oxxwsx", //(tms_sip) 获取客户信息列表，包含经纬度
	spot_01_save_chklist_tpl:"s1oxxx6r", //(tms_sip) 保存清单模版
	tpm_004_save_subclass_info:"s1oxxxgb", //(tms_sip) 保存表单信息，客户或设备
	insp_flow_076_get_flow_activity_detail:"s1oxxyni", //(tms_sip) 获取流程图进行解析，返回节点列表
	oa_003_get_oa_form:"s1oxxyyd", //(tms_sip) 获取OA表单
	tpm_012_batch_import_scenes_or_customers:"s1oxxzba", //(tms_sip) 批量导入导入客户/设备数据
	tpm_009_import_scenes_or_customers:"s1oxxa9m", //(tms_sip) 导入客户/设备数据
	tpm_005_get_all_scenes_or_customers:"s1oxxaom", //(tms_sip) 获取全部客户/设备列表
	insp_107_get_project_process_partition:"s1oxxbm1", //(tms_sip) 获取项目流程和分区
	tpm_010_export_scenes_or_customers:"s1oxxbxw", //(tms_sip) 导出客户/设备数据
	tpm_008_save_region_scene:"s1oxxdwx", //(tms_sip) 保存客户设备关系
	oa_004_get_process_history_detail:"s1oxxe6o", //(tms_sip) 获取发现项历史详情(右弹呈现)
	tpm_011_get_tpm_customer_questions:"s1oxxexs", //(tms_sip) 获取客户a问卷列表
	tpm_002_get_subclass_obj_list:"s1oxxfbk", //(tms_sip) 获取表单对象列表
	insp_flow_109_get_team_project_flow:"s1oxxfjo", //(tms_sip) 获取团队下项目流程
	tpm_003_save_subclass_obj_list:"s1oxxgkm", //(tms_sip) 编辑保存表单对象列表
	tpm_007_save_customer_latlng:"s1oxxhwv", //(tms_sip) 保存客户经纬度
	team_510_add_obj_rel:"s1oxplyp", //(tms_team) 添加对象关联
	team_999_export_user:"s1oxpo1b", //(tms_team) excel导出(可用于模板下载)
	team_552_save_front_query:"s1oxpoma", //(tms_team) 保存页面查询
	uc_025_merge_user_by_openid:"s1oxppnm", //(tms_team) 账号合并，将扫码的（wx、qq）账号合并到当前的（手机）账号
	team_016_update_node_op:"s1oxpqf9", //(tms_team) 更新节点(改名，改颜色，加tag色等)
	team_531_comment_social_op:"s1oxpr0q", //(tms_team) 评论点赞，倒赞
	team_015_create_folder_pkg:"s1oxprb1", //(tms_team) 新建 文件夹/对象包
	team_723_upload_log:"s1oxpsme", //(tms_team) 上传日志信息（主要供前端上传js异常日志）
	team_733_save_user_products:"s1oxpsw0", //(tms_team) 给用户批量绑定产品列表
	team_1301_get_page_part_widget:"s1oxpty6", //(tms_team) 获取页面模块及组件
	team_003_create_space:"s1oxpu8z", //(tms_team) 创建空间
	setting_114_get_team_import_setting:"s1oxpuml", //(tms_team) 获取团队导入设置
	team_577_submit_invoice_request:"s1oxpvcf", //(tms_team) 保存发票申请
	team_637_set_user_pwd:"s1oxpvqe", //(tms_team) 设置用户初始密码
	setting_234_get_team_mbr_user_info:"s1oxpx2j", //(tms_team) 获取个人(团队内)信息
	team_643_qq_auth_bind:"s1oxpxwq", //(tms_team) (qq)绑定授权链接
	setting_123_change_team_status:"s1oxpyfa", //(tms_team) 修改工作区禁用/启用状态
	setting_311_del_role_ug:"s1oxpz3m", //(tms_team) 删除角色成员(组)
	setting_326_get_node_priv_user:"s1oxpzjn", //(tms_team) 获取节点上有权限的用户信息
	team_032_get_share_encryption_status:"s1oxpaxp", //(tms_team) 获取当前分享链接加密状态
	team_592_get_sched_cron_task_list:"s1oxpbzg", //(tms_team) 获取定时计划列表
	team_001_get_recent_team_list:"s1oxpbxg", //(tms_team) 获取近期访问的团队列表
	team_502_del_trash_node:"s1oxpcvd", //(tms_team) 物理删除回收站节点
	setting_227_save_team_user_tree_width:"s1oxpdjf", //(tms_team) 团队成员树宽度
	team_1503_get_os_help_list:"s1oxpe2r", //(tms_team) 操作系统内的帮助文档
	setting_111_save_team_subscribe_setting:"s1oxpegh", //(tms_team) 保存团队订阅设置
	team_504_get_comment_list:"s1oxpfct", //(tms_team) 获取评论列表
	team_565_create_shortcut:"s1oxpg7d", //(tms_team) 创建快捷方式
	team_712_toggle_product:"s1oxpgfa", //(tms_team) 启用/禁用产品
	setting_126_get_setting_teams:"s1oxph4t", //(tms_team) 获取全部团队列表
	setting_208_toggle_user:"s1oxpks6", //(tms_team) 启用/禁用一个成员
	team_567_get_dash_shortcut_list:"s1oxpl1b", //(tms_team) 获取我的桌面节点下的快捷方式列表
	uc_003_get_account_info:"s1oxpmgi", //(tms_team) 获取账户安全信息，密码，手机等
	team_111_del_obj_txt_tag:"s1oxpmkf", //(tms_team) 删除某个对象设定的话题
	team_539_get_task_src_obj_list:"s1oxpnly", //(tms_team) 系统订阅内容列表
	team_573_op_objtype_tour:"s1oxpo1p", //(tms_team) 新手指引的点击操作
	team_509_add_comment:"s1oxpokx", //(tms_team) 添加评论
	team_503_restore_trash_node:"s1oxppwh", //(tms_team) 恢复回收站对象
	team_553_get_front_query:"s1oxpqks", //(tms_team) 获取页面查询
	team_035_merchant_check_in_request:"s1oxprpv", //(tms_team) 发起商户申请
	setting_230_get_invite_list:"s1oxps01", //(tms_team) 获取邀请历史
	team_628_get_login_register_method:"s1oxpse0", //(tms_team) 获取登录/注册平台支持的方式
	team_559_set_notification_remind_later:"s1oxptne", //(tms_team) 设置消息稍后提醒
	team_512_del_attachment:"s1oxpttw", //(tms_team) 删除附件
	setting_402_update_attr_seq_no:"s1oxpuvj", //(tms_team) 变更attr显示排序(上移/下移)
	team_578_get_notify:"s1oxpv81", //(tms_team) 获取系统通知 + 站内信
	uc_002_save_person_info:"s1oxpvgk", //(tms_team) 保存个人资料
	team_734_get_user_coupon_list:"s1oxpx2s", //(tms_team) 获取用户优惠券列表
	team_530_get_obj_node_info:"s1oxpxjk", //(tms_team) 获取节点详情
	team_564_load_obj_list:"s1oxpylt", //(tms_team) 获取某个节点下的对象列表 (权限过滤)
	team_722_get_order_list_by_product_id:"s1oxpzam", //(tms_team) 获取当前团队购买过的套餐功能（含过期的）
	setting_235_set_team_mbr_user:"s1oxpzho", //(tms_team) 设置个人(团队内)信息
	team_614_get_wx_login_param:"s1oxq069", //(tms_team) 获取微信登录参数(用于二维码生成)
	team_589_get_sched_msg_tpl_list:"s1oxq0tc", //(tms_team) 获取文案模版列表
	setting_330_set_space_objtype_toggle:"s1oxq1su", //(tms_team) 设置空间某个角色“启用/禁用”产品
	team_554_get_inner_obj_rel_list:"s1oxq2do", //(tms_team) 编辑器内对象关联列表
	team_551_get_be_reled_obj_list:"s1oxq3as", //(tms_team) 被对象关联列表
	setting_302_get_role_detail:"s1oxq3fm", //(tms_team) 获取主(子)团队某角色详情
	setting_225_update_user_info:"s1oxq4ss", //(tms_team) 维护团队成员的基本资料
	team_523_save_sched_task_src:"s1oxq4sb", //(tms_team) 保存订阅消息
	setting_218_invite_users_via_emails:"s1oxq5p2", //(tms_team) 手机批量邀请用户
	setting_239_set_team_mbr_user_preferences:"s1oxq69r", //(tms_team) 设置用户个性化
	setting_205_get_space_nogrp:"s1oxq6ak", //(tms_team) 获取空间"未分组"组成员列表
	team_596_get_sched_task_src_detail:"s1oxq7g8", //(tms_team) 订阅内容详情
	team_640_get_wx_bind_param:"s1oxq7ma", //(tms_team) 获取微信绑定参数( 用于二维码生成 )
	setting_110_get_team_subscribe_setting:"s1oxq8n6", //(tms_team) 团队订阅设置
	team_508_get_rel_obj:"s1oxq9hl", //(tms_team) 查看对象关联
	team_581_get_mbr_node_preference:"s1oxqaek", //(tms_team) 获取节点个人设置
	team_037_get_share_link_list:"s1oxqb04", //(tms_team) 获取外链分享列表
	setting_109_change_space_status:"s1oxqc8n", //(tms_team) 禁用/启用，删除空间
	setting_105_get_team_detail:"s1oxqcgg", //(tms_team) 获取团队详情
	team_571_get_space_valid_user_list:"s1oxqdni", //(tms_team) 获取空间有效的人员列表
	team_017_move_obj_node:"s1oxqe2f", //(tms_team) 将某节点拖拽到另一个节点下
	setting_224_verification_invite_url:"s1oxqfjm", //(tms_team) 邀请链接的验证
	team_122_get_tag_obj_list:"s1oxqfpc", //(tms_team) 获取话题下的对象列表
	team_706_get_free_team_count_by_user:"s1oxqgx4", //(tms_team) 获取团队规模最小值
	setting_217_add_user_by_invite_url:"s1oxqhcs", //(tms_team) 用户点击邀请，响应邀请
	setting_228_get_team_user_tree_width:"s1oxqhoy", //(tms_team) 获取团队成员树宽度信息
	setting_236_apply_to_join_team:"s1oxqidr", //(tms_team) 用户申请加入团队(空间)
	setting_215_2_get_team_usrgrps_and_users:"s1oxqjk9", //(tms_team) 获取主(子)团队成员组及其成员列表
	uc_024_merge_user_by_mobile:"s1oxqjna", //(tms_team) 账号合并，将当前（wx、qq）账号合并到指定的（手机）账号
	team_704_submit_order:"s1oxqkro", //(tms_team) 团队套餐下单(产生付款二维码)
	team_579_get_invoice_list:"s1oxqlf0", //(tms_team) 获取开票历史
	setting_204_get_team_nogrp:"s1oxqlop", //(tms_team) 获取团队"未分组"组成员列表
	team_013_search_obj_tree:"s1oxqmlu", //(tms_team) 获取对象树 (权限过滤)
	setting_332_set_space_mbr_setting:"s1oxqmwp", //(tms_team) 设置空间成员保密信息
	setting_409_get_team_attrgrp_props:"s1oxqnwn", //(tms_team) 获取attr组(tab)的所有property - 团队端
	team_558_update_notification_status:"s1oxqodw", //(tms_team) 更新通知状态
	setting_210_toggle_usergrp:"s1oxqoqd", //(tms_team) 启用/禁用成员组
	team_526_save_notify:"s1oxqpu6", //(tms_team) 站内信保存(供各模块内部调用)
	setting_203_get_space_allusergrp:"s1oxqq3j", //(tms_team) 获取空间"全部成员"组成员列表
	team_590_get_sched_msg_tpl_detail:"s1oxqqfm", //(tms_team) 获取文案模版详情
	team_735_bind_user_coupon:"s1oxqrmx", //(tms_team) 绑定用户输入的优惠码
	setting_124_delete_team:"s1oxqruq", //(tms_team) 解散团队
	team_571_del_notify:"s1oxqtua", //(tms_team) 删除消息
	team_036_get_node_ctx_options:"s1oxqutu", //(tms_team) 获取单个节点的可操作菜单项
	team_029_obj_social_op:"s1oxqv14", //(tms_team) 点赞/关注 (及取消操作)
	team_588_save_sched_msg_tpl:"s1oxqvdm", //(tms_team) 保存系统订阅文案模版
	team_709_get_team_order_list:"s1oxqvzd", //(tms_team) 获取团队订单
	setting_331_get_space_mbr_setting:"s1oxqwzn", //(tms_team) 获取空间成员保密设置信息
	team_538_get_cron_test_data:"s1oxqxki", //(tms_team) 获取cron表达式前十次运行时间
	team_544_get_table_field_list:"s1oxqy3z", //(tms_team) 获取选择字段，如果未设置，则取默认的
	setting_231_set_invite_overdue:"s1oxqywg", //(tms_team) 设置邀请链接无效（过期）
	team_645_get_qq_bind_state:"s1oxqz1l", //(tms_team) 获取qq绑定(扫码)状态
	uc_007_bind_wx_qq:"s1oxqzml", //(tms_team) 绑定、解绑 微信/qq
	team_642_get_wx_bind_state:"s1oxqan3", //(tms_team) 获取微信绑定(扫码)状态
	team_020_create_space_folder:"s1oxqavk", //(tms_team) 在空间设置，角色权限Tab下面创建文件夹
	team_616_get_wx_login_state:"s1oxqbdz", //(tms_team) 获取微信登录(扫码)状态
	team_576_save_mbr_preference:"s1oxqcat", //(tms_team) 保存成员偏好设置
	setting_221_get_team_tree:"s1oxqcj0", //(tms_team) 获取团队树
	team_591_save_sched_cron_task:"s1oxqdob", //(tms_team) 保存定时计划
	setting_232_get_across_team_spaces:"s1oxqe1r", //(tms_team) 跨团队邀请团队及空间列表获取
	setting_328_save_user_node_privs:"s1oxqewe", //(tms_team) 设置空间成员对象权限
	setting_233_invite_cross_team_member:"s1oxqfkt", //(tms_team) 跨团队邀请成员
	setting_214_create_usergrp:"s1oxqgmj", //(tms_team) 创建一个空成员组
	team_638_user_bind_mobile_email:"s1oxqhci", //(tms_team) 用户绑定手机/邮箱
	// news_002_get_news_detail:"s1oxqiuw", //(tms_team) 获取新闻详情
	team_575_get_os_version:"s1oxqjev", //(tms_team) 获取前端兼容的最低版本
	team_586_list_my_like:"s1oxqknc", //(tms_team) 我的点赞
	team_619_user_info:"s1oxqkuw", //(tms_team) 获取用户信息
	setting_128_save_customized_sub_domain:"s1oxqly0", //(tms_team) 保存自定义域名
	setting_333_set_user_space_roles:"s1oxqlys", //(tms_team) 管理员给某成员，批量分配空间及角色列表
	team_103_get_recent_update_by_allusers:"s1oxqmds", //(tms_team) 近期更新-所有更新
	team_545_select_table_fields:"s1oxqnhc", //(tms_team) 选择字段
	team_580_get_desktop_settings:"s1oxqnd7", //(tms_team) 获取桌面设置
	team_555_get_obj_rel_link_info:"s1oxqoji", //(tms_team) 对象关联信息获取
	team_736_get_team_order_info:"s1oxqpgj", //(tms_team) 个人中心- 获取订单详情
	team_595_del_sched_cron_task:"s1oxqqgr", //(tms_team) 删除定时计划
	team_570_get_share_user_list:"s1oxqqmy", //(tms_team) 共享人员列表接口
	setting_216_generate_invite_url:"s1oxqrlw", //(tms_team) 生成邀请链接
	team_593_get_sched_cron_task:"s1oxqs17", //(tms_team) 获取定时计划详情
	team_556_get_up_obj_nodes:"s1oxqsxy", //(tms_team) 获取所有父节点
	setting_113_save_team_setting:"s1oxqtbl", //(tms_team) 获取团队设置
	team_537_get_sched_task_src_list:"s1oxqtpo", //(tms_team) 订阅内容列表
	setting_107_save_team_email:"s1oxquw8", //(tms_team) 保存团队邮箱设置
	team_101_get_recent_visit_popular:"s1oxqvvq", //(tms_team) 近期访问-热门访问
	setting_305_rename_role:"s1oxqw9l", //(tms_team) 主(子)团队角色重命名
	team_030_obj_share_link:"s1oxqwvh", //(tms_team) 生成分享短链
	team_513_get_import_obj_list:"s1oxqx4t", //(tms_team) 甘特图导入对象列表
	team_110_add_obj_txt_tag:"s1oxqxka", //(tms_team) 对象增加话题
	setting_209_delete_user:"s1oxqyz2", //(tms_team) 删除一个用户
	setting_215_add_usergrp_user:"s1oxqzfg", //(tms_team) 往成员组添加成员
	team_701_get_product_list:"s1oxqzzd", //(tms_team) 获取团队版本套餐
	team_711_get_team_product_list:"s1oxr0io", //(tms_team) 产品管理-产品列表
	team_514_get_trash_node_list:"s1oxr19r", //(tms_team) 获取回收站对象列表
	team_022_create_sider_folder:"s1oxr1bc", //(tms_team) 创建中间树上的文件夹
	setting_229_save_team_user_config:"s1oxr2l1", //(tms_team) 团队成员配置
	setting_211_delete_usergrp:"s1oxr2sa", //(tms_team) 删除成员组
	team_525_edit_comment:"s1oxr3zb", //(tms_team) 编辑评论
	setting_320_get_node_priv:"s1oxr4b3", //(tms_team) 获取节点权限
	team_521_get_securitycode:"s1oxr4ov", //(tms_team) 获取手机/邮箱验证码
	setting_240_get_team_mbr_user_preferences:"s1oxr5tx", //(tms_team) 获取用户个性化
	setting_219_signoff_pending_invite_user:"s1oxr5lj", //(tms_team) 审批(通过或拒绝)待加入的成员
	team_527_get_notify_unread_num:"s1oxr727", //(tms_team) 获取(我的)站内信未读数
	team_112_get_my_favorite:"s1oxr7cz", //(tms_team) 我的收藏对象列表
	setting_108_send_test_email:"s1oxr8mc", //(tms_team) 发送测试邮件
	team_568_save_share_user:"s1oxr8ir", //(tms_team) 保存共享人员
	team_732_save_product_users:"s1oxr9ni", //(tms_team) 给产品指定授权用户列表
	team_587_list_my_mention:"s1oxr9uw", //(tms_team) 我提及@的
	team_501_trash_obj_node:"s1oxrafc", //(tms_team) 删除节点(放回收站)
	setting_408_get_console_attrgrp_props:"s1oxrbgo", //(tms_team) 获取attr组(tab)的所有property - 团队设置端
	setting_407_get_console_selection_list:"s1oxrcmk", //(tms_team) 获取下拉字典
	setting_212_rename_usergrp:"s1oxrcjk", //(tms_team) 成员组重命名
	setting_301_get_roles:"s1oxrdus", //(tms_team) 获取主(子)团队角色列表
	team_019_get_space_tree:"s1oxrecn", //(tms_team) 获取子空间对象树
	setting_329_get_space_objtype_toggle:"s1oxrfqm", //(tms_team) 获取空间某个角色“可启用/禁用”产品列表
	team_503_get_attachment_list:"s1oxrfr5", //(tms_team) 获取附件列表
	team_594_del_sched_msg_tpl:"s1oxrgxi", //(tms_team) 删除文案模版
	uc_021_user_security_verify:"s1oxrhej", //(tms_team) 修改密码前，先安全验证
	team_031_get_share_link_info:"s1oxrhx7", //(tms_team) 换取生成分享页get_admin_team_list面 必需的信息
	team_617_bind_wx_qq_relation_user:"s1oxrirn", //(tms_team) 微信与用户信息(手机号、邮箱)绑定
	team_611_user_register:"s1oxrj6c", //(tms_team) 用户注册
	// news_001_get_news_list:"s1oxrjv5", //(tms_team) 获取新闻列表
	setting_327_get_team_space_admin:"s1oxrknu", //(tms_team) 判断登录人员是团队空间的管理员
	setting_207_get_usergrp_users:"s1oxrlrj", //(tms_team) 获取主(子)工作区某个分组下的成员列表
	team_113_favorite_obj:"s1oxrlwl", //(tms_team) 收藏
	team_581_get_desktop_node_list:"s1oxrmcd", //(tms_team) 桌面-对象节点
	team_580_get_review_comment_list:"s1oxrpgd", //(tms_team) 获取审核评论列表
	setting_040_get_kpi_selection_list:"s1oxrq3f", //(tms_team) 获取批次任务下拉字典
	team_731_get_product_auth_users:"s1oxrqab", //(tms_team) 获取产品的授权用户列表
	uc_001_get_person_info:"s1oxrr7l", //(tms_team) 获得个人资料
	team_520_get_obj_txt_tag_list:"s1oxrrfr", //(tms_team) 对象获取话题列表
	setting_307_add_role_ug:"s1oxrsge", //(tms_team) 新增角色成员(组)
	team_1303_get_team_demo:"s1oxrsq4", //(tms_team) 获取示例团队信息
	team_736_unbind_user_coupon:"s1oxru8b", //(tms_team) 解绑优惠券
	team_583_remove_demo_team_user:"s1oxrudz", //(tms_team) 移除正常用户访问demo团队的记录
	setting_404_update_attr_prop_value:"s1oxrw1u", //(tms_team) 保存attr节点prop值
	team_114_cancel_favorite_obj:"s1oxrwpf", //(tms_team) 取消收藏
	team_505_get_obj_list:"s1oxrxlm", //(tms_team) 获取关联对象列表
	team_1401_save_contact_info:"s1oxrxss", //(tms_team) 联系我们
	team_585_get_team_share_list:"s1oxryoh", //(tms_team) 获取共享对象接口
	setting_206_get_pending_invite_users:"s1oxrzuo", //(tms_team) 获取主(子)工作区申请加入的用户列表
	team_563_set_notify_auto_popup:"s1oxragc", //(tms_team) 设置团队成员通知是否自动弹出
	setting_201_get_team_usergrps:"s1oxratz", //(tms_team) 获取团队成员组列表
	team_557_get_sys_notification_reminders:"s1oxrbwx", //(tms_team) 获取全部系统通知提醒列表
	setting_304_create_role:"s1oxrcet", //(tms_team) 创建主(子)普通角色
	team_705_get_order_status:"s1oxrd7n", //(tms_team) 团队套餐付款状态查询(扫码回调)
	setting_310_get_obj_node_privs:"s1oxrddx", //(tms_team) 查看某个对象节点的所有角色及权限
	setting_309_update_role_node_priv:"s1oxrehd", //(tms_team) 更新角色的节点权限
	setting_202_get_team_allusergrp:"s1oxreq2", //(tms_team) 获取"全部成员"组成员列表
	team_618_user_login:"s1oxrfex", //(tms_team) 用户登录
	team_598_get_sched_task_page_by_id:"s1oxrgds", //(tms_team) 获取订阅数据所在页码
	setting_112_get_team_setting:"s1oxrgya", //(tms_team) 获取团队设置
	team_579_save_desktop_settings:"s1oxrhwm", //(tms_team) 保存桌面设置
	team_639_unbind_user_wx_qq:"s1oxri7u", //(tms_team) 微信/qq的解绑
	team_562_get_my_function:"s1oxriaf", //(tms_team) 我的桌面"与我相关"的功能
	team_524_delete_comment:"s1oxrjxb", //(tms_team) 删除评论
	team_630_user_log_out:"s1oxrjyh", //(tms_team) 退出登录
	team_034_get_merchant_detail:"s1oxrkwp", //(tms_team) 获取商户详情
	setting_321_get_user_node_priv:"s1oxrlbo", //(tms_team) 获取成员权限列表
	team_540_del_sched_task_src:"s1oxrlom", //(tms_team) 删除系统订阅消息
	team_511_del_obj_rel:"s1oxrmok", //(tms_team) 删除对象关联
	setting_324_get_node_lock_status:"s1oxrn2x", //(tms_team) 获取锁状态
	team_506_upload_file:"s1oxrneq", //(tms_team) 上传文件
	setting_101_save_team_detail:"s1oxrobm", //(tms_team) 保存团队设置
	setting_106_get_team_email:"s1oxropl", //(tms_team) 获取团队邮箱设置
	team_560_get_my_watch:"s1oxrpmy", //(tms_team) 我的关注对象列表
	setting_308_update_role_priv:"s1oxrqwk", //(tms_team) 更新角色权限
	setting_213_remove_usergrp_user:"s1oxrrpq", //(tms_team) 主(子)工作区将某个成员从成员组移除
	setting_306_delete_role:"s1oxrtuc", //(tms_team) 删除主(子)团队的某个角色
	team_535_get_recent_visit_by_docs:"s1oxrtxa", //(tms_team) 我的文档库 - 文档近期访问记录
	team_582_get_product_var_objType:"s1oxrut2", //(tms_team) 获取团队下启用产品的对象
	setting_102_change_team_creator:"s1oxrv0z", //(tms_team) 团队移交
	team_529_update_notify_unread:"s1oxrvhe", //(tms_team) 更新站内信未读标识
	team_597_rename_sched_task_src:"s1oxrw0b", //(tms_team) 订阅重命名
	team_014_load_obj_tree:"s1oxrwxp", //(tms_team) 获取对象树 (权限过滤)
	team_120_get_obj_favorite_status:"s1oxrxms", //(tms_team) 获取对象收藏、关注、点赞
	team_572_get_objtype_tour:"s1oxrxww", //(tms_team) 获取（功能模块的）新手指引
	team_121_get_tag_tree:"s1oxrykb", //(tms_team) 获取我话题签列表
	team_541_change_sched_task_src_status:"s1oxrzqn", //(tms_team) 修改系统订阅禁用/启用状态
	team_528_get_notify_list:"s1oxs0ky", //(tms_team) 获取(我的)站内信列表
	team_627_user_pwd_reset:"s1oxs1an", //(tms_team) 个人中心,用户密码重置
	setting_129_view_guide_page:"s1oxs1zm", //(tms_team) 查看引导页
	team_581_operate_review_comment:"s1oxs2xi", //(tms_team) 审核评论
	team_703_calc_price:"s1oxs3x3", //(tms_team) 团队套餐价格计算
	// team_635_qq_auth_login:"s1oxs4ef", //(tms_team) (qq)登录授权链接
	team_542_exec_sched_task_src:"s1oxs5qj", //(tms_team) 订阅任务立即执行
	setting_334_apply_authorization:"s1oxs6pq", //(tms_team) 申请授权
	team_039_cut_obj_node:"s1oxs87t", //(tms_team) 剪切粘贴
	team_041_update_share_status:"s1oxsa2n", //(tms_team) 更改分享状态
	team_635_qq_auth_login:"s1oxsabr", //(tms_team) (qq)登录授权链接
	team_649_mp_create_qrcode:"s1oxsayq", //(tms_team) 生成微信公众号关注二维码
	team_650_mp_get_wx_notice_state:"s1oxsbmg", //(tms_team) 获取微信公众号(扫码)状态
	team_599_get_data_dictionary:"s1oxse2p", //(tms_team) 获取消息推送的触发事件数据字典
	setting_223_user_quit_team:"s1oxye1m", //(tms_team) 保留旧 request_url
	browser_001_save_browser:"s1oxtcni", //(tms_tools) 新建/编辑内置浏览器(站点)
	cdisk_051_get_client_cookie_url:"s1oxtcqu", //(tms_tools) 获取cookie
	mock_057_batch_auto_link_api:"s1oxtdcz", //(tms_tools) 代理调用批量关联api
	cdb_305_get_db_tpl:"s1oxted6", //(tms_tools) 获取系统提供的sql模板（tpl）
	local_001_api_invoke:"s1oxtehu", //(tms_tools) 接口真实调用
	mock_053_get_user_var:"s1oxtfvk", //(tms_tools) 获取用户变量
	mock_006_get_api_rule_info:"s1oxtgba", //(tms_tools) 获取用例详情
	mock_069_get_api_window_list:"s1oxtiin", //(tms_tools) 获取窗口列表
	mock_055_get_api_srv_list:"s1oxtjoo", //(tms_tools) 获取api mock/代理服务设置
	mock_070_del_api_window:"s1oxtju3", //(tms_tools) 删除窗口
	mock_051_get_exec_script:"s1oxtknc", //(tms_tools) 获取前置,后置脚本
	cdisk_041_admin_copyuser:"s1oxtl5v", //(tms_tools) 拷贝客户
	mock_035_get_api_srv_info:"s1oxtlww", //(tms_tools) 获取apiSrv详情
	disk_019_client_weblink_update:"s1oxtmtk", //(tms_tools) 更新下载外链选项
	disk_024_client_zip:"s1oxtmxq", //(tms_tools) 压缩文件
	cdb_112_get_db_list:"s1oxtnqc", //(tms_tools) 获取某个conn下的db列表
	cdb_307_get_db_window:"s1oxto25", //(tms_tools) 获取用户tab（windows）窗体列表
	mock_075_ignore_api_call:"s1oxtoks", //(tms_tools) 忽略api/api_call
	mock_056_modify_api_call_desc:"s1oxtps3", //(tms_tools) 修改api_call_desc
	cdisk_050_admin_get_random_password:"s1oxtqqn", //(tms_tools) 获取随机密码
	cdb_111_get_connection_info:"s1oxtr58", //(tms_tools) 查询某个数据库链接信息
	cdisk_027_admin_get_account_list:"s1oxtrgy", //(tms_tools) 获取云盘普通账号列表(不含默认账号)
	cdb_302_get_table_view_function_event_procedure_list:"s1oxtsqk", //(tms_tools) 获取数据库对象
	cdisk_004_admin_adduser:"s1oxtsz3", //(tms_tools) 创建云盘普通账号
	disk_023_client_cut:"s1oxttb7", //(tms_tools) 剪切文件
	disk_012_client_mkdir:"s1oxttwo", //(tms_tools) 新建文件夹
	mock_028_get_apigrp_info:"s1oxtudx", //(tms_tools) 获取项目详情
	cdb_114_get_db_obj_account_privilege_sql:"s1oxtvvg", //(tms_tools) 获取srv数据库账号与数据库对象权限关系矩阵
	cdb_201_create_db:"s1oxtvx9", //(tms_tools) 创建db
	mock_015_operate_project:"s1oxtwnz", //(tms_tools) 项目操作: 新增，编辑，删除
	mock_044_add_rel_doc:"s1oxtx95", //(tms_tools) 关联文档
	cdb_204_save_db_restore_cron:"s1oxtz6c", //(tms_tools) 添加、维护数据库执行计划
	cdb_113_get_account_list:"s1oxu05a", //(tms_tools) 获取某个conn下的账号列表
	mock_021_get_env_list:"s1oxu0mu", //(tms_tools) 获取环境变量列表
	mock_081_get_web_proxy_base_url:"s1oxu1jh", //(tms_tools) 从web代理，将实际基地址 置换成 代理基地址
	mock_061_move_api_rule:"s1oxu2ae", //(tms_tools) mock用例拖动排序
	cdisk_031_admin_get_loglist:"s1oxu38r", //(tms_tools) 获取操作历史
	cdisk_1002_admin_del_team_user:"s1oxu4wd", //(tms_tools) 管理员 云盘删除关联客户
	mock_071_get_temp_api_srv:"s1oxu589", //(tms_tools) 创建临时的apisrv, 当用户点击创建 代理 或 mock，打开对话框时，即创建一个临时的
	mock_023_get_api_call_detail:"s1oxu5fu", //(tms_tools) 获取api调用详情
	cdisk_006_admin_get_weblink:"s1oxu6pz", //(tms_tools) 查看上传下载外链
	cdb_103_remove_connection:"s1oxu7du", //(tms_tools) 删除链接
	cdisk_046_search_all_dir:"s1oxu7k7", //(tms_tools) 搜索功能
	disk_014_client_weblink:"s1oxu8zx", //(tms_tools) 查看下载外链选项/打开/关闭下载外链
	cdb_304_get_func_def:"s1oxu9kg", //(tms_tools) 获取视图、函数、存储过程、事件的定义
	disk_021_client_uplink:"s1oxuarn", //(tms_tools) 
	cdb_308_operate_db_window:"s1oxuatq", //(tms_tools) tab窗体及查询的保存
	mock_066_operate_call_draft:"s1oxubfe", //(tms_tools) 新建草稿表单
	disk_020_client_uploaded:"s1oxucca", //(tms_tools) 上传
	mock_008_get_next_response_no:"s1oxue7e", //(tms_tools) 保存mock数据编号
	disk_022_client_uplink_update:"s1oxuenx", //(tms_tools) 更新上传外链设置
	browser_006_del_browser_intranet_proxy:"s1oxufs5", //(tms_tools) 删除内置浏览器代理设置
	disk_026_client_extn_utils:"s1oxufve", //(tms_tools) 上传链接
	cdisk_032_admin_mkdir:"s1oxugcv", //(tms_tools) 创建目录superadmin
	cdisk_044_admin_get_user_member_list:"s1oxuhdd", //(tms_tools) 获取当前云盘账号下绑定的成员列表
	mock_038_curl_invoke:"s1oxuhtv", //(tms_tools) curl调用
	disk_025_client_unzip:"s1oxuiuc", //(tms_tools) 解压
	mock_052_parse_curl:"s1oxujbt", //(tms_tools) 解析curl,返回url及请求参数
	cdb_310_run_cmd_sql:"s1oxukud", //(tms_tools) 命令行执行
	cdb_311_terminate_sql_exec:"s1oxulsi", //(tms_tools) 中断sql运行
	mock_034_save_mock_api_list:"s1oxum1e", //(tms_tools) mock服务引用api列表
	disk_011_client_chdir:"s1oxumqu", //(tms_tools) 切换目录
	mock_004_save_api_rule:"s1oxunib", //(tms_tools) 新建或修改 api规则用例
	cdb_303_get_table_info:"s1oxunre", //(tms_tools) 单个表基本信息（字段、索引、触发器、外键、注释等）
	cdisk_036_admin_adduser_form:"s1oxuoii", //(tms_tools) 获取用户设置详情
	mock_068_save_api_window:"s1oxuouq", //(tms_tools) 保存窗口列表
	mock_047_save_response_no:"s1oxupho", //(tms_tools) 保存响应体的mock编号
	cdb_107_get_db_obj_account_privilege:"s1oxuqey", //(tms_tools) 获取srv数据库账号与数据库对象权限关系矩阵
	mock_063_ignore_header:"s1oxur04", //(tms_tools) 忽略header信息
	cdisk_042_generate_weblinks:"s1oxus69", //(tms_tools) 批量发送外链
	mock_062_get_api_call_page:"s1oxusx9", //(tms_tools) 获取当前调用所在页码
	mock_036_get_api_call_list:"s1oxutsp", //(tms_tools) 获取api调用历史列表
	cdb_203_get_db_restore_cron_list:"s1oxutvl", //(tms_tools) 数据库执行计划列表
	mock_033_save_api_srv_info:"s1oxuubs", //(tms_tools) mock环境变量操作
	cdisk_028_admin_delete_user:"s1oxuvow", //(tms_tools) 删除本地的账号信息
	cdb_110_close_connection:"s1oxuvxj", //(tms_tools) 打开数据库链接
	mock_037_get_api_rule_list:"s1oxux2u", //(tms_tools) 获取mock用例列表
	mock_064_remove_ignored_header:"s1oxuxbm", //(tms_tools) 移除已忽略header信息
	cdb_205_get_db_restore_cron_exec_history:"s1oxuyqe", //(tms_tools) 执行计划运行历史列表
	mock_046_save_api_srv_auth_user:"s1oxuaat", //(tms_tools) mock调用鉴权设置
	mock_050_modify_rule_priority:"s1oxuaon", //(tms_tools) 修改mock用例优先级
	mock_076_remove_ignored_api_call:"s1oxubgt", //(tms_tools) 移除已忽略api/api_call
	cdb_105_create_account:"s1oxucli", //(tms_tools) 创建srv数据库账号（页面操作）
	mock_049_get_response_by_no:"s1oxudh3", //(tms_tools) 根据关键词查询mock编号及mock返回结果
	mock_065_list_ignored_headers:"s1oxue2i", //(tms_tools) 获取已忽略header列表
	browser_005_list_browser_intranet_proxy:"s1oxueeg", //(tms_tools) 获取内置浏览器代理设置
	mock_048_toggle_or_del_rule:"s1oxuf3y", //(tms_tools) 启用/禁用/删除 用例
	mock_077_get_ignored_api_call_list:"s1oxufha", //(tms_tools) 获取忽略列表
	cdisk_005_admin_modifyuser:"s1oxugul", //(tms_tools) 更新下account里的账户名称密码
	mock_031_check_request_if_match_api:"s1oxugui", //(tms_tools) 判断是否有匹配的api调用
	mock_072_save_api_case_doc:"s1oxuhbl", //(tms_tools) 新建用例
	disk_010_client_dir:"s1oxuhxt", //(tms_tools) 
	disk_015_client_rename:"s1oxuia8", //(tms_tools) 重命名
	mock_042_save_api_doc:"s1oxuj7s", //(tms_tools) 保存api文档
	mock_043_get_api_doc:"s1oxukaj", //(tms_tools) 获取api文档详情
	mock_054_operate_user_var:"s1oxulh5", //(tms_tools) 用户变量操作
	cdisk_008_admin_rel_wkspace_user:"s1oxullz", //(tms_tools) 管理员 云盘关联客户
	mock_020_get_api_list:"s1oxumeu", //(tms_tools) 获取接口列表
	mock_045_del_rel_doc:"s1oxun2e", //(tms_tools) 删除关联文档，取消关联
	cdb_108_set_db_obj_account_privilege:"s1oxunrd", //(tms_tools) 设置srv数据库账号权限
	disk_038_get_bookmark_list:"s1oxuoto", //(tms_tools) 获取书签列表
	mock_022_operate_env:"s1oxup4l", //(tms_tools) 环境变量操作
	cdb_109_open_connection:"s1oxuqns", //(tms_tools) 打开数据库链接
	cdb_312_get_sql_exec_history:"s1oxuqtk", //(tms_tools) sql执行历史记录
	mock_014_get_api_info:"s1oxurxa", //(tms_tools) 获取api详情
	cdisk_030_admin_delete_weblink:"s1oxus5c", //(tms_tools) 删除外链
	mock_058_call_proxy_link_api:"s1oxusxi", //(tms_tools) 代理调用手工关联api接口
	disk_040_del_bookmark:"s1oxutt0", //(tms_tools) 删除书签
	browser_002_list_browser:"s1oxuu9p", //(tms_tools) 获取内置浏览器列表
	cdb_309_run_sql:"s1oxuucs", //(tms_tools) postSQL语句执行
	disk_037_bookmark:"s1oxuveb", //(tms_tools) 添加书签
	mock_012_create_api_node:"s1oxuvvo", //(tms_tools) 节点操作: 添加，编辑（重命名)
	browser_003_del_browser:"s1oxuwwv", //(tms_tools) 删除内置浏览器窗口
	cdisk_039_admin_get_dirlist:"s1oxux1y", //(tms_tools) 切换并输出目录
	mock_041_check_curl_if_match_api:"s1oxuxdw", //(tms_tools) 根据传入的curl判断是否有匹配的api调用
	mock_013_edit_api:"s1oxuydr", //(tms_tools) 编辑api
	cdb_102_operate_connection:"s1oxuyuq", //(tms_tools) 操作conn连接
	disk_013_client_checkdownload:"s1oxuzhc", //(tms_tools) 检测下载许可
	disk_029_client_rmdirfile:"s1oxv01x", //(tms_tools) 删除文件/文件夹
	browser_004_save_browser_intranet_proxy:"s1oxv1dv", //(tms_tools) 内置浏览器代理设置
	disk_017_client_copy:"s1oxv3sn", //(tms_tools) 拷贝
	disk_018_client_paste:"s1oxv3yx", //(tms_tools) 粘贴
	exam_031_edit_paper_version_desc:"s1oxseht", //(tms_work) 编辑试卷版本
	excel_003_get_excel_version_list:"s1oxsfww", //(tms_work) 获取在线Excel版本历史
	task_307_delete_user_daily_task:"s1oxsgee", //(tms_work) 删除用户日常任务
	task_308_submit_user_exam:"s1oxshym", //(tms_work) 提交考试
	mailrpt_108_send_mail:"s1oxsiwg", //(tms_work) 发送邮件/存为草稿
	exam_004_get_question_rel_papers:"s1oxsj5b", //(tms_work) 获取题目关联试卷
	exam_016_save_import_questions:"s1oxsjr5", //(tms_work) 获取导入试题列表
	exam_040_get_question_page:"s1oxslme", //(tms_work) 根据nodeId查询页码 获取题目所在题库中的页码
	exam_002_save_question:"s1oxsmkq", //(tms_work) 保存题目
	exam_021_edit_questiongrp:"s1oxsnfr", //(tms_work) 编辑试卷
	mailrpt_123_update_reportcfg_seq_no:"s1oxsomh", //(tms_work) 移动报告类型
	task_117_get_team_exam_homework_nodes:"s1oxsp45", //(tms_work) 获取团队下考试/作业集
	exam_035_get_questiongrp_info:"s1oxsqjt", //(tms_work) 获取试卷详情
	task_114_del_color_tag:"s1oxsqqc", //(tms_work) 删除颜色标记
	mailrpt_115_create_work_report:"s1oxsr9l", //(tms_work) 创建工作报告
	mailrpt_116_get_work_report_info:"s1oxsst7", //(tms_work) 获取工作报告详情
	draw_002_get_diagram_template_list:"s1oxsth0", //(tms_work) 在线作图模板列表
	task_205_get_task_obj_question_detail:"s1oxstpk", //(tms_work) 获取考试/作业作答详情（阅卷人视角+成员视角）
	task_303_get_user_batchtask:"s1oxsusa", //(tms_work) 批次任务,任务详情(成员视角：相关联的任务执行情况，计算当前单个任务得分)
	mailrpt_103_flagging_mail:"s1oxsvfc", //(tms_work) 标记邮件/置顶
	mailrpt_107_get_mail_info:"s1oxswsf", //(tms_work) 邮件详情
	task_202_get_taskgrp_batch_user_statistics:"s1oxsx1l", //(tms_work) 批次任务某成员的统计
	exam_020_clear_questiongrp_questions:"s1oxsxqw", //(tms_work) 清空试题篮
	task_306_update_user_daily_task_finish_flag:"s1oxsycc", //(tms_work) 设置日常任务是否完成
	task_102_update_taskgrp_batch:"s1oxsyom", //(tms_work) 新建/修改批次任务
	draw_003_get_diagram_info:"s1oxszhr", //(tms_work) 在线作图详情
	exam_030_get_exam_analysis:"s1oxsaok", //(tms_work) 获取作答详情
	mailrpt_131_get_mailgrp_scorecfg_list:"s1oxsbso", //(tms_work) 获取评审结果列表
	task_115_edit_daily_task:"s1oxscg5", //(tms_work) 编辑日常任务
	doc_004_get_tutorial_info:"s1oxsdcp", //(tms_work) 获取教程信息(含章节信息)
	mailrpt_119_get_mail_page_with_id:"s1oxsdvp", //(tms_work) 获取邮件页码
	task_105_operate_exam_or_homework:"s1oxsewm", //(tms_work) 新建考试
	mailrpt_124_get_mailgrp_reportcfg_detail:"s1oxsf1q", //(tms_work) 获取报告设置详情
	task_301_get_user_batch_tasks:"s1oxsflm", //(tms_work) 获取某个批次任务的任务列表（成员视角：查看user_task）
	mailrpt_134_delete_scorecfg:"s1oxsgtd", //(tms_work) 评审结果设置的删除
	mailrpt_104_del_mail:"s1oxshfd", //(tms_work) 删除/彻底删除
	mailrpt_126_get_reportcfg_year_month_list:"s1oxshew", //(tms_work) 获取报告年份，月份列表
	exam_015_get_import_questions:"s1oxsibk", //(tms_work) 获取导入题目列表
	doc_003_get_version_list:"s1oxsjxz", //(tms_work) 获取文档版本列表
	exam_011_add_question_to_questiongrp:"s1oxsk0b", //(tms_work) 添加试题到试题篮
	exam_010_get_paper_list:"s1oxskon", //(tms_work) 获取试卷列表
	task_311_update_exam_or_homework_status:"s1oxslap", //(tms_work) 更新考试/作业任务状态
	task_103_get_taskgrp_batch:"s1oxslsd", //(tms_work) 查看批次任务
	task_111_get_color_tag_list:"s1oxsmtg", //(tms_work) 获取团队颜色标记列表
	mailrpt_128_save_reportcfg_timeline:"s1oxsooc", //(tms_work) 创建/修改报告配置
	excel_004_restore_excel_from_version:"s1oxsppq", //(tms_work) 恢复历史版本
	task_112_save_color_tag:"s1oxspzc", //(tms_work) 保存颜色标记
	mailrpt_121_save_mailgrp_reportcfg:"s1oxsqjc", //(tms_work) 克隆/编辑报告类型设置
	draw_005_save_diagram:"s1oxsrpr", //(tms_work) 在线作图保存
	task_201_get_taskgrp_batch_statistics:"s1oxss3f", //(tms_work) 批次任务-统计报表(成员指标统计/得分）
	doc_012_copy_doc:"s1oxsso4", //(tms_work) 复制文档
	draw_001_get_diagram_list:"s1oxste7", //(tms_work) 在线作图列表
	task_302_get_user_daily_tasks:"s1oxstw3", //(tms_work) 获取日常任务列表
	mailrpt_135_get_tpl_reportcfg_list:"s1oxsuuk", //(tms_work) 获取5001模板报告设置列表(用于新建时下拉选择)
	task_206_review_task_obj_question:"s1oxswdi", //(tms_work) 批阅试卷(阅卷人：单个题目手动评分)
	excel_002_get_excel_detail:"s1oxswvm", //(tms_work) 获取在线Excel
	news_002_get_news_detail:"s1oxsxzr", //(tms_work) 获取新闻详情
	exam_027_get_questiongrp_rel_user_task:"s1oxsxyq", //(tms_work) 获取试卷关联的考试/作业（task）信息
	task_314_get_exam_page:"s1oxsyow", //(tms_work) 根据nodeId查询页码 获取考试在“考试”中树列表中的页码
	doc_001_save_tutorial:"s1oxszje", //(tms_work) 创建文本库
	mailrpt_104_restore_mail:"s1oxszlu", //(tms_work) 恢复邮件
	task_104_create_daily_task:"s1oxt0b9", //(tms_work) 创建个人待办
	draw_004_get_diagram_content:"s1oxt10l", //(tms_work) 在线作图内容
	mailrpt_105_set_mail_read_unread_flg:"s1oxt1cu", //(tms_work) 已读/未读邮件
	mailrpt_129_delete_reportcfg_timeline:"s1oxt2hp", //(tms_work) 删除报告配置
	mailrpt_122_del_mailgrp_reportcfg:"s1oxt2xj", //(tms_work) 删除报告类型设置
	news_004_get_news_version_list:"s1oxt3t4", //(tms_work) 获取新闻版本列表
	excel_001_save_excel:"s1oxt40p", //(tms_work) 保存在线Excel
	task_310_get_user_exams_or_homeworks:"s1oxt4co", //(tms_work) 获取考试/作业列表
	news_003_save_news_detail:"s1oxt5db", //(tms_work) 保存新闻详情
	news_005_restore_new_from_version:"s1oxt5jl", //(tms_work) 恢复历史版本
	mailrpt_120_get_mailgrp_reportcfg_list:"s1oxt74j", //(tms_work) 获取报告设置列表
	mailrpt_133_update_scorecfg_seq_no:"s1oxt7gc", //(tms_work) 评审结果设置的移动
	task_107_get_exam_or_homework_info:"s1oxt8hh", //(tms_work) 考试基本信息(管理员视角)
	exam_024_get_paper_detail:"s1oxt8vn", //(tms_work) 获取试卷详情
	exam_029_get_paper_detail_by_version:"s1oxt9ee", //(tms_work) 获取试卷详情(去考试使用)
	exam_008_remove_questiongrp:"s1oxt9mv", //(tms_work) 删除题库
	exam_025_get_paper_versions:"s1oxtaye", //(tms_work) 获取试卷版本列表
	mailrpt_132_update_scorecfg:"s1oxtb53", //(tms_work) 评审结果设置的克隆/编辑/启用/禁用
	mailrpt_114_send_mail_remind:"s1oxtbjh", //(tms_work) 发送提醒
	doc_013_set_doc_help_type:"s1oxtcyn", //(tms_work) 设置文档是否有帮助
	task_118_move_task_tag:"s1oxtdtu", //(tms_work) 上下移动任务标签
	task_302_0_get_user_daily_tasks_and_schedules:"s1oxtdys", //(tms_work) 日常任务列表
	exam_019_set_paper_section_score:"s1oxtfrx", //(tms_work) 设置试卷题型分数
	exam_006_save_questiongrp:"s1oxtfr5", //(tms_work) 新增题库
	exam_032_get_questiongrp_cart_info:"s1oxtgre", //(tms_work) 获取试题篮信息
	task_304_get_user_exam_or_homework:"s1oxthhl", //(tms_work) 成员考试详情(成员视角)
	doc_002_save_doc_detail:"s1oxticl", //(tms_work) 保存教程的文章详情信息
	exam_026_restore_from_version_id:"s1oxtj7i", //(tms_work) 恢复至指定版本
	task_113_edit_color_tag:"s1oxtjwc", //(tms_work) 编辑颜色标记
	mailrpt_109_save_wkrpt_review_result:"s1oxtk5m", //(tms_work) 工作报告评分
	mailrpt_101_get_badge_num_by_user_id:"s1oxtkl8", //(tms_work) 邮件统计数字
	doc_011_restore_doc_from_version:"s1oxtloh", //(tms_work) 恢复历史版本
	mailrpt_130_update_timeline_seq_no:"s1oxtmje", //(tms_work) 更新报告配置顺序
	mailrpt_127_get_mailgrp_statistics:"s1oxtn4b", //(tms_work) 报告统计
	task_313_sync_user_task_info:"s1oxtnvx", //(tms_work) 同步考生考试/作业相关信息
	exam_007_update_questiongrp:"s1oxto76", //(tms_work) 编辑题库
	exam_033_move_questiongrp_question:"s1oxtpmt", //(tms_work) 试卷题目上下移动
	task_312_get_pre_exam_info:"s1oxtqkn", //(tms_work) 获取考前汇总信息
	mailrpt_117_del_work_report:"s1oxtqsx", //(tms_work) 删除工作报告
	news_001_get_news_list:"s1oxtruj", //(tms_work) 获取新闻列表
	task_116_create_node_daily_task:"s1oxts7n", //(tms_work) 创建节点日常任务
	exam_034_get_questiongrp_tree:"s1oxtsip", //(tms_work) 获取团队题库树
	mailrpt_102_get_mail_list:"s1oxttia", //(tms_work) 获取邮件列表
	mailrpt_125_save_mailgrp_reportcfg_detail:"s1oxttmm", //(tms_work) 编辑报告类型详情
	exam_001_get_questiongrp_questions:"s1oxtv7w", //(tms_work) 获取题库，试卷，试题篮题目列表
	task_123_export_task_score:"s1oxtvfn", //(tms_work) 任务得分详情导出
	task_126_get_report_info_list:"s1oxtwbz", //(tms_work) 获取报表列表
	task_125_save_report_list:"s1oxtwhe", //(tms_work) 批量保存报表
	task_122_get_taskgrp_report_list:"s1oxtxgk", //(tms_work) 获取任务组报表列表
	task_121_save_taskgrp_report:"s1oxty35", //(tms_work) 保存任务组报表
	task_501_get_completion_data_list:"s1oxtytf", //(tms_work) 根据表格配置条件获取完成率报表
	task_124_export_report_score:"s1oxtz3x", //(tms_work) 统计报表得分详情导出
	task_120_get_taskgrp_var_result_list:"s1oxtahp", //(tms_work) 查看任务组变量计算结果
	exam_042_export_questions:"s1oxtatz", //(tms_work) 导出题目
	exam_041_get_questiongrp_list:"s1oxtbzt", //(tms_work) 获取问卷列表
	doc_005_get_doc_detail:"s3jq3rmu", //(tms_work) 获取教程文章详情
}

//4个包合并后的 api
const apiNameNoEncrypt ={
  ...tmsTeamApis,
  ...tmsWorkApis,
  ...tmsToolsApis,
  ...tmsSipApis,
}

// 用一个对象包裹 getter, 当ot_token\bypass_token更改后apiName会自动更新
const apiNameProxy = {
  get apiName() {
    // 超级Key，存在即无需加密
    let bypass_token = localStorage.getItem(storageConstant.bypass_token); // TODO:需要接口验证bypass_token的准确性
    let ot_token = localStorage.getItem(storageConstant.ot_token);
    const isNoEncrypt = process.env.NODE_ENV == "development" || !!bypass_token || !ot_token;
    return isNoEncrypt ? apiNameNoEncrypt : apiNameEncrypt;
  }
};

// 导出 getter
export default apiNameProxy.apiName;