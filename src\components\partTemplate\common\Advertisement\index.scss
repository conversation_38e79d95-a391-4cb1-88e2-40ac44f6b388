.home-recommend {
    position: relative;
    padding: 50px 0;
    width: 100%;
    &-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        margin: auto;
        width: 1260px;
        &-left {
            display: flex;
            flex-direction: column;
        }
        &-right {
            position: relative;
            min-width: 700px;
            width: 700px;
            height: 400px;      
            text-align: center;
            line-height: 300px;  
            border-radius: 8px;
            box-shadow: 0 4px 4px 0 rgba(77, 77, 77, 0.01), 0 2px 8px 0 rgba(77, 77, 77, 0.07);
        }
    }
}

// 文案居左（默认）
.home-recommend-left {
    .home-recommend-content {
        flex-direction: row !important;
    }
}

// 文案居右
.home-recommend-right {
    .home-recommend-content {
        flex-direction: row-reverse !important;
    }
}