import { Input } from "antd";
import React, { forwardRef } from "react";

/**
 * @description: 封装带实时搜索的input,解决拼音输入法,不断搜索问题
 * TODO: 不能用在form表单中
 * @param {*} props 传入的属性
 * @param {*} ref 传入的ref
 * @return {*}
 */
function InputSearch({...props}, ref) {

  const cacheRef = React.useRef(Object.create(null));

  const onCompositionStart = () => {
    console.log("onCompositionStart");
    cacheRef.current.inputOnchange = true;
  }

  const onChange = (e) => {
    console.log("onChange");
    if (!cacheRef.current.inputOnchange) {
      props.onChange && props.onChange(e);
    }
  }

  const onCompositionEnd = (e) => {
    // 输入汉字拼音时不会触发该方法，可以用于判断输入的是英文还是拼音
    console.log("onCompositionEnd");
    cacheRef.current.inputOnchange = false;
    props.onChange && props.onChange(e);
  }

  return (
    <Input
      ref={ref}
      onCompositionStart={onCompositionStart}
      onCompositionEnd={onCompositionEnd}
      onChange={onChange}
      allowClear={true}
      className='tms-search-input'
      {...props}
    />
  );
};

export default forwardRef(InputSearch);