import React, { useEffect, useState, useRef } from "react";
import {
  Form,
  Input,
  Radio,
  Badge,
  InputNumber,
  Row,
  Button,
  Dropdown,
  Menu,
  Table,
  Card,
  Tooltip,
  Popconfirm,
} from "antd";
import {
  CloseOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { Link } from "react-router-dom";
import {
  team_703_calc_price,
  team_735_bind_user_coupon,
  team_736_unbind_user_coupon,
} from "../../../api/http_price";
import {
  team_701_get_product_list_query,
  team_706_get_free_team_count_by_user_query,
  useQueryGetUserValidCoupon
} from "../../../api/query/query_price";
import {team_619_user_info_query} from "../../../api/query/query"
import { useDebounce } from "../../../hook/index";
import "./index.scss";
import TLoading from "../../../components/TLoading";
import { useQueries, useQuery } from "@tanstack/react-query";
import { globalEventBus } from "../../../utils/eventBus";
import { globalUtil } from "@/utils/globalUtil";
import moment from "moment";

const { Search } = Input;

const PAYVERSION_BASE = 29; // 基础版
const PAYVERSION_PRO = 30; // 企业版

export const CREATETYPE_CREATE = 0; // 创建团队
export const CREATETYPE_UPGRADE = 1; // 升级至企业版

export const USAGETYPE_1 = 1; //创建团队
export const USAGETYPE_2 = 2; //产品购买
export const USAGETYPE_3 = 3; //创建团队、产品购买
export const USAGETYPE_5 = 5; //创建团队、云盘扩容
export const USAGETYPE_6 = 6; //产品购买、云盘扩容
export const USAGETYPE_7 = 7; //全品类可用

const formItemLayout = {
  labelCol: {
    span: 2,
  },
  wrapperCol: {
    span: 22,
  },
};

/**
 * @description 创建工作区
 * @param {*} param0
 * @returns
 */
export default function CreateTeam({
  teamId,
  type,
}) {
  const queryResult = useQueries({
    queries: [
      team_701_get_product_list_query(),
      team_706_get_free_team_count_by_user_query(),
      // {
      //   ...setting_105_get_team_detail_query(teamId),
      //   enabled: !!teamId,
      // },
      //team_702_get_team_package_duration_rebate_query(),
    ],
  });
  const [
    team701Result,
    team706Result,
    //setting105Result,
    //team702Result,
  ] = queryResult;
  const team619QueryData = useQuery(team_619_user_info_query())
  const {
    data: { couponList } = { couponList: [] },
    isLoading: isLoadingGetUserValidCoupon,
    refetch: refetchGetUserValidCoupon,
  } = useQueryGetUserValidCoupon();
  const [form] = Form.useForm();
  const priceRef = useRef({});
  const [discountForm] = Form.useForm();

  const [priceInfo, setPriceInfo] = useState({
    originalPrice: 0,
    regularAmt: 0,
    promoAmt: 0,
    discountReduction: 0,
    payPrice: 0,
    couponReduction: 0,
    resultCode: null,
    resultMessage: null
  });
  const isEnterpriseEdition = Form.useWatch("isEnterpriseEdition", form);
  const [discountCodeVisible, setDiscountCodeVisible] = useState(false);
  const [discountCodeFlag, setDiscountCodeFlag] = useState(false);

  const [dataSource,setDataSource] = useState([]);
  const [couponSelect,setCouponSelect] = useState(null);

  const [myCouponList,setMyCouponList] = useState([]);
  const [refreshingFlg,setRefreshingFlg] = useState(false);

  useEffect(() => {
    if((team701Result.data?.productList||[]).length > 0){
      let formatList = team701Result.data.productList.filter(group => type == CREATETYPE_UPGRADE ? group.groupId != 1 : true)
      let packageList = [];
      formatList.forEach(product => {
        let item = packageList.find(_product => product.groupId == _product.groupId)
        if(!item){
          packageList.push({groupName: product.groupName, groupId: product.groupId, groupList: [product]});
        }else{
          item.groupList.push(product)
        }
      });
      let list = []
      packageList.forEach(group => {
        let groupList = group.groupList.map((config,index) => ({
          ...config,
          key: config.productId,
          isRowSpan: index == 0 ? true : false,
          groupListLength: index == 0 ? group.groupList.length : 0,
          memberNo: 0,
          memberNoShow: 0,
          durationMonth: null,
          name: '',
          rebate: '',
          effectBeginDt: '',
          expirationDt: '',
          originalPrice: '',
          discountPrice: '',
          checked: config?.configValue?.status == 3 ? false : true,
        }));
        list = list.concat(groupList)
      });
      setDataSource([...list]);
    }
  }, [team701Result.dataUpdatedAt]);

  useEffect(()=>{
    if((couponList||[]).length > 0){
      setMyCouponList([...couponList]);
    }
  },[JSON.stringify(couponList)]);

  function memberNoChange(item,clickType){
    let data = dataSource.find(data => data.key == item.key)
    if(clickType == 0 && data.memberNo <= 0){
      return
    }
    if(clickType == 1){
      if(data.memberNo >= 1000){
        return
      }
    }
    dataSource.find(data => data.key == item.key).memberNo = clickType == 0 ? data.memberNo - 1 : clickType == 1 ? data.memberNo + 1 : (data.memberNoShow||0)
    dataSource.find(data => data.key == item.key).memberNoShow = clickType == 0 ? data.memberNoShow - 1 : clickType == 1 ? data.memberNoShow + 1 : (data.memberNoShow||0)
    if(!dataSource.find(data => data.key == item.key).durationMonth){
      dataSource.find(data => data.key == item.key).durationMonth = 12
      dataSource.find(data => data.key == item.key).name = '1年'
      dataSource.find(data => data.key == item.key).rebate = '9折'
    }
    if(dataSource.find(data => data.key == item.key).memberNo == 0){
      dataSource.find(data => data.key == item.key).durationMonth = null
      dataSource.find(data => data.key == item.key).name = ''
      dataSource.find(data => data.key == item.key).rebate = ''
    }
    load_team_703_calc_price(couponSelect?.couponCode)
    setDataSource([...dataSource]);
  }

  function memberNoChangeW(item,value){
    let data = dataSource.find(data => data.key == item.key)
    data.memberNo = value
    data.memberNoShow = value
    if(!data.durationMonth){
      data.durationMonth = 12
      data.name = '1年'
      data.rebate = '9折'
    }
    if((value||0) == 0){
      data.durationMonth = null
      data.name = ''
      data.rebate = ''
    }
    setDataSource([...dataSource]);
  }

  function durationMonthChange(item,item1){
    if(!!item1){
      dataSource.find(data => data.key == item.key).durationMonth = item1.monthCnt
      dataSource.find(data => data.key == item.key).name = item1.monthCntDesc
      dataSource.find(data => data.key == item.key).rebate = item1.promoRateDesc
      if(dataSource.find(data => data.key == item.key).memberNo == 0){
        dataSource.find(data => data.key == item.key).memberNo = 1
        dataSource.find(data => data.key == item.key).memberNoShow = 1
      }
    }else{
      dataSource.find(data => data.key == item.key).durationMonth = null
      dataSource.find(data => data.key == item.key).name = ''
      dataSource.find(data => data.key == item.key).rebate = ''
      dataSource.find(data => data.key == item.key).memberNo = 0
      dataSource.find(data => data.key == item.key).memberNoShow = 0
    }
    load_team_703_calc_price(couponSelect?.couponCode);
    setDataSource([...dataSource]);
  }

  const columns = [
    {
      title: <div style={{ display: 'flex', justifyContent: 'center' }}>类别</div>,
      dataIndex: 'groupName',
      key: 'groupName',
      width: 120,
      render: (groupName, item) => <div style={{ display: 'flex', justifyContent: 'center' }}>{groupName}</div>,
      onCell: (item) => {
        if (!!item.groupName) {
          return { rowSpan: item.groupListLength }
        } else {
          return { rowSpan: 0 }
        }
      },
    },
    {
      title: '产品',
      dataIndex: 'productName',
      key: 'productName',
      width: 170,
      render: (productName, item) =>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
          {productName}
          {item?.statusType == 2 && <span style={{ color: '#70B603', fontSize: 12, marginLeft: 10 }}>内测中</span>}
          {item?.statusType == 3 && <span style={{ color: '#F59A23', fontSize: 12, marginLeft: 10 }}>即将推出</span>}
        </div>,
      onCell: (item) => { return { rowSpan: 1 } },
    },
    {
      title: <div style={{textAlign:'center'}}>{isEnterpriseEdition == 1 ? '授权人数' : '成员数'}</div>,
      dataIndex: 'memberNo',
      key: 'memberNo',
      width: 130,
      render: (memberNo, item) =>{
        if(isEnterpriseEdition == 1){
          if(item.groupId == 1){
            return (<div style={{textAlign:'center'}}>-</div>)
          }
          return (
            <div style={{display:'flex',alignItems:'center',justifyContent:'center'}}>
              <div className={`member-edit-primary`}>
                <InputNumber 
                  controls={false}
                  style={{width:110}}
                  size="small"
                  precision={0}
                  min={0}
                  max={1000}
                  disabled={item?.statusType == 3}
                  addonBefore={
                    <Button 
                      type={'primary'} 
                      disabled={item?.statusType == 3 || (memberNo||0) == 0}
                      style={{borderTopLeftRadius:5,borderBottomLeftRadius:5,borderRight:'0px',borderColor: (item?.statusType == 3 || (memberNo||0) == 0) && '#d9d9d9'}}
                      onClick={()=>memberNoChange(item,0)}
                    >
                      -
                    </Button>
                  }
                  addonAfter={
                    <Button 
                      type={'primary'} 
                      disabled={item?.statusType == 3 || (memberNo||0) == 1000}
                      style={{borderTopRightRadius:5,borderBottomRightRadius:5,borderLeft:'0px',borderColor: (item?.statusType == 3 || (memberNo||0) == 1000) && '#d9d9d9'}}
                      onClick={()=>memberNoChange(item,1)}
                    >
                      +
                    </Button>
                  }
                  value={memberNo}
                  onChange={(value)=>memberNoChangeW(item,value)}
                  onPressEnter={()=>memberNoChange(item,2)}
                  onBlur={()=>memberNoChange(item,2)}
                />
              </div>
            </div>
          )
        }else{
          return (<div style={{textAlign:'center'}}>∞</div>)
        }
      },
      onCell: (item) => { return { rowSpan: 1 } },
    },
    {
      title: <div style={{textAlign:'center'}}>资源新建数</div>,
      dataIndex: 'resourceNo',
      key: 'resourceNo',
      width: 190,
      render: (resourceNo, item) =>{
        if(isEnterpriseEdition == 1 && item.memberNo > 0){
          return  (<div style={{textAlign:'center'}}>∞</div>);
        }else{
          return  (<div style={{textAlign:'center'}}>{item?.freeQuotaDesc||'-'}</div>);
        }
      },
      onCell: (item) => { return { rowSpan: 1 } },
    },
    {
      title: <div style={{textAlign:'center'}}>购买时长</div>,
      dataIndex: 'durationMonth',
      key: 'durationMonth',
      width: 100,
      render: (durationMonth, item, index1) => {
        if(item.groupId == 1){
          return (<div style={{textAlign:'center'}}>-</div>);
        }
        return (
          <div style={{display:'flex',alignItems:'center',justifyContent:'center'}}>
            <Dropdown trigger={['click']} disabled={item?.statusType == 3} overlay={
              <Menu>
                <Menu.Item>
                  <Radio.Group size="small" buttonStyle="solid" className="create-team-date-item" value={item.durationMonth}>
                    {(team701Result.data?.monthPromoList || []).map((item1,index)=> 
                      <Badge key={index} size="small" title="" className="pay-badge" count={item1.promoRateDesc || ""} offset={[-8,-5]}>
                        <Radio.Button onClick={()=>durationMonthChange(item,item1)} value={item1.monthCnt}>{item1.monthCntDesc}</Radio.Button> 
                      </Badge>
                    )}
                  </Radio.Group>
                </Menu.Item>
              </Menu>
            }>
              <a style={{color:'inherit'}}>
                {!!durationMonth ?
                  <Badge key={index1} size="small" title="" className="pay-badge" count={item.rebate || ""} offset={[-8,-5]}>
                    <div style={{height:24,width:52,display:'flex',justifyContent:'center',alignItems:'center',fontSize:12,color:'#fff',backgroundColor:'#0077F2'}} type='primary'>{item.name}</div> 
                  </Badge>
                :
                  <span style={{color: item.memberNo > 0 ? 'orange' : '#999'}}>新购时长</span>
                }
              </a>
            </Dropdown>
            {!!durationMonth && 
              <Button 
                style={{ borderRadius: '50%', transition: 'all 0s 0s'}} 
                size="small" 
                type="text" 
                icon={<CloseOutlined className="fontsize-12" />} 
                onClick={()=>durationMonthChange(item)}/>
            }
          </div>
        )
      },
      onCell: (item) => { return { rowSpan: 1 } },
    },
    {
      title: <div style={{textAlign:'center'}}>生效开始</div>,
      dataIndex: 'effectBeginDt',
      key: 'effectBeginDt',
      width: 120,
      render: (effectBeginDt, item) => {
        if(item.groupId == 1){
          return (<div style={{textAlign:'center'}}>-</div>)
        }
        return (<div style={{textAlign:'center'}}>{!!effectBeginDt ? moment(effectBeginDt).format('YYYY-MM-DD') : ''}</div>)
      },
      onCell: (item) => { return { rowSpan: 1 } },
    },
    {
      title: <div style={{textAlign:'center'}}>有效期至</div>,
      dataIndex: 'expirationDt',
      key: 'expirationDt',
      width: 120,
      render: (expirationDt, item) => {
        if(isEnterpriseEdition == 1 && !!expirationDt){
          return (
            <div style={{textAlign:'center'}}>{!!expirationDt ? moment(expirationDt).format('YYYY-MM-DD') : ''}</div>
          )
        }else{
          return (<div style={{textAlign:'center'}}>∞</div>)
        }
      },
      onCell: (item) => { return { rowSpan: 1 } },
    },
    {
      title: <div style={{textAlign:'center'}}>单价</div>,
      dataIndex: 'discountPrice',
      key: 'discountPrice',
      width: 150,
      render: (discountPrice, item) => {
        if(item.groupId == 1){
          return (<div style={{textAlign:'center'}}>0</div>)
        }
        return (
          <div style={{ display: 'flex', alignItems: 'center',justifyContent:'center' }}>
            <span style={{color: '#999',width: 40, textDecoration: 'line-through'}}>¥{item?.listingUnitAmt||'0'}</span>
            <span>¥{item?.regularUnitAmt||'0'}/人/月</span>
          </div>
        );
      },
      onCell: (item) => { return { rowSpan: 1 } },
    },
    {
      title: '小计',
      dataIndex: 'subtotal',
      key: 'subtotal',
      render: (subtotal, item) => {
        if(item.groupId == 1){
          return (<div></div>)
        }
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{color: '#999',marginRight: 10, textDecoration: 'line-through',flex:1}}>{!!item.originalPrice ? '¥' : ''}{item.originalPrice}</span>
            <span style={{flex:1}}>{!!item.discountPrice ? '¥' : ''}{item.discountPrice}</span>
          </div>
        );
      },
      onCell: (item) => { return { rowSpan: 1 } },
    }
  ]

  const _onOk = useDebounce(() => {
    if(!(team619QueryData?.data?.resultCode === 200)) {
      globalEventBus.emit("openLoginModal", "",);
      return
    }
    let params = `?isEnterpriseEdition=${isEnterpriseEdition}`
    let code_ = ''
    if(!!couponSelect && priceInfo.couponReduction > 0){
      code_ = couponSelect.couponCode
    }
    let url_ = `${process.env.REACT_APP_TMS_URL}/allteam${params}&code=${code_}&teamPackage=${JSON.stringify(dataSource)}`;
    window.open(url_);
  }, 500);

  // 获取支付价格
  const load_team_703_calc_price = useDebounce((couponCode="") => {
    let _packageList = dataSource.filter(data => data.memberNo > 0 && !!data.durationMonth).map(data => ({
      productId: data.productId,
      adjustMonthCnt: !!data.durationMonth ? data.durationMonth : 0,
      adjustAuthCnt: data.memberNo,
    }))

    if(_packageList.length == 0){
      if(!!couponCode){
        globalUtil.warning('请选择产品套餐');
      }
      setPriceInfo({
        originalPrice: 0,
        regularAmt: 0,
        promoAmt: 0,
        discountReduction: 0,
        payPrice: 0,
        couponReduction: 0,
        resultCode: null,
        resultMessage: null
      });
      setDataSource([...dataSource.map(data => {
        data.effectBeginDt = ''
        data.expirationDt = ''
        data.originalPrice = ''
        data.discountPrice = ''
        data.opType = 0
        return data
      })]);
      return
    }

    let request = {
      teamId,
      productList: _packageList,
      orderType: type == CREATETYPE_UPGRADE ? 2 : 1,
      couponCode: couponCode
    }


    team_703_calc_price(request).then((result) => {
      if(result.resultCode == 200){
        const {listingAmt, regularAmt, promoAmt, volumePromoAmt, netAmt,couponAmt,orderProductList,couponList} = result;
        setPriceInfo({
          originalPrice: listingAmt,
          regularAmt: regularAmt,
          promoAmt: promoAmt,
          discountReduction: volumePromoAmt,
          payPrice: netAmt,
          couponReduction: couponAmt,
          resultCode: !!couponCode ? result.resultCode : null,
          resultMessage: !!couponCode ? "有效优惠码" : ""
        });
        setMyCouponList([...(couponList||[])]);
        if((orderProductList||[]).length > 0){
          dataSource.forEach(data => {
            let pkgItem = orderProductList.find(pkg => pkg.productId == data.productId)
            if(!!pkgItem){
              data.effectBeginDt = pkgItem.effectiveDt
              data.expirationDt = pkgItem.expirationDt
              data.originalPrice = pkgItem.listingAmt
              data.discountPrice = pkgItem.netAmt
            }else{
              data.effectBeginDt = ''
              data.expirationDt = ''
              data.originalPrice = ''
              data.discountPrice = ''
            }
          });
          setDataSource([...dataSource]);
        }
      } else if(result.resultCode == 500){
        const {listingAmt, regularAmt, promoAmt, volumePromoAmt, netAmt,couponAmt,orderProductList,couponList} = result;
        setPriceInfo({
          originalPrice: listingAmt,
          regularAmt: regularAmt,
          promoAmt: promoAmt,
          discountReduction: volumePromoAmt,
          payPrice: netAmt,
          couponReduction: couponAmt,
          resultCode: !!couponCode ? result.resultCode : null,
          resultMessage: !!couponCode? result.resultMessage : null
        });
        if((orderProductList||[]).length > 0){
          dataSource.forEach(data => {
            let pkgItem = orderProductList.find(pkg => pkg.productId == data.productId)
            if(!!pkgItem){
              data.effectBeginDt = pkgItem.effectiveDt
              data.expirationDt = pkgItem.expirationDt
              data.originalPrice = pkgItem.listingAmt
              data.discountPrice = pkgItem.netAmt
            }else{
              data.effectBeginDt = ''
              data.expirationDt = ''
              data.originalPrice = ''
              data.discountPrice = ''
            }
          });
          setDataSource([...dataSource]);
        }
      }
    });
  }, 500);

  const onFieldsChange = (args) => {
    // console.log("我正在瞬息万变！！！！", args);
  };

  if (team701Result.isLoading || team706Result.isLoading) return <TLoading />;

  const userCntHelp =
    isEnterpriseEdition === 0
      ? (
        `限免基础版(免费)团队${team706Result.data?.teamCountFree}个`
        +
        (team619QueryData.data?.resultCode === 200 ? `,已使用${team706Result.data?.enabledTeamCountFree}个` : '')
        )
      : "";

  // 打开优惠码校验框
  const openDiscountCode = () => {
    if(discountCodeVisible) return
    setDiscountCodeVisible(!discountCodeVisible)
  }

  // 关闭优惠码校验框
  const closeDiscountCode = () => {
    setDiscountCodeVisible(!discountCodeVisible)
    discountForm.resetFields()
    priceRef.current = {}
    //clearDiscountCodeVerification()
  }

  // 校验优惠码
  const verificationDiscountCode = (value, event) => {
    if(event.nativeEvent.type === 'click' && !value) {
      //if(event.nativeEvent.type === 'click') clearDiscountCodeVerification()
      if(!value) //globalUtil.warning("请填写优惠码");
      return
    }
    team_735_bind_user_coupon({couponCode: value }).then(res => {
      if(res.resultCode == 200){
        discountForm.resetFields()
        priceRef.current = {}
        if((priceInfo.payPrice||0) > 0){
          load_team_703_calc_price(couponSelect?.couponCode);
        }else{
          refetchGetUserValidCoupon()
        }
      }
      if(res.resultCode == 500){
        globalUtil.error(res.resultMessage);
      }
    });
  }

  function couponSelectChange(coupon){
    if(!couponCanUse(coupon)){
      if(couponSelect?.couponId != coupon.couponId){
        setCouponSelect(coupon);
        load_team_703_calc_price(coupon.couponCode);
      }
    }
  }

  function couponCanUse(coupon){
    if(!!coupon.expirationDt){
      if(moment(coupon.expirationDt).isBefore(moment())){
        return '优惠券已过期'
      }
    }
    if(coupon.orderAmtFlg == 0){//金额是否适用，0---不适用   1---适用
      return '订单金额不在适用范围'
    }
    if(!!teamId){//产品购买
      if(!!coupon.teamId && coupon.teamId != teamId){//如果指定团队和该团队不匹配，无法使用
        return `指定团队可用(${coupon.teamId})`
      }
      if(coupon.usedByTeamFlg == 1){//券有没有被该团队使用过，0---未使用过  1---使用过
        return '本团队已使用过该券'
      }
    }else{//创建团队
      if(!!coupon.teamId){//是否指定团队使用，不为空即表示指定团队使用，为空表示任意团队都可使用
        return `指定团队可用(${coupon.teamId})`
      }
      //因为是新建团队，所以不必关注是否被团队使用过
      if(coupon.usedByUserFlg == 1){//有没有被该用户使用过， 0---未使用过  1---使用过
        return '您已使用过该券'
      }
    }
    return ''
  }

  function deleteCoupon(coupon){
    team_736_unbind_user_coupon({couponId: coupon.couponId}).then(res => {
      if(res.resultCode == 200){
        if((priceInfo.payPrice||0) > 0){
          if(couponSelect?.couponCode == coupon.couponCode){
            setCouponSelect(null);
            load_team_703_calc_price();
          }else{
            load_team_703_calc_price(couponSelect?.couponCode);
          }
        }else{
          refetchGetUserValidCoupon()
        }
      }
    });
    //setMyCouponList([...myCouponList.filter(myCoupon => myCoupon.couponId != coupon.couponId)]);
  }

  return (
    <>
      <Form
        form={form}
        name="basic"
        onFinish={_onOk}
        initialValues={{
          isEnterpriseEdition: discountCodeFlag ? 1 : (type === CREATETYPE_CREATE ? 0 : 1),
        }}
        {...formItemLayout}
        autoComplete="off"
        onFieldsChange={onFieldsChange}>
        <Form.Item 
          label="版本选择" 
          name="isEnterpriseEdition" 
          style={type == CREATETYPE_CREATE?{}:{display:"none"}}>
            <Radio.Group buttonStyle="solid">
              <Radio.Button value={0} style={{borderTopLeftRadius:5,borderBottomLeftRadius:5,width:120,textAlign:'center',zIndex:0}}>基础版(免费)</Radio.Button>
              <Radio.Button value={1} style={{borderTopRightRadius:5,borderBottomRightRadius:5,width:120,textAlign:'center'}}>VIP版</Radio.Button>
            </Radio.Group>
        </Form.Item>
      </Form>
      <Form
        className="CreateTeam-form"
        name="productCheck"
        {...formItemLayout}
        autoComplete="off"
      >
        <Form.Item 
          className="product-selection-formItem"
          label="产品选择" 
          name="packageList">
          <Table
            size="small"
            dataSource={dataSource}
            columns={type == CREATETYPE_CREATE && isEnterpriseEdition == 0 ? columns.filter(column => column.key != 'durationMonth' && column.key != 'effectBeginDt' && column.key != 'discountPrice' && column.key != 'subtotal') : columns}
            pagination={false}
            bordered
          />
        </Form.Item>
      </Form>
      {isEnterpriseEdition == 1 && <div className="price-bottom" style={{ marginTop: 10, marginLeft: 10 }}>
        <div className="price-bottom-left">
          <div style={{display:'flex',alignItems:'center'}}>
            <span style={{marginRight:100,fontSize:14}}>选择优惠券({(myCouponList||[]).length})<Tooltip title="高亮显示的优惠券可用，灰显的优惠券不可用，鼠标移至不可用的优惠券上显示不可用原因" placement="right"><QuestionCircleOutlined style={{ color: '#f59a23',marginLeft:5 }}/></Tooltip></span>
            <Button
              className={refreshingFlg && 'refresh-icon'}
              style={{ position: 'relative', color: '#666' }}
              type="link"
              icon={<span className="refresh-position fontsize-14 iconfont shuaxin1" />}
              onClick={() => {
                setRefreshingFlg(true);
                setTimeout(() => {
                  if((priceInfo.payPrice||0) > 0){
                    load_team_703_calc_price(couponSelect?.couponCode);
                  }else{
                    refetchGetUserValidCoupon()
                  }
                  setRefreshingFlg(false);
                }, 500);
              }}
            />
            <span style={{marginRight:50,fontSize:14}}>刷新</span>
            <Form colon={false} form={discountForm}>
              <Form.Item 
              style={{ margin: 0 }}
              label={<span style={{ color: '#0077f2', cursor: 'pointer' }}
              onClick={openDiscountCode}>我有优惠券</span>}
              >
                {discountCodeVisible &&
                  <Form.Item 
                  name="code" 
                  noStyle
                  >
                    <Search
                      className="discount-code discount-code-visible"
                      size="small"
                      // style={{ width: 160 }}
                      maxLength={11}
                      allowClear
                      placeholder="请填写优惠码"
                      enterButton="校验"
                      onChange={e => { priceRef.current.couponCode =  e.target.value }}
                      onSearch={verificationDiscountCode}
                    />
                  </Form.Item>
                }
                <Form.Item noStyle>
                  <Button 
                    style={{ borderRadius: '50%', visibility: discountCodeVisible ? 'visible' : 'hidden', transition: 'all 0s 0s'}} 
                    size="small" 
                    type="text" 
                    icon={<CloseOutlined className="fontsize-12" />} 
                    onClick={closeDiscountCode}/>
                </Form.Item>
              </Form.Item>
            </Form>
          </div>
          <div className="flex-column-parent" style={{height: 120, width: 990, marginBottom: 10, border: '1px solid #f0f0f0', borderRadius: 5}}>
            <div style={{height:'100%',padding: '5px 0px 5px 5px'}} className="flex-column-child section">
              <Row>
                {myCouponList.map(coupon => {
                  return (
                    <div style={{display:'flex',alignItems:'flex-start',position:'relative', height: 105}}>
                      <Button type='text' title={!couponCanUse(coupon) ? '' : `不可用原因：${couponCanUse(coupon)}`} onClick={() => couponSelectChange(coupon)} style={{padding:0}} disabled={!!couponCanUse(coupon)}>
                        <Card 
                          style={{backgroundColor: !couponCanUse(coupon) ? '#E6F2FE' : '#f0f0f0'}} 
                          className={(couponSelect?.couponId == coupon.couponId ? "CouponCard-select" : '') + " CouponCard"} 
                          hoverable={false}
                        >
                          <div
                            style={{
                              width:'100%', 
                              height: 70, 
                              backgroundColor: !couponCanUse(coupon) ? '#0077F2' : '#999',
                              borderTopLeftRadius:5,
                              borderTopRightRadius:5
                            }}
                          >
                            <div style={{display:'flex',alignItems:'center',height:'100%',marginLeft:10}}>
                              <div style={{color:'#fff'}}>
                                <div style={{display:'flex',alignItems:'center'}}>
                                  <div style={{fontSize:18,marginRight:20}}>{coupon.couponType == 2 ? ('减￥'+(coupon.couponValue||'')) : ((coupon.couponDiscount||'')+'折')}</div>
                                  <div>满  ￥{coupon.minOrderAmt}</div>
                                </div>
                                <div>有效期至{moment(coupon.expirationDt).format('YYYY-MM-DD')}</div>
                              </div>
                            </div>
                          </div>
                          <div style={{width:'100%',height: 30}}>
                            <div style={{display:'flex',alignItems:'center',height: 30, marginLeft:10,color: !couponCanUse(coupon) ? '#0077F2' : '#999'}}>
                              <span>{coupon.couponName}</span>
                            </div>
                          </div>
                        </Card>
                      </Button>
                      <Popconfirm title='确定删除该优惠券?' onConfirm={()=>deleteCoupon(coupon)}>
                        <Button 
                          style={{ borderRadius: '50%', transition: 'all 0s 0s',width:14,height:14,position:'absolute',right:0}} 
                          size="small" 
                          icon={<CloseOutlined style={{fontSize:10}} />}
                        />
                      </Popconfirm>
                    </div>
                  );
                })}
              </Row>
            </div>
          </div>
        </div>
        <div className="price-bottom-right">
          <div className="price-bottom-detailed">
            <div className="price-bottom-detailed-descriptions">
              <div className="price-bottom-detailed-descriptions-li">
                <span className="li-label">原价:</span>
                <span className="li-value"><span style={{textDecoration: 'line-through',color:'#999',marginRight:10}}>￥{priceInfo.originalPrice||0}</span>{(priceInfo.promoAmt||0) > 0 ? `(立减-￥${priceInfo.promoAmt})` : ''}</span>
              </div>
              <div className="price-bottom-detailed-descriptions-li">
                <span className="li-label">现价:</span>
                <span className="li-value">￥{priceInfo.regularAmt||0}</span>
              </div>
              {priceInfo.discountReduction ?
              <div className="price-bottom-detailed-descriptions-li">
                <span className="li-label">折扣减:</span>
                <span className="li-value">-￥{priceInfo.discountReduction}</span>
              </div>
              :
              <div style={{display:'none'}}></div>
              }
              {!!couponSelect ?
              <div className="price-bottom-detailed-descriptions-li">
                <span className="li-label" style={{color: priceInfo.couponReduction == 0 ? '#999' : '#333'}}>优惠券:</span>
                <span className="li-value" style={{color: priceInfo.couponReduction == 0 ? '#999' : '#333'}}>
                  -￥{priceInfo.couponReduction}
                  <span className="coupon" style={{backgroundColor: priceInfo.couponReduction == 0 ? '#999' : '#0077F2'}}>
                    满￥{myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.minOrderAmt}
                      {myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponType == 2 ? '减￥' : '享'}
                      {myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponType == 2 ? myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponValue : myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponDiscount}
                      {myCouponList.find(coupon => coupon.couponCode == couponSelect.couponCode)?.couponType == 2 ? '' : '折'}
                    <a 
                      className="delete" 
                      style={{color: priceInfo.couponReduction == 0 ? '#999' : '#0077F2'}} 
                      onClick={()=>{setCouponSelect(null);load_team_703_calc_price();}}
                    >
                      <CloseOutlined style={{fontSize:10}} />
                    </a>
                  </span>
                </span>
              </div>
              :
              <div style={{display:'none'}}></div>
              }
              <div className="price-bottom-detailed-descriptions-li">
                <span className="li-label">合计:</span>
                <span className="li-value">
                  <div style={{fontSize:12,display:'flex',alignItems:'baseline',justifyContent:'end'}}>
                    <span style={{color:'#f59a23'}}>￥</span>
                    <span className="shifu-rate" style={{color:'#f59a23'}}>{priceInfo.payPrice||0}</span>
                  </div>
                </span>
              </div>
              {/* <a style={{fontSize:12,display:'flex',color:'#0077F2'}} onClick={()=>setShowPriceDetail(true)}>价格详情</a> */}
            </div>  
          </div>
        </div>
      </div>}
      <div>
        {isEnterpriseEdition == 0 &&
          <div style={{ marginLeft: 10,height: 20,fontSize:12,color: '#999' }}>备注1：{userCntHelp}</div>
        }
        {isEnterpriseEdition == 1 && 
          <div style={{ marginLeft: 10,height: 20,fontSize:12,color: '#999' }}>备注1：vip人数为0的产品，您将继续享有基础版的功能免费长期使用。</div>
        }
        {type == CREATETYPE_CREATE ?
          <div 
            style={{ 
              marginLeft: 10, 
              height: 20, 
              fontSize: 12, 
              color: '#999'
            }}
          >
            备注2：您不需要的产品功能，在团队创建成功后，可在 设置 {'->'} 产品管理 页面中禁用(或再次开启)
          </div>
        :
          null
        }
        {type == CREATETYPE_CREATE &&
          <div style={{ marginLeft: 10,height: 20,fontSize:12,color: '#999',display:'flex',alignItems:'center' }}>
            <div style={{width:42.38}}></div>
            点击 <Link to='/help/doc'>帮助中心</Link> {'->'} <Link to={`/help/doc/${process.env.REACT_APP_PRODUCT_NODEID}`}>产品管理</Link> 链接，可定位到 官网帮助页的 关于产品管理的帮助文档。
          </div>
        }
        {type == CREATETYPE_UPGRADE && 
        <div 
          style={{ 
            marginLeft: 10, 
            height: 20, 
            fontSize: 12, 
            color: '#999'
          }}
        >
          备注2：可在 设置 {'->'} 产品管理 中进行产品的启用/禁用。
        </div>
        }
      </div>

      <div style={{ padding: '0 40px', marginTop: 10, textAlign: 'right' }}>
        <Button
          className={isEnterpriseEdition == 1 ? "purchase-btn":"found-btn"}
          disabled={isEnterpriseEdition == 1 && priceInfo.discountPrice === 0}
          type="primary"
          style={{ width: 140, height: 34, borderRadius: 5 }}
          onClick={_onOk}
          >
            {isEnterpriseEdition == 1 ? "立即购买" : "立即创建团队"}
        </Button>
      </div>
    </>
  );
}
