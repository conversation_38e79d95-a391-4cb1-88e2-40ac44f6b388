import {
  setting_105_get_team_detail,
  team_701_get_product_list,
  team_702_get_team_package_duration_rebate,
  team_706_get_free_team_count_by_user,
  team_710_get_team_validity_package,
  team_734_get_user_coupon_list,
} from '../http_price';
import { useQuery } from "@tanstack/react-query";

// 获取团队详情
export const setting_105_get_team_detail_query = (teamId) => ({
  queryKey: ['setting_105_get_team_detail', teamId],
  queryFn: async () => setting_105_get_team_detail({ teamId })
})

// 获取团队版本套餐
export const team_701_get_product_list_query = () => ({
  queryKey: ['team_701_get_product_list'],
  queryFn: async () => team_701_get_product_list()
})

// 根据团队人数计算购买时长的折扣比例
export const team_702_get_team_package_duration_rebate_query = (couponCode) => ({
  queryKey: ['team_702_get_team_package_duration_rebate', couponCode],
  queryFn: async () => team_702_get_team_package_duration_rebate({ couponCode })
})

// 获取团队规模最小值
export const team_706_get_free_team_count_by_user_query = () => ({
  queryKey: ['team_706_get_free_team_count_by_user'],
  queryFn: async () => team_706_get_free_team_count_by_user()
})

// 获取当前团队购买的有效期内的套餐功能
export const team_710_get_team_validity_package_query = (teamId) => ({
  queryKey: ['team_710_get_team_validity_package',teamId],
  queryFn: async () => team_710_get_team_validity_package({teamId})
})

export const team_734_get_user_coupon_list_query = () => ({
  queryKey: ['team_734_get_user_coupon_list'],
  queryFn: async () => team_734_get_user_coupon_list()
})

export function useQueryGetUserValidCoupon() {
  return useQuery({...team_734_get_user_coupon_list_query()});
}
