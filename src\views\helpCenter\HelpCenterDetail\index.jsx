/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useParams, useLocation, useOutletContext } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import {
  Box,
  Typography,
  Button,
  Stack,
} from "@mui/material";
import "../index.scss";
import {
  doc_005_get_doc_detail
} from "../../../api/http_helpcenter";
import {
  team_120_get_obj_favorite_status,
  team_029_obj_social_op,
  team_564_load_obj_list
} from "../../../api/http_common";
import {doc_004_get_tutorial_info_query} from "../../../api/query/query";
import {TEditorPreview} from "../../../components/TEditor/TEditor";
import { useDebounce } from "../../../hook/index";
import PageNonExistent from "../../../components/PageNonExistent";
import {FOLDER_INFO, getDocNodeId} from "../fileTypeUtils";
import { LikeOutlined } from '@ant-design/icons';
import { Divider, Breadcrumb  } from "antd";
import PageTitle from "@/components/PageTitle";
import PortalLink from "../../../components/PortalLink";

// 帮助文档详情
export default function HelpCenterDetail() {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentRouteInfo } = useOutletContext();
  const nodeTypeRef = useRef({nodeType: null})
  const docTeamId = process.env.REACT_APP_WORKORDER_TEAMID; // 文档接口teamId
  const { nodeId } = useParams()
  const docNodeId = getDocNodeId(location); // 文档接口nodeId
  const [docDetail, setDocDetail] = useState({}); // 文档详情
  const [resourceList, setResourceList] = useState([]);
  const [isFolder, setIsFolder] = useState(true);
  const [getDataSucceFlg, setGetDataSucceFlg] = useState(true);
  const [objFavoriteAndWatch, setObjFavoriteAndWatch] = useState({});
  const [animationClassName, setAnimationClassName] = useState("");
  const [loadObjInfo, setLoadObjInfo] = useState({
    name: '',
    objList: []
  });
  const [tutorialList,setTutorialList] = useState([]);


  const { data: { rootNodeUi, treeKeyList } = { rootNodeUi: null, treeKeyList: [] }, isLoading: isLoadingObjTree, refetch } = useQuery({
    ...doc_004_get_tutorial_info_query(process.env.REACT_APP_WORKORDER_TEAMID, docNodeId, [], true)
  })

  useEffect(() => {
    if(rootNodeUi){
      initData();
    }
  },[rootNodeUi, nodeId])

  // 初始化数据
  const initData = async () => {
    let toTitleFilterList = treeToTile(rootNodeUi?.children || [], []).filter(el => el.nodeId == nodeId)
    if(toTitleFilterList.length > 0){
      nodeTypeRef.current = toTitleFilterList[0]
      setIsFolder(compareFolder(nodeTypeRef.current.nodeType - 0))
      if(compareFolder(nodeTypeRef.current.nodeType - 0)) {
        loadObjList(docTeamId, nodeId)
      } else {
        getDocDetail(docTeamId, nodeId)
        getObjFavoriteStatus(docTeamId, nodeId)
        setTutorialList(toTitleFilterList);
      }
    }else{
      setIsFolder(false)
      //getDocDetail(docTeamId, nodeId)
      //getObjFavoriteStatus(docTeamId, nodeId)
    }
  }

  const treeToTile = (data, list) => {
    if (!(data.length > 0)) {
        return [];
    }
    data.forEach(item => {
      if (item.children && item.children.length) {
        let item_ = {
          ...item,
          children: []
        }
        list.push(item_)
        treeToTile(item.children, list)
      } else {
        list.push(item)
      }
    });
    return list;
  }

  // 判断是否为文件夹，反之为文档
  const compareFolder = (type) => {
    const {
      FOLDER_302,
      FOLDER_302105,
      FOLDER_302106,
      FOLDER_302107,
      FOLDER_302109,
      FOLDER_302316,
      FOLDER_302317,
      FOLDER_30231703,
      FOLDER_302500,
      FOLDER_302118,
      FOLDER_302312,
      FOLDER_312
    } = FOLDER_INFO;
    if(
      type === FOLDER_302 || 
      type === FOLDER_302105 || 
      type === FOLDER_302106 || 
      type === FOLDER_302107 || 
      type === FOLDER_302109 || 
      type === FOLDER_302316 || 
      type === FOLDER_302317 || 
      type === FOLDER_30231703 || 
      type === FOLDER_302500 || 
      type === FOLDER_302118 || 
      type === FOLDER_302312 || 
      type === FOLDER_312
    ) return true
    return false
  }

  // 获取文件夹下的节点
  const loadObjList = (teamId, objNodeId) => {
    team_564_load_obj_list({teamId, objNodeId}).then(res => {
      if(res.resultCode == 200){
        setGetDataSucceFlg(true)
        setLoadObjInfo({
          name: res.name,
          objList: [...res.objList]
        })
      } else {        
        setLoadObjInfo({
          name: "",
          objList: []
        })
      }
    });
  } 

  // 获取帮助文档详情
  const getDocDetail = (teamId, objNodeId) => {
    doc_005_get_doc_detail({teamId, objNodeId}).then(res => {
      if(res.resultCode == 200){
        setGetDataSucceFlg(true)
        setDocDetail({...res.docDetail})
        setResourceList([...(Array.isArray(res.resourceList) || [])])
      } else {
        setGetDataSucceFlg(false)
        setDocDetail({})
      }
    });
  }

  // 获取点赞等数量
  const getObjFavoriteStatus = (teamId, objNodeId) => {
    team_120_get_obj_favorite_status({teamId, objNodeId}).then(res => {
      if(res.resultCode == 200){
        setObjFavoriteAndWatch({...res.objFavoriteAndWatch})
      } else {
      }
    });
  }

  // 点赞
  const objSocialOp =(teamId, opType, objNodeId) => {
    likesClick()
    let obj_ = objFavoriteAndWatch
    obj_.likeCnt += 1
    setObjFavoriteAndWatch({...obj_})
    team_029_obj_social_op({teamId, opType, objNodeId}).then(res => {
      if(res.resultCode == 200){
      } else {
        obj_.likeCnt -= 1
        setObjFavoriteAndWatch({...obj_})
      }
    });
  }

  const likesClick = useDebounce(() => {
    setAnimationClassName("likes-animation")
    setTimeout(() => {
      setAnimationClassName("")
    }, 1000);
  },100)

  function prevAndNext(_nodeId){
    let path = currentRouteInfo.path
    navigate(`/${path}/${_nodeId}`);
  }

  return (
    getDataSucceFlg  ? 
      <>
        <HelpBreadcrumb currentRouteInfo={currentRouteInfo} nodeList={treeKeyList}/>
        {isFolder ? 
          <FolderChildNode loadObjInfo={loadObjInfo} nodeType={nodeTypeRef.current.nodeType} compareFolder={compareFolder}/>
         :
        <>
          {docDetail?.title && <PageTitle nodeId={nodeId} title={docDetail?.title}/>}
          <Box className="des-information">
            <Stack
            sx={{ flexWrap: "wrap", alignItems: 'center' }}
            direction={{ xs: "column", sm: "row" }}
            spacing={3}
            >
              {!!docDetail.createDt && <Box className="des-information-stack-li">
                <Typography variant="subtitle2">发布时间：</Typography>
                <Typography variant="body2">{docDetail.createDt}</Typography>
              </Box>}
              <Box className="des-information-stack-li">
                <Typography variant="subtitle2"><span title="访问量" className="iconfont yanjing_xianshi" style={{ fontSize: 22, color: "#999" }}/></Typography>
                <Typography variant="body2">{objFavoriteAndWatch.visitCnt >= 0 ? objFavoriteAndWatch.visitCnt : 0}</Typography>
              </Box>
              <Box className="des-information-stack-li">
                <Box className="a-literary-eulogy">
                  <div className={['likes-style', animationClassName].join(" ")}>+1</div>
                  <Button 
                  className="approve-of-btn" 
                  size="small" 
                  variant="outlined" 
                  startIcon={<LikeOutlined style={{fontSize:15}}/>}
                  onClick={() => objSocialOp(docTeamId, 4, nodeId)}>
                    {objFavoriteAndWatch.likeCnt > 0 ? `${objFavoriteAndWatch.likeCnt}` : 0}</Button>
                </Box>
              </Box>
            </Stack>
          </Box>
          <Box className="doc-content">
            <TEditorPreview content={docDetail.content}/>
          </Box>
          <Divider style={{margin:'5px 0px'}}/>
          {(tutorialList[0]?.prevNodeId || tutorialList[0]?.nextNodeId) && <div className="doc-changing-over">
            {tutorialList[0]?.prevNodeId ? 
              <a style={{alignItems:'center',fontSize:14,flex:1}} onClick={()=>prevAndNext(tutorialList[0].prevNodeId)}>{'<上一篇' + (tutorialList[0]?.prevName ? ('：' + tutorialList[0].prevName) : '')}</a>
            : <div style={{fontSize:14,color:'#CCCCCC',flex:1}}>{'<上一篇'}</div>}
            {tutorialList[0]?.nextNodeId ? 
              <a style={{alignItems:'center',fontSize:14,flex:1,textAlign:'right'}} onClick={()=>prevAndNext(tutorialList[0]?.nextNodeId)}>{'下一篇' + (tutorialList[0]?.nextName ? ('：'+tutorialList[0].nextName) : '') + '>'}</a>
            : <div style={{fontSize:14,color:'#CCCCCC',flex:1,textAlign:'right'}}>{'下一篇>'}</div>}
          </div>}
        </>}
      </>
     : <PageNonExistent tips={"页面走丢了"}/>
  )
}


// 文件夹下面的节点目录
function FolderChildNode({loadObjInfo, nodeType, compareFolder}) {
  const navigate = useNavigate();
  const location = useLocation();
  const { nodeId } = useParams()

  // 文档跳转
  const docSkip = (item) => {
    navigate(`/help/doc/${item.id}`)
  }
  return (
    <div className="FolderChildNode">
      <div className="FolderChildNode-title FolderChildNode-link" onClick={() => docSkip({id: nodeId, nodeType})}>{loadObjInfo.name}</div>
      <div className="FolderChildNode-node">
        {loadObjInfo.objList.map((item, index) => (
          <div className="FolderChildNode-node-li" key={item.id} onClick={() => docSkip(item)}>
            <span 
              className={["fontsize-16", "iconfont", compareFolder(item.nodeType) ? "folder-fill" : "file-word-fill"].join(" ")}
              style={{ color: "#999" }}
            />
            <span style={{ marginLeft: 6 }} className="FolderChildNode-link">{item.name}</span>
          </div>
        ))}
      </div>
    </div>
  )
}

// 帮助文档面包屑
function HelpBreadcrumb({currentRouteInfo, nodeList}) {
  const { nodeId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const docNodeId = getDocNodeId(location); // 文档接口nodeId
  const [breadcrumbData, setBreadcrumbData] = useState([]);
  useEffect(() => {
    if(nodeId && nodeList.length) {
      setBreadcrumbData([...getCurrentNodeBreadcrumb([], nodeId, nodeList)])
    }
  },[nodeList, nodeId])
  
  // 通过nodeId寻找上一层级
  const getCurrentNodeBreadcrumb = (arr, nodeId, data) => {
    let node_ = data.find(e => e.key == nodeId)
    if(node_?.key && node_?.key != docNodeId){
      arr.unshift({...node_})
      getCurrentNodeBreadcrumb(arr, node_.parentKey, data)
    }
    return arr
  }

  return (
    <div className="HelpBreadcrumb">
      <Breadcrumb>
        <Breadcrumb.Item key={docNodeId}>              
          <PortalLink className="fontsize-12 HelpBreadcrumb-link" name={currentRouteInfo.meta.title}/>
        </Breadcrumb.Item>
        {
          breadcrumbData.map((node, index) => (
            <Breadcrumb.Item key={node.key}>
              <PortalLink 
                className="fontsize-12 HelpBreadcrumb-link"
                to={`/${currentRouteInfo.path}/${node.key}`} 
                name={node.label}
              />
            </Breadcrumb.Item>
          ))
        }
      </Breadcrumb>
    </div>
  )
}