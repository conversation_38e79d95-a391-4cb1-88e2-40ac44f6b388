// iTeam简介-右侧滚动切换模板（默认幻灯片滚动区居右）
.FunctionIntroductionScrollSwitch {
    position: relative;
    padding: 50px 0;
    width: 100%;
    &-title {
        font-size: 34px;
        text-align: center;
    }
    &-content {
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
        margin: 10px auto 0;
        width: 1260px;
        &-left {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin: 4px;
            padding: 16px;
            width: 780px;
            min-width: 780px;
            height: 550px;
            box-shadow: 0px 0px 4px 1px rgba(0,0,0,.06);
            border-radius: 5px;
            .ant-image {
                height: 360px;
                overflow: hidden;
                border-radius: 5px;
                box-shadow: 0px 0px 4px 1px rgba(0,0,0,.06);
            }
            &-image {
            }
            &-text {
                &-title {
                    font-weight: bold;
                }
            }
        }
        &-right {
            padding: 4px;
            width: 400px;
            height: 550px;
            overflow: auto;
            &-li {
                margin-bottom: 16px;
                padding: 10px;
                box-shadow: 0px 0px 4px 1px rgba(0,0,0,.06);
                border-radius: 5px;
                cursor: pointer;
                .scrollSwitch-image {
                    height: 100px;
                    overflow: hidden;
                    box-shadow: 0px 0px 4px 1px rgba(0,0,0,.06);
                }
                .scrollSwitch-detail {
                    font-size: 12px;
                    color: #999;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
            .select_li {
                box-shadow: 0px 0px 2px 2px #0077f2;
            }
        }
        &-right::-webkit-scrollbar {
            display: none;
        }
    }
}

// 幻灯片滚动区居右
.FunctionIntroductionScrollSwitch-right {
    .FunctionIntroductionScrollSwitch-content {
        flex-direction: row !important;
    }
}
// 幻灯片滚动区居左
.FunctionIntroductionScrollSwitch-left {
    .FunctionIntroductionScrollSwitch-content {
        flex-direction: row-reverse !important;
    }
}
// 幻灯片滚动区居上
.FunctionIntroductionScrollSwitch-top {
    .FunctionIntroductionScrollSwitch-content {
        flex-direction: column !important;
        &-left {
            margin: auto;
        }
        &-right {
            display: flex;
            margin: 40px auto 0;
            width: 780px;
            height: auto;
            white-space: nowrap;
        }
    }
}
// 幻灯片滚动区居下
.FunctionIntroductionScrollSwitch-bottom {
    .FunctionIntroductionScrollSwitch-content {
        flex-direction: column-reverse !important;
        &-left {
            margin: 40px auto 0;
        }
        &-right {
            display: flex;
            margin: auto;
            width: 780px;
            height: auto;
            white-space: nowrap;
        }
    }
}