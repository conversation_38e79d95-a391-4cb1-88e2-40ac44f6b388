import { eConsoleNodeId, eEnableFlg, ePagination } from "@/utils/enum";
import { useQuerySetting407_getCodeValueList } from "@/views/issueTrack/service/issueHooks";
import { useCriteriaList, useIssueSearchParams } from "@/views/issueTrack/service/issueSearchHooks";
import { Layout, Spin } from "antd";
import { useEffect, useState } from "react";
import { Outlet, useAsyncValue, useLocation } from "react-router-dom";
import { useQueryIssue017_getIssueList, useQueryIssue511_getPageById, useQuerySetting409_getTeamAttrgrpProps, useQueryTeam571_GetSpaceVaildUserList  } from "../../service/issueHooks";
import { getViewModePath } from "../../utils/ArrayUtils";
import "./IssueHome.scss";
import IssueTopSearchBar from "./IssueTopSearchBar";
import IssueTotal from "./IssueTotal";
import {
  issue_512_get_issue_total_query
} from "@/api/query/query"
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthContext } from "@/context/AppAuthContextProvider";

export default function IssueHome() {

  const authActions = useAuthContext();

  console.log("isAuthenticated", authActions?.isAuthenticated);
  
  const location = useLocation();
  const teamId = process.env.REACT_APP_WORKORDER_TEAMID;
  const issueListNodeId = process.env.REACT_APP_WORKORDER_NODEID;
  const {  issue506Result, queryKeywords, /* setting320Result, userList, objInfo,  */criteriaList: _criteriaList } = useAsyncValue(); //预加载获取的数据
  const { data: selectionList, isLoading: isLoadingCodeValueList, refetch: refechCodeValueList } = useQuerySetting407_getCodeValueList(teamId); //字典数据
  //自定义表单属性列表
  const { subclassAttrList, isLoading: isLoadingGetSubclassAttrs } = useQuerySetting409_getTeamAttrgrpProps(teamId, issueListNodeId, issue506Result?.subclassNid, !!issue506Result?.subclassNid);

  const { data: spaceUserList, } = useQueryTeam571_GetSpaceVaildUserList(teamId, issueListNodeId, eEnableFlg.disable, ); //字典数据

  const viewMode = getViewModePath(location.pathname); //视图回显
  const { issueNodeId, issueQueryId, navId, setIssueSearchParams } = useIssueSearchParams(); //issue路由配置，页面刷新
  const [_issueList, setIssueList] = useState(null); //issue长短列表数据
  const [_totalCnt, setTotalCnt] = useState(0); //查询结果集总的记录数，分页加载数据
  const [ countType, setCountType] = useState(); //工单用的统计类型
  const [iconSelectionLid, setIconSelectionLid] = useState(null); //icon列表id

  // 搜索条件相关
  let pageSize = ePagination.PageSize_30; //默认pageSize为30
  const [criteriaList, updateQuery] = useCriteriaList({ teamId, _criteriaList }); //查询条件列表
  const [pageNo, setPageNo] = useState(1); //页码
  const [keywords, setKeywords] = useState(""); //关键字
  const [orderBy, setOrderBy] = useState(eConsoleNodeId.Nid_11118_Issue_CreateDate); //排序，创建时间
  const [ascendingFlg, setAscendingFlg] = useState(false); //false:倒序,true:正序
  // 定位信息，查询条件及页码
  const { queryPage } = useQueryIssue511_getPageById(teamId, issueListNodeId, (issueNodeId), criteriaList, pageSize, keywords, orderBy, ascendingFlg, (!!issueNodeId)); //获取页码

  const [currentIssueIdx, setCurrentIssueIdx] = useState(0); // 用于切换页码时，加载数据前，先设置选中的issue索引,上一条为当前页的最后一条，下一条为当前页的第一条

  const [previousIssueLinkDisabledFlg, setPreviousIssueLinkDisabledFlg] = useState(false);
  const [nextIssueLinkDisabledFlg, setNextIssueLinkDisabledFlg] = useState(false);

  const [createIssueModalVisibleFlg, setCreateIssueModalVisibleFlg] = useState(false); //是否显示新建issue对话框

  const { issueList, tableColumn, totalCnt, isLoading: isLoadingIssueList, refetch: refetchIssueList } =
    useQueryIssue017_getIssueList(teamId, issueListNodeId, pageNo, pageSize, criteriaList, keywords, orderBy, ascendingFlg, countType, true);

  // issue-512 get_issue_total 按条件查询工单total 注意：无需放在loader中，1.影响页面加载速度，2.在不更新路由的情况下，无法重新刷新数据
  const {data: issueTotal = {}} = useQuery(issue_512_get_issue_total_query(teamId, issueListNodeId)); // 

  // 监听queryKeywords
  useEffect(() => {
    setKeywords(queryKeywords)
  }, [queryKeywords])

  // 监听issuePage
  useEffect(() => {
    if (queryPage) {
      setPageNo(queryPage)
    }
  }, [queryPage])

  // 找到当前issue详情
  function findByIssueId(data, nodeId) {
    let nodeItem = null
    data.find((el, index) => {
      if (el.nodeId == nodeId) {
        el.index = index
        return nodeItem = el
      }
    })
    return nodeItem
  }

  useEffect(() => {
    if (issueList) {
      issueList.forEach(_issue => _issue.key = _issue.nodeId); //增加key属性，IssueLongList表格需要用key属性
      // 保存工单类别iconSelectionLid
      tableColumn.forEach(el => {
        if (el.attrNid == eConsoleNodeId.Nid_11109_Issue_Type) {
          setIconSelectionLid(el.selectionLid)
        }
      })
      setIssueList(issueList)
      setTotalCnt(totalCnt)
      // issueList 无数据，则进行提示
      if (totalCnt == 0) {
        // globalUtil.info("暂无数据!");
        setIssueSearchParams({issueNodeId: null})
      } else {
        // issueList 有数据，则进行数据处理
        // 根据issueId 路由跳转issue详情
        if (issueNodeId) {
          // 查找列表中是否有issueId
          let foundNode = findByIssueId(issueList, (issueNodeId))
          if (foundNode) {
            return;
          } 
        } 
        const _issueNodeId = issueList[currentIssueIdx]?.nodeId;
        setIssueSearchParams({issueNodeId: _issueNodeId})
      }
    }
  }, [issueList]);

  //issue编号发生变化, 判定是否可以上一条和下一条
  //不能使用currentIssueIdx来判定，因为鼠标点击某一个issue时，currentIssueId发生变化，需要重新计算其对应的index
  useEffect(() => {
    if (issueList) {
      let _idx = issueList.findIndex(_issue => _issue.nodeId == issueNodeId);
      setPreviousIssueLinkDisabledFlg(pageNo == 1 && _idx == 0);
      setNextIssueLinkDisabledFlg(pageNo == Math.ceil(totalCnt / pageSize) && _idx == issueList?.length - 1);
    }
  }, [issueList, issueNodeId])

  // 上一条
  function gotoPreviousIssue() {
    const currentIssueIdx = issueList.findIndex(_issue => _issue.nodeId == issueNodeId);
    if (currentIssueIdx == 0) {  //当前页的最后一行记录，再往前即需要向前翻页
      if (pageNo > 1) {
        setPageNo(pageNo - 1); //触发 useQueryIssue017_getIssueList 再次加载
        setCurrentIssueIdx(pageSize - 1);
      }
    } else {
      setIssueSearchParams({issueNodeId: issueList[currentIssueIdx - 1].nodeId});
    }
  }

  // 下一条
  function gotoNextIssue() {
    const currentIssueIdx = issueList.findIndex(_issue => _issue.nodeId == issueNodeId);
    if (currentIssueIdx == pageSize - 1) { //当前页的最后一行记录，再往后即需要向后翻页
      if (pageNo < Math.ceil(_totalCnt / pageSize)) {
        setPageNo(pageNo + 1);
        setCurrentIssueIdx(0);
      }
    } else {
      setIssueSearchParams({issueNodeId: issueList[currentIssueIdx + 1].nodeId});
    }
  }

  if (isLoadingGetSubclassAttrs || isLoadingCodeValueList) {
    return
  }

  return <>
    {/* 头部搜索栏 */}
    <IssueTopSearchBar
      subclassAttrList={subclassAttrList}
      subclassNid={issue506Result?.subclassNid}
      selectionList={selectionList}
      projectId={issue506Result?.projectId}
      keywords={keywords}
      setKeywords={setKeywords}
      setPageNo={setPageNo}
      criteriaList={criteriaList}
      updateQuery={updateQuery}
      refechCodeValueList={refechCodeValueList}
      setCountType={setCountType}
      // setting320Result={setting320Result}
      // userList={userList}
      // objInfo={objInfo}
    />
    {/* 短列表/长列表/看板 视图 */}
    <Layout style={{ flex: "auto", backgroundColor: "#fff", height: 0 }}>
      <Spin spinning={isLoadingIssueList} wrapperClassName="issueSpin">
        <Outlet context={{
          issue506Result, subclassAttrList, selectionList, spaceUserList,/* userList, objInfo, */ issueList: _issueList, attrList: tableColumn, totalCnt: _totalCnt, pageNo, setPageNo, criteriaList,
          iconSelectionLid, issueNodeId,  previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg, keywords, gotoPreviousIssue, gotoNextIssue,
          ascendingFlg, setAscendingFlg, orderBy, setOrderBy, viewMode, refetchIssueList, setIssueSearchParams, navId,
          createIssueModalVisibleFlg, setCreateIssueModalVisibleFlg, pageSize, setCurrentIssueIdx, countType
        }} />
      </Spin>
    </Layout>
    {/* 工单总数 */}
    {/* tmsbug-4655：工单模块，未登录时的UI部分隐藏，新建工单和评论时需弹出登录对话框*/}
    {
       /* team619Data.resultCode != 200 */
     authActions.isAuthenticated  && <IssueTotal issueTotal={issueTotal} setCreateIssueModalVisibleFlg={setCreateIssueModalVisibleFlg} countType={countType} setCountType={setCountType}/>
    }
  </>;
}