import "./ViewUtils.scss";

// 格式化svg
export function formatSvg(svgUrl, ...props) {
  if(!!svgUrl){
    return <div className={`format-svg`} dangerouslySetInnerHTML={{ __html: svgUrl }} {...props}/>;
  }
    return <></>;
}

// 只有当子元素全部出现在父元素中时，才会返回true。
export function isContain(dom) {
  if(!dom){ // 未找到该元素，则不在可视区域内
    return false;
  }
  const totalHeight = window.innerHeight || document.documentElement.clientHeight;
  const totalWidth = window.innerWidth || document.documentElement.clientWidth;
  // 当滚动条滚动时，top, left, bottom, right时刻会发生改变。
  const { top, right, bottom, left } = dom.getBoundingClientRect();
  console.log(top, right, bottom, left)
  return (top >= 0 && left >= 0 && right <= totalWidth && bottom <= totalHeight);
}

// 滚动至锚点
export  const scrollToAnchor = (target) => {
    if (target) {
      // 添加focus样式
      target.classList.add("focus");
      target.scrollIntoView({
        behavior: "smooth",  // 平滑过渡
        block: "start"
      });
    }
}