/* eslint-disable jsx-a11y/anchor-is-valid */
import { EllipsisOutlined } from "@ant-design/icons";
import { team_530_get_obj_node_info_query, setting_407_get_console_selection_list_query } from "@/api/query/query";
import AppNodeResourceIcon from "@/components/AppNodeResourceIcon";
// import ContextBoard from "@/components/ContextBoard";
// import PagePriv from "@/components/PagePriv";
import { eInputMaxLength, eTreeOpType } from "@/utils/enum";
import { globalEventBus } from "@/utils/eventBus";
import { eCtxTypeId, eNodeTypeId, getColorByType } from "@/utils/TsbConfig";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button, Input, Space } from "antd";
import { useEffect, useRef, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { getSysIconList } from "@/utils/ArrayUtils";
import { formatSvg } from "@/utils/ViewUtils";
import { isEmpty } from "@/utils/ArrayUtils";
import * as qs from "qs"
import "./index.scss";

export default function PageTitle({ 
    teamId = process.env.REACT_APP_WORKORDER_TEAMID, 
    nodeId, 
    showMoreIcon = true,
    onMoreBtnClick: _onMoreBtnClick, 
    onCreateBtnClick:_onCreateBtnClick,
    showIcon = true,
    title, // 标题
    // powerLock = false, 
  }) {

  const params = useParams();

  const contextboardRef = useRef(null)

  const queryClient = useQueryClient();

  const [initFlg , setInifFlg] = useState(true);
  const [nodeData , setNodeData] = useState({});

  const { data: _nodeData = {} } = useQuery(team_530_get_obj_node_info_query(teamId, nodeId));

  const { data: selectionList, isFetching: isLoadingCodeValueList } = useQuery({...setting_407_get_console_selection_list_query(teamId)}) //字典数据

  const inputNameRef = useRef(null)

  useEffect(() => {
    if(!isEmpty(_nodeData)){
      if(!!title && initFlg ){ // 初始化值取title
        setNodeData({..._nodeData, label: title, nodeName: title}); //label：显示标题，nodeName：重命名
        if(_nodeData.isRenaming){ // 第一次重命名时，取初始值
          setInifFlg(false);
        }
      } else {
        setNodeData({..._nodeData});
      }
    }
  }, [qs.stringify(_nodeData), title])

  // isRenaming状态在objNodeMoreOps、中设置重命名状态时通过更改team_530_get_obj_node_info_query实现数据传递
  useEffect(() => {
    if (nodeData.isRenaming) {
      inputNameRef.current?.focus() // 聚焦
      inputNameRef.current?.select() // 内容全选
    }
  }, [nodeData.isRenaming])

  // 系统图标
  const sysIconList = useMemo(() => {
    return getSysIconList(selectionList)
  }, [selectionList]);

  // 重命名
  const onNameChanged = (e) => {
    // 不写或者名称与原先相同都不会重命名
    onMoreBtnClick({ nodeItem: nodeData, ctxType: eCtxTypeId.ctx_100_rename_confirmed, name: e.target.value, treeOpType: eTreeOpType.opTitle})
  }

  // 显示右击菜单
  const onShowMoreButtonClick = (info) => {
    console.log("onShowMoreButtonClick info", info)
    contextboardRef.current.showContextBoard(info.event.nativeEvent || info.event, info.node, info.node.nodeId, info.needCreateFlg)
  }

  // 新建资源
  const onCreateBtnClick = (args) => {
    if(_onCreateBtnClick){  // 有传值则直接调用
      return _onCreateBtnClick(args)
    }
    if(nodeId == params.nodeId){// 判断是左侧的资源树还是右侧的资源树， nodeId等于左树的nodeId即为左树资源
      return globalEventBus.emit("onCreateBtnClick", "", { ...args, treeOpType: eTreeOpType.opTitle});
    } 
    // 中树
    globalEventBus.emit("onMiddleTreeCreateBtnClick", "", { ...args, treeOpType: eTreeOpType.opTitle});
  }

  // 更多操作
  const onMoreBtnClick = (args) => {
    if(_onMoreBtnClick){ // 有传值则直接调用
      return _onMoreBtnClick(args)
    } 
    if(nodeId == params.nodeId){// 判断是左侧的资源树还是右侧的资源树， nodeId等于左树的nodeId即为左树资源
     return globalEventBus.emit("onMoreBtnClick", "", { ...args, treeOpType: eTreeOpType.opTitle});
    } 
    // 中树
    globalEventBus.emit("onMiddleTreeMoreBtnClick", "", { ...args, treeOpType: eTreeOpType.opTitle});
  }

  // 标题
  const nodeTitle = (
    <div
      // onContextMenu={(e) => {
      //   e.preventDefault();
      //   e.stopPropagation();
      //   onShowMoreButtonClick && onShowMoreButtonClick({
      //     event: e,
      //     node: nodeData,
      //     needCreateFlg: true,
      //   })
      // }}
    >
      <Space size={5}>
        {/* issue图标暂未处理好，不显示 */}
        {
          // TODO: 图标字体大小更改
          !!showIcon && <AppNodeResourceIcon
          nodeType={nodeData?.nodeType}
          style={{ color: getColorByType(nodeData.rightFlgIconType) ? getColorByType(nodeData.rightFlgIconType) : '#666',  }}
        />
        }
        <span
          className={`tree-dir-title tree-dir-title-bold fontsize-20 ${nodeData.nameTextStrikeFlg == '1' ? "tree-dir-title-delete" : ""}`}
          style={{ color: getColorByType(nodeData.nameTextColorType) ? getColorByType(nodeData.nameTextColorType) : '#333' }}>
          {nodeData.label}
        </span>
      </Space>
    </div>
  )

  return (
    <>
      {/* {
        // data-content-key 用于获取当前节点的key, 保证编辑状态也能获取到
        nodeData?.isRenaming && <div data-title-key={nodeData.key}>
          <Input ref={inputNameRef}
            placeholder=""
            autoComplete="off"
            defaultValue={nodeData.label}
            onBlur={onNameChanged} 
            onPressEnter={onNameChanged}
            onClick={(e) => { e.preventDefault(); e.stopPropagation(); }}
            maxLength={eInputMaxLength.twenty}
          />
        </div>
      } */}
      {
        !nodeData?.isRenaming && <>
          <div className="tree-dir-flex flexaotu" data-title-key={nodeData.key}>
            <Space size = {5}>
              {nodeTitle}
              {formatSvg(sysIconList.find(sys => sys.propType == nodeData.nodeIconType)?.propValue) }
            </Space>
            {/* 更多 */}
            {/* <span className="tree-dir-more-action" >
              {showMoreIcon &&
                <Button type="link"
                  icon={<EllipsisOutlined />}
                  // 选中时固定显示更多按钮
                  className={`tree-dir-color tree-dir-more-btn`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onShowMoreButtonClick && onShowMoreButtonClick({
                      event: e,
                      node: nodeData,
                      needCreateFlg: true, // 标题右击菜单需要创建操作
                    })
                  }} />
              }
            </span> */}
            {/* 锁 */}
            {/* {powerLock && <PagePriv teamId={teamId} nodeId={nodeId} />} */}
          </div>
        </>
      }

      {/* 右击菜单:id需要唯一 */}
      {/* <ContextBoard ref={contextboardRef} teamId={teamId} onMoreBtnClick={onMoreBtnClick} onCreateBtnClick={onCreateBtnClick} id={nodeId} treeOpType={eTreeOpType.opTitle} /> */}
    </>
  );
}
