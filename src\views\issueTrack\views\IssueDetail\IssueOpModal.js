import React, { useRef, useState } from "react";
import { Button, Form, message, Modal, Select, Table, Upload } from "antd";
import TEditor from "@/components/TEditor/TEditor";
// import ResourceUse from "@/components/ResourceUse";
import { eNodeTypeId } from "@/utils/TsbConfig";
// import AppNodeResourceIcon from "@/components/AppNodeResourceIcon";
import { PlusOutlined } from '@ant-design/icons';
import { getCodeValueListByCode } from "@/utils/ArrayUtils";
import { globalUtil } from "@/utils/globalUtil";
import { useMutationTeam511delObjRel } from "@/views/issueTrack/service/issueHooks";
const { Option } = Select;

//issue操作对话框
export default function IssueOpModal({ teamId, visible, /* userList, */ issueNodeId,  issueStatus, selectionList, issueStatusAttr, onOk, onCancel }) {
    const [objRelModalVisibleFlg, setObjRelModalVisibleFlg] = useState(false);
    const [status, setStatus] = useState(""); // 状态
    const [opDescription, setOpDescription] = useState(""); // 描述
    const [attachmentList, setAttachmentList] = useState([]); //附件列表
    const [objRelList, setObjRelList] = useState([]);  //资源引用
    const { delObjRelMutation } = useMutationTeam511delObjRel()
    let editRef = useRef(null);
    const props = {
        name: 'file',
        multiple: true,
        action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`,
        data: { teamId: teamId, moduleName: "issues", nodeId: issueNodeId, objType: eNodeTypeId.nt_31704_objtype_issue_item },
        onChange: uploadFile
    };
    const layout = {
        labelCol: {
            span: 2,
        },
        wrapperCol: {
            span: 20,
        },
    };
    function onStatusChanged(e) {
        issueStatus = e;
        setStatus(e)
    }
    //资源引用表格columns
    // const columns = [
    //     {
    //         title: '名称', dataIndex: 'name', key: 'name',
    //         render: (text, record, index) => <>
    //             <AppNodeResourceIcon nodeType={record.objType} className="fontsize-16" style={{ color: "#3279fe", opacity: 0.4 }}/>
    //             {text}
    //         </>
    //     },
    //     { title: '类型', dataIndex: 'nodeTypeName', key: 'nodeTypeName', },
    //     { title: '创建人', dataIndex: 'userName', key: 'userName', },
    //     { title: '创建时间', dataIndex: 'createDt', key: 'createDt', },
    //     { title: '操作', dataIndex: 'operation', key: 'operation', render: (text, record) => <a onClick={() => deleteResource(record)}>删除</a> }
    // ];

    // 删除资源引用
    const deleteResource = (data) => {
        setObjRelList(objRelList.filter(item => item.obj2Id != data.obj2Id));
        // let request = { teamId: teamId, objRelId: data?.objId };
        // console.log(data)
        // delObjRelMutation(request)
    }
    // 勾选了哪几个资源引用
    function onObjRelSelected(_selectedObjRelList) {
        _selectedObjRelList.map((item, index) => {
            item.objId = item.obj2Id
            item.objType = item.nodeType
            item.objName = item.name
            item.seqNo = index
        })
        setObjRelList([...objRelList, ..._selectedObjRelList]);
        setObjRelModalVisibleFlg(false);  //关闭弹窗
    }

    // 编辑器
    function editContentChange(e) {
        setOpDescription(editRef.current.getContent())
    }

    // 编辑器@UserName
    function getEditorUserList(data) {
        return (data || []).map(item => ({ key: item.userName, value: item.userName, id: item.userId.toString() }));
    }

    function uploadFileCallBack(data) {
        let link = data.link
        let name = link.substring(link.lastIndexOf("/") + 1)
        setAttachmentList([...attachmentList, { objId: data.id, objType: 23, objName: name, seqNo: 1 }]);
    }

    function uploadFile(info) {
        if (info.file.status !== 'uploading') {
            console.log('uploading', info.file, info.fileList);
        }
        if (info.file.status === 'done') {
            if (info.file.response.resultCode == 200) {
                let fileObj = { objId: info.file.response.id, objType: 23, objName: info.file.name, seqNo: 1 }; //objType: 23 这个参数应该不需要再传了，原先是放在objRel表的，现在放到了各自的业务表
                setAttachmentList([...attachmentList, fileObj]);
                globalUtil.success(`${info.file.name} 文件上传成功！`);
            }
        } else if (info.file.status === 'error') {
            console.log('error', info.file, info.fileList);
        }
    }

    return <Modal title="操作" className="tms-modal"
        width={900}
        destroyOnClose
        maskClosable={false}
        open={visible}
        onOk={(e) => onOk(status, opDescription, attachmentList, e)}
        onCancel={onCancel}
        okText="确定"
        cancelText="取消">
        <Form {...layout} name="control-hooks">
            <Form.Item name="subClassId" label="状态">
                <Select placeholder="请选择" defaultValue={issueStatusAttr.itemValue} onChange={onStatusChanged}>
                    {
                        getCodeValueListByCode(selectionList, issueStatusAttr.id).map((item, index) => {
                            return <Option key={index} value={item.propType}>{item.propValue}</Option>
                        })
                    }
                </Select>
            </Form.Item>
            <Form.Item name="name" label="描述">
                <TEditor ref={editRef}
                    placeholderText=""
                    contentChanged={(e) => editContentChange(e)}
                    uploadParams={{ teamId, nodeId: issueNodeId, moduleName: "issues",  objType: eNodeTypeId.nt_31704_objtype_issue_item}}
                    uploadCallback={uploadFileCallBack}/>
            </Form.Item>
            <Form.Item name="fujian" label="附件">
                <Upload {...props}>
                    <Button type="link" icon={<PlusOutlined />}>上传附件</Button>
                </Upload>
            </Form.Item>
            {/* <Form.Item name="ziyuan" label="资源引用">
                <Button type="link" icon={<PlusOutlined />} onClick={() => setObjRelModalVisibleFlg(true)}>资源引用</Button>
                {objRelList.length > 0 &&
                    <Table className="custome-table"
                        columns={columns}
                        dataSource={objRelList}
                        size={"small"}
                        pagination={false} />
                }
            </Form.Item> */}
        </Form>
        {/* {objRelModalVisibleFlg &&
            <ResourceUse teamId={teamId}
                objType={eNodeTypeId.nt_31704_objtype_issue_item}
                showResource={objRelModalVisibleFlg}
                onOk={onObjRelSelected}
                onCancle={() => setObjRelModalVisibleFlg(false)} />
        } */}
    </Modal>;
}