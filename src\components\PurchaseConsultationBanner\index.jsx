import React, { useEffect, useState, useRef } from "react";
import {
  Box,
  Typography,
  Button,
} from '@mui/material';
import "./index.scss";
import { skipOS } from "../../utils/commonUtils";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import bannerBgImage from "../../assets/images/banner_bg.png";
import { useAuthContext } from "@/context/AppAuthContextProvider";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../utils/commonUtils";
import { formatVariantColor } from "../../utils/muiThemeUtils";


// 免费使用和购买咨询
export default function PurchaseConsultationBanner({data}){
  const navigate = useNavigate();
  const authActions = useAuthContext();
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  },[data?.widgetList])

  return (
    <Box sx={{
      padding: '50px 100px',
      background: `url(${bannerBgImage}) no-repeat top left`,
      backgroundSize: '100% 100%'
    }}>
      <Box className="use-consulting">
        <Box className="use-consulting-left">
          {!!filterWidgetList(moduleData, 1)[0]?.value && <Box className="use-consulting-left-title">
            <Typography variant="h5" sx={{ color: 'white' }}>{filterWidgetList(moduleData, 1)[0]?.value}</Typography>
          </Box>}
          {!!filterWidgetList(moduleData, 2)[0]?.value && <Box sx={{ mt: '20px', fontSize: '18px' }}>
            <Typography sx={{ color: 'white' }} variant="subtitle1">
              {filterWidgetList(moduleData, 2)[0]?.value}
            </Typography>
          </Box>}
        </Box>
        {filterWidgetList(moduleData, 4).length > 0 && <Box className="use-consulting-right">
          {filterWidgetList(moduleData, 4).map((item, index) => (
            <Button 
              key={item.widgetId}
              className={["fontsize-18", item.linkUrl === "os" ? "purchase-consulting-btn": "free-use-btn"].join(" ")} 
              variant={formatVariantColor(item.style)?.variant}
              color={formatVariantColor(item.style)?.color}
              onClick={() => {
                item.linkUrl === "os"
                 ? skipOS(authActions.isAuthenticated)
                 : navigate(item.linkUrl)
              }}
            >
              {item.value}
            </Button>
          ))}
        </Box>}
      </Box>
    </Box>
  )
}