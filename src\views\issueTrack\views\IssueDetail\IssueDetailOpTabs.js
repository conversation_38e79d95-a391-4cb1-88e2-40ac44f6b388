import CommonComment from "@/components/CommonComment/CommonComment";
import { eNodeTypeId } from "@/utils/TsbConfig";
import { Tabs } from "antd";
import { useState } from "react";

//Issue详情: 评论列表 + 操作历史列表
export default function IssueDetailOpTabs({ teamId, issueNodeId, }) {
    const [commentCount, setCommentCount] = useState("");//评论数量

    // 评论数回显
    function totalCallBack(total) {
        setCommentCount(total)
    }

    const tabs = [
        {
            label: commentCount ? `评论(${commentCount})` : `评论`,
            key: 'tab-1',
            children: <CommonComment uploadParams={{ teamId, nodeId: issueNodeId, moduleName: "issues", objType: eNodeTypeId.nt_31704_objtype_issue_item }} totalCallBack={totalCallBack} />
        },
    ];
    return <Tabs defaultActiveKey="1" className="issue-detail-tabs" items={tabs} />;
}