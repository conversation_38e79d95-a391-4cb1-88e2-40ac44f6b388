export const FOLDER_INFO = {
  FOLDER_302: 302,
  FOLDER_302105: 302105,
  FOLDER_302106: 302106,
  FOLDER_302107: 302107,
  FOLDER_302109: 302109,
  FOLDER_302316: 302316,
  FOLDER_302317: 302317,
  FOLDER_30231703: 30231703,
  FOLDER_302500: 302500,
  FOLDER_302118: 302118,
  FOLDER_302312: 302312,
  FOLDER_312: 312,
}

export const DOC_INFO = {
  DOC_311: 311,
  DOC_31201: 31201,
}

// 帮助中心、视频介绍、软件下载文档库id
export const DOC_ID_INFO = {
  doc: process.env.REACT_APP_DOCUMENT_NODEID,
  software: process.env.REACT_APP_SOFTWARE_NODEID,
  video: process.env.REACT_APP_VIDEO_NODEID,
  os: process.env.REACT_APP_OS_NODEID
}

export function getDocNodeId(location) {
  if(location?.pathname.includes("doc")) return DOC_ID_INFO["doc"]
  if(location?.pathname.includes("video")) return DOC_ID_INFO["video"]
  if(location?.pathname.includes("software")) return DOC_ID_INFO["software"]
  if(location?.pathname.includes("os")) return DOC_ID_INFO["os"]
  return ""
}