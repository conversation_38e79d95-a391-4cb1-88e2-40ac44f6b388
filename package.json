{"name": "tms-front-portal", "version": "0.1.0", "private": true, "homepage": "./", "dependencies": {"@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@mui/icons-material": "^5.10.3", "@mui/lab": "^5.0.0-alpha.120", "@mui/material": "^5.11.10", "@tanstack/react-query": "^4.24.10", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^4.23.1", "antd-img-crop": "^4.6.0", "axios": "^0.27.2", "dotenv-cli": "^7.0.0", "fabric": "^5.2.4", "froala-editor": "^4.0.14", "highlight.js": "^11.6.0", "immer": "^9.0.15", "jquery": "^3.6.3", "json-bigint": "^1.0.0", "mobile-detect": "^1.4.5", "moment": "^2.29.4", "pikaday": "^1.8.2", "qs": "^6.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-froala-wysiwyg": "^4.0.14", "react-infinite-scroll-component": "^6.1.0", "react-player": "^2.16.0", "react-resizable": "^3.0.5", "react-router-dom": "^6.8.2", "react-scripts": "5.0.1", "react-slick": "^0.29.0", "react-swipeable-views": "^0.14.0", "sass": "^1.54.9", "slick-carousel": "^1.8.1", "tributejs": "^5.1.3", "use-immer": "^0.7.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "dotenv -e .env.local craco start", "build:dev": "dotenv -e .env.dev craco build", "build:qa": "dotenv -e .env.qa craco build", "build:prod": "dotenv -e .env.prod craco build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "assert": "^2.1.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "progress-bar-webpack-plugin": "^2.1.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.3", "webpack-retry-chunk-load-plugin": "^3.1.1", "webpackbar": "^5.0.2"}}