import React, { useEffect, useState, useRef } from "react";
import { useLocation } from "react-router-dom";
import {
  Box, IconButton, Typography, Button, List, ListItemText, ListItemIcon,
  ListItemButton, Table, TableBody, TableCell, TableContainer,
  TableHead, TableRow, Collapse, Link, Tooltip
} from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import "./PriceHome.scss";
import "../HomePage.scss"
import {
  portal_1101_get_news_list,
  team_1302_get_price_solution,
  team_1301_get_page_part_widget,
} from "../../api/http";
import TopMenuBar from "../../components/TopMenuBar";
import FooterBar from "../../components/FooterBar";
import CreateTeam from "./CreateTeam";
import ContactUsModal from "../../components/ContactUsModal";
import { skipOS } from "../../utils/commonUtils";
import { globalEventBus } from "../../utils/eventBus";
import {VersionDetailDrawer} from "../Versions";
import { useAuthContext } from "@/context/AppAuthContextProvider";
import PurchaseConsultationBanner from "../../components/PurchaseConsultationBanner"; // 购买咨询横幅
import {partTypeEnum} from 'src/utils/enum';

export default function Price(){
  const location = useLocation();
  const authActions = useAuthContext();
  const [solutionList, setSolutionList] = useState([]);
  const [priceDetailList, setPriceDetailList] = useState([]);
  const [newsList, setNewsList] = useState([]);
  const [contactUsOpen, setContactUsOpen] = useState(false);
  const newInfoRef = useRef();
  const [newDetailOpen, setNewDetailOpen] = useState(false);
  const [partList, setPartList] = useState([]);

  useEffect(() => {
    getPriceSolution(6666)
    getNewsList(6666, 32464199609684, 2, '1', '100')
    getPagePartWidget(6666, location.pathname)
  },[])

  const getPagePartWidget = (teamId, menuUrl) => {
    team_1301_get_page_part_widget({teamId, menuUrl}).then(res => {
      if(res.resultCode == 200){
        setPartList([...res.partList])
      }
    });
  }

  const formatTreeData = (cur=[],arr=[]) => {
    // 生成根目录
    if(cur.length == 0){
      // 格式化数据格式
      arr = arr.map(item => {
        return {
          key: item.id,
          ...item
        }
      })
      cur = arr.filter(item => arr.every(itemx => itemx.id != item.parentId));
    }
    cur.forEach(item => {
      let childs = arr.filter(itemx => itemx.parentId == item.id);
      if(childs.length){
        let turnChilds = formatTreeData(childs,arr);
        item.children = turnChilds;
      }
    });
    return cur;
  }

  const getPriceSolution = (teamId) => {
    team_1302_get_price_solution({teamId}).then(res => {
      if(res.resultCode == 200){
        setSolutionList([...res.solutionList]);
        setPriceDetailList([...res.priceDetailList]);
      }
    });
  }

  // 获取常见问题
  const getNewsList = (teamId, objNodeId, newsType, pageNum, pageSize) => {
    portal_1101_get_news_list({teamId, objNodeId, newsType, pageNum, pageSize}).then(res => {
      if(res.resultCode == 200){
        setNewsList([...res.newsList])
      }
    });
  }

  // 打开联系我们弹窗
  const openContactUsModal = () => setContactUsOpen(true)

  // 关闭联系我们弹窗
  const cancelContactUsModal = () => setContactUsOpen(false)

  const buyNow = () => {
    if(authActions.isAuthenticated) {
      let params = `?isEnterpriseEdition=1&durationMonth=1&userCnt=5&packageList=`
      let url_ = `${process.env.REACT_APP_TMS_URL}/allteam${params}`
      window.open(url_);
    } else {
      globalEventBus.emit("openLoginModal", "",);
    }
  }

  // 版本选择
  const versionSelection = (item) => {
    if(item.solutionType === 1) {
      skipOS(authActions.isAuthenticated)
    } else if(item.solutionType === 2) {
      turnPriceCalcution()
    } else {
      openContactUsModal()
    }
  }

  // 查看常见问题
  const lookIssue = (issue) => {
    if(!issue.linkUrl) {
      newInfoRef.current = issue.id
      setNewDetailOpen(true)
    } else {
      window.open(issue.linkUrl)
    }
  }

  // 跳转价格计算器
  const turnPriceCalcution = () => {
    let anchorElement = document.getElementById("price-calculation");
    anchorElement?.scrollIntoView()
  }

  function partIsExists(partType){
    return partList.filter(el => el.partType === partType).length > 0
  }

  function getTitleAndSubTitle(partType,widgetType){
    return (partList.find(el => el.partType === partType).widgetList||[]).find(widget => widget.widgetType == widgetType)?.value||''
  }

  return (
    <div className="price">
      <Box sx={{ height: 76 }}/>
      <Box sx={{ width: '100%',padding: '0 100px' }}>
        <Box 
        className="TopMenuBar-box-price">
          <TopMenuBar/>  
        </Box>
      </Box>

      {/* 选择最合适的方案-start */}
      {partIsExists(partTypeEnum.part_type_41_price_solution) && 
        <Box className="price-programme">
          <Box className="price-programme-top">
            <Typography variant="h5" sx={{ fontSize: '34px', fontWeight: '500' }}>{getTitleAndSubTitle(partTypeEnum.part_type_41_price_solution,1)||'选择最适合的方案'}</Typography>
            <Typography className="fontsize-16" sx={{ mt: "10px", mb: "40px", color: "#666" }} variant="body2">
              {getTitleAndSubTitle(partTypeEnum.part_type_41_price_solution,2)||'可以根据您团队规模与业务需求，选择相应版本套餐'}
            </Typography>
          </Box>
          <Box className="price-programme-content">
            {solutionList.map((solution,index) => (
              <Box sx={{ mt: solution.solutionType === 2 ? '0': '10px !important' }} className="price-programme-content-li" key={solution.id}>
                {solution.solutionType === 2 && <Box className={solution.solutionType === 2 ? "price-programme-select" : "price-programme-select-false"}/>}
                <Box className={"price-programme-content-li-heard " + (solution.solutionType === 2 ? 'major-heard' : solution.solutionType === 3 ? 'enterprise-heard' : '')}>
                  <Box className="price-programme-content-li-heard-name">{solution.title}</Box>
                  <Box className="price-programme-content-li-heard-title">{solution.subTitle}</Box>
                  {solution.solutionType === 2 && <Box className="price-programme-select-text">最多选择</Box>}
                </Box>
                <Box className="price-programme-content-li-body">
                  <Box className="package-description">
                    <Box sx={{ fontSize: '14px', fontWeight: 'bold' }}>{solution.solutionDesc}</Box>
                    {solution.solutionType === 2
                    ? <Button className="fontsize-14" size="small" sx={{ color: "#0077f2" }} onClick={turnPriceCalcution}>价格计算器</Button>
                      : <Button sx={{ visibility: 'hidden' }} className="fontsize-14" size="small">&nbsp;</Button>
                    }
                  </Box>
                  {solution.solutionType === 3 ? 
                    <Button className="free-version-contact-us" variant="contained" onClick={openContactUsModal}>{solution.priceDesc}</Button>
                  : (solution.solutionType === 1 ? 
                    <Button className="free-version-buy" onClick={() => skipOS(authActions.isAuthenticated)}>
                    <span style={{ color: '#0077f2' }}>{solution.priceDesc}</span>
                    </Button> 
                    : <Button className="free-version-buy" onClick={turnPriceCalcution}>
                    <span className="free-version-buy-money">{solution.priceDesc}</span>
                    <span className="free-version-buy-a" style={{ color: "#0077f2" }}>立即购买</span>
                    </Button>)
                  }
                  <Box className="include-functions">
                    {/* <Box className="include-functions-title">{solution.functionDesc}</Box> */}
                    <Box className="include-functions-content">
                      <Box className="include-functions-content-li">{solution.functionDesc}</Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      }
      {/* 选择最合适的方案-end */}

      {/* 完整方案对比-start */}
      {partIsExists(partTypeEnum.part_type_42_price_matrix) && 
        <Box className="complete-scheme" href="#price-calculation">
          <Typography variant="h5" sx={{ fontSize: "34px" }}>{getTitleAndSubTitle(partTypeEnum.part_type_42_price_matrix,1)||'完整方案对比'}</Typography>
          <Typography sx={{ mt: "10px", color: "#666" }} variant="body2">
            {getTitleAndSubTitle(partTypeEnum.part_type_42_price_matrix,2)||'可以根据您团队规模与业务需求，选择相应版本套餐'}
          </Typography>
          <Box className="complete-scheme-table">
            <Box className="complete-scheme-table-head">
              <Box sx={{ display: 'flex', alignItems: 'center' }} className="complete-scheme-table-head-li">
                <Typography className="fontsize-18" variant="h6">功能明细</Typography>
                {/* <KeyboardDoubleArrowDownIcon className="fontsize-18"/> */}
              </Box>
              {solutionList.map((item, index) => (
                <Box key={item.id} className="complete-scheme-table-head-li">
                  <Typography className="fontsize-18" variant="h6">{item.title}</Typography>
                  <Button 
                    size="small" 
                    variant={item.solutionType === 3 ? "contained" : "outlined"} 
                    sx={item.solutionType === 3 ? { backgroundColor: '#0077f2' } : { color: "#0077f2", borderColor: "#0077f2" }}
                    onClick={() => versionSelection(item)}
                  >
                    {item.priceDesc}
                  </Button>
                </Box>
              ))}
            </Box>
            {formatTreeData([], priceDetailList || []).map((price, index) => (
              <CompleteSchemeTable key={price.key} price={price}/>
            ))}
          </Box>
        </Box>
      }
      {/* 完整方案对比-end */}

      {/* 价格计算器-start */}
      {partIsExists(partTypeEnum.part_type_43_price_calculator) && 
        <Box className="price-calculation" id="price-calculation">
          <Box className="price-calculation-top">
            <Typography variant="h5" sx={{ fontSize: "34px", color: 'white' }}>{getTitleAndSubTitle(partTypeEnum.part_type_43_price_calculator,1)||'价格计算器'}</Typography>
            <Typography sx={{ mt: "10px" }} variant="body2">
              {getTitleAndSubTitle(partTypeEnum.part_type_43_price_calculator,2)||'输入VIP产品的VIP人数以及选择购买时长，获得相应的价格信息'}
            </Typography>
          </Box>
          <Box className="price-calculation-content">
            {/*teamId={6666}*/}
            <CreateTeam  type={0}/>
          </Box>
        </Box>
      }
      {/* 价格计算器-end */}

      {/* 购买常见问题-start */}
      {partIsExists(partTypeEnum.part_type_44_price_question) && newsList.length > 0 ? 
        <Box className="common-problem">
          <Typography sx={{ textAlign: 'center' }} variant="h5">{getTitleAndSubTitle(partTypeEnum.part_type_44_price_question,1)||'购买常见问题'}</Typography>
          <Box className="common-problem-content">
            <List
            className="common-problem-content-list"
            sx={{ width: '100%' }}
            // component="nav"
            aria-labelledby="nested-list-subheader"
            >
              {newsList.map((issue, index) => (
                <ListItemButton key={issue.id} onClick={() => lookIssue(issue)}>
                  <ListItemText primary={issue.title} />
                  <ListItemIcon>
                    <ArrowForwardIosIcon />
                  </ListItemIcon>
                </ListItemButton>
              ))}
            </List>
          </Box>
        </Box>
      :
        null
      }
      {/* 购买常见问题-end */}

      {/* 免费使用和购买咨询-start */}
      {partIsExists(partTypeEnum.part_type_8_home_claim) && <PurchaseConsultationBanner data={partList.filter(el => el.partType === partTypeEnum.part_type_8_home_claim)[0]}/>}
      {/* 免费使用和购买咨询-start */}

      <FooterBar/>
      <ContactUsModal open={contactUsOpen} onCancel={cancelContactUsModal}/>
      <VersionDetailDrawer objNodeId={newInfoRef.current} open={newDetailOpen} popUpNews={newsList} onClose={() => setNewDetailOpen(false)}/>
    </div>
  )
}

// 完整方案对比table
function CompleteSchemeTable(props) {
  const { price } = props;
  return (
    <TableContainer>
      <Table>
        <TableHead className="CompleteSchemeTable-head">
          <TableRow className="CompleteSchemeTable-head-row">
            <TableCell align="left">{price.title}</TableCell>
            <TableCell align="center">基础版(免费)</TableCell>
            <TableCell align="center">Vip版</TableCell>
            <TableCell align="center">私有部署</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {(price?.children || []).map((priceRow, index) => (
            <CompleteSchemeTableRow key={priceRow.key} priceRow={priceRow} />
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}
// 完整方案对比table-row
function CompleteSchemeTableRow(props) {
  const { priceRow } = props;
  const [open, setOpen] = useState(false);
  return (
    <>
      <TableRow className="CompleteSchemeTableRow-row">
        <TableCell component="th" align="left" scope="row">
          <IconButton
          sx={{ padding: 0, visibility: !priceRow?.children ? "hidden" : "visible" }}
          size="small"
          onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
          {priceRow.title}
          {!!priceRow.titleDesc && <span dangerouslySetInnerHTML={{ __html: decodeURIComponent(priceRow.titleDesc) }}/>}
          {
            priceRow.titleInfo && 
            <Tooltip title={priceRow.titleInfo}>
              <ErrorOutlineIcon className="fontsize-20" sx={{ ml: '4px', color: '#666', verticalAlign: 'text-bottom', transform: 'rotate(180deg)'}}/>
            </Tooltip>
          }
          
        </TableCell>
        <TableCell align="center">
          {/* {
           priceRow.valueFree
           ? <span style={{ color: '#70b603' }} className="iconfont gou fontsize-12"/>
           : <span style={{ color: '#d9001b' }} className="iconfont cuocha_kuai fontsize-12"/>
          } */}
          <div dangerouslySetInnerHTML={{ __html: decodeURIComponent(priceRow.valueFree) }}/>
        </TableCell>
        <TableCell align="center">
          <div dangerouslySetInnerHTML={{ __html: decodeURIComponent(priceRow.valueBusiness) }}/>
        </TableCell>
        <TableCell align="center">
          <div dangerouslySetInnerHTML={{ __html: decodeURIComponent(priceRow.valuePrivate) }}/>
        </TableCell>
      </TableRow>
      {open && <TableRow className="CompleteSchemeTableRow-row-table">
        <TableCell colSpan={24}>
          <Collapse in={open} timeout="auto" >
            <Table className="CompleteSchemeTableRow-childNode-table">
              <TableBody>
                {(priceRow?.children || []).map((priceRowSon) => (
                  <TableRow key={priceRowSon.key}>
                    <TableCell component="th" scope="row">
                      {/* {priceRowSon.title} */}
                      <Box className="CompleteSchemeTableRow-value">
                        <Box className="table-row-left">
                          <span className="table-row-left-title">{priceRowSon.title}</span>
                          {!!priceRowSon.tags
                           &&
                           <div dangerouslySetInnerHTML={{ __html: decodeURIComponent(priceRowSon.tags) }}/>
                            }
                            {!!priceRowSon.titleDesc && <span dangerouslySetInnerHTML={{ __html: decodeURIComponent(priceRowSon.titleDesc) }}/>}
                        </Box>
                        {!!priceRowSon.linkUrl && <Link className="table-row-right" href={priceRowSon.linkUrl} underline="none">更多</Link>}
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <div dangerouslySetInnerHTML={{ __html: decodeURIComponent(priceRow.valueFree) }}/>
                    </TableCell>
                    <TableCell align="center">
                      <div dangerouslySetInnerHTML={{ __html: decodeURIComponent(priceRow.valueBusiness) }}/>
                    </TableCell>
                    <TableCell align="center">
                      <div dangerouslySetInnerHTML={{ __html: decodeURIComponent(priceRow.valuePrivate) }}/>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Collapse>
        </TableCell>
      </TableRow>}
    </>
  );
}