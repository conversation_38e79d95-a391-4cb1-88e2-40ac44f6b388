import React, { useEffect, useState, useRef } from "react";
// import { useNavigate,useParams,useLocation } from "react-router-dom";
import {
  Box
} from '@mui/material';
import TopMenuBar from "../../components/TopMenuBar";
import "./index.scss";
import { Outlet } from "react-router-dom";


export default function WorkOrder(){
  return <div className="home">
    <Box display="flex" flexDirection="column" style={{height:"100%"}}>
      <Box sx={{ width: "100%", padding: "0 100px" }}>
        <Box sx={{ height: 76 }} />
        <Box className="workirder-TopMenuBar-box">
          <TopMenuBar/>
        </Box>
      </Box>
      
      <Box flex="auto" style={{ width: '100%', padding: '0 100px', display: "flex", flexDirection: "column"}}>
        <Outlet/>
      </Box>

    </Box>
  </div>
}
