.createTeamDraModal {
    .ant-modal-content {
      min-height: calc(100vh - 400px);
      border-radius: 5px;
    }
  
    .ant-modal-header{
      border: none;
      border-radius: 5px;
      padding: 14px 24px 0px 10px;
      .ant-modal-title{
        .dragTitle {
          color: #666666 !important;
          font-weight: 600 !important;
        }
      }
    }
  
    .ant-modal-body {
      padding: 6px 24px 20px;
    }
  
    .ant-modal-footer{
      border: none;
      text-align: right !important;
    }
  
    .ant-form-item {
      margin-bottom: 8px;
    }
  
    .ant-col-24.ant-form-item-label{
      padding: 0 0 4px;
    }
}
  
.team-name-input.ant-input{
  border-radius: 5px;
}
  
.team-name-label {
  color: #999999;
}
  
.create-team-price-color {
  color: #F99B1D;
}
  
.create-team-discountPrice {
  font-size: 28px;
  font-weight: bold;
}

.create-team-originalPrice {
  text-decoration: line-through;
}
  
.create-team-date-item .ant-radio-button-wrapper{
  display: inline-flex;
  justify-content: center;
  margin-right: 2px;
  min-width: 52px;
  font-size: 12px;
  border: none;
  background-color: #E6F2FE;
  box-shadow: none;
}
  
// 产品选择
.pay-version {

}
  
.pay-version-item {
  // padding-top: 8px;
  overflow: hidden;
  box-sizing: border-box;
  background: #fff;
  border-color: #d9d9d9;
  border-style: solid;
  border-width: 1.02px 1px 1px 0;
  color: rgba(0,0,0,.85);
  cursor: pointer;
  display: inline-block;
  font-size: 14px;
  height: 32px;
  line-height: 20px;
  margin: 0;
  padding: 0 15px;
  position: relative;
  transition: color .3s,background .3s,border-color .3s,box-shadow .3s;
  border: 1px solid #d9d9d9;
  // margin-right: 10px;
  // margin-top: 10px;
  border-radius: 10px;
  //width: 150px;
  height: inherit;

  &-icon {
    color: #0077F2;
    padding-top: 5px;
    .iconfont{
      font-size: 16px;
      line-height: 20px;
    }
  }

  &-container {
    padding-top: 6px;
  }

  &-title {
    font-size: 14px;
    line-height: 18px;
    color: #333;
    font-weight: 500;
  }

  &-money {
    font-size: 12px;
    color: #F59B23;
    line-height: 12px;
  }

  &-oldmoney {
    font-size: 12px;
    color: #d9d9d9;
    text-decoration: line-through;
    line-height: 12px;
  }

  &-overdueTime {
    font-size: 12px;
    color: #999999;
    line-height: 20px;
  }
}

.pay-version-item-checked {
  box-sizing: border-box;
  border-color: #0077F2;
}

.pay-version-item-checked::before {
  position: absolute;
  bottom: 15px;
  right: -1px;
  font-family: "iconfont" !important;
  font-size: 50px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e719";
  color: #0077F2;
}
.pay-version-item-checked-disabled {
  box-sizing: border-box;
  border-color: #0077f2;
}
.pay-version-item-checked-disabled::before {
  position: absolute;
  bottom: 15px;
  right: -1px;
  font-family: "iconfont" !important;
  font-size: 50px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e719";
  color: #d9d9d9;
}

.pay-version-item.disabled {
  color: #999999;
  // background-color: #0000000a;
  .pay-version-item-icon{
    // color: #999999;
  }
}
.shifu-rate{
  font-size: 20px;
  font-weight: 600;
}

// 消息
.pay-badge .ant-badge-count{
  box-shadow: 0 0 0 0px #ff4d4f;
  margin-top: -3px !important;
  right: 20px !important;
}
.pay-badge .ant-badge-count-sm {
  font-size: 10px;
}

.pay-version-item.pay-version-item-checked.disabled {
  // border-color: #d9d9d9;
}
.pay-version-item.pay-version-item-checked.disabled::before {
  // color: #999999;
}

// 升级企业版、创建团队弹窗-form样式
.CreateTeam-form {
  .ant-form-item {
    margin-bottom: 10px;
  }
  // max-height: calc(100vh - 148px);
  // overflow-x: hidden;
  // overflow-y: auto;
}

.CreateTeam-form::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.CreateTeam-form::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
}
.CreateTeam-form::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  // background   : #ededed;
}

// 产品选择formitem
.product-selection-formItem {
  
}

// 费用formItem
.cost-formItem {
  .ant-form-item-row {
    align-items: center;
  }
}

// 
.price-bottom {
  padding: 0 40px 0 10px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  .price-bottom-left {
  }
  .price-bottom-right {
    .price-bottom-detailed {
      display: flex;
      justify-content: baseline;
      align-items: end;
      .price-bottom-detailed-descriptions {
        &-li {
          display: flex;
          align-items: center;
          font-size: 12px;
          .li-label {
            width: 50px;
            color: #666;
          }
          .li-value {
            display: flex;
            align-items: center;
            color: #333;
            .coupon{
              padding: 5px;
              border-radius: 5px;
              background-color: #0077F2;
              color: #fff;
              margin-left: 10px;
              display: flex;
              align-items: center;
              .delete{
                border-radius: 50%;
                width: 14px;
                height: 14px;
                margin-left: 5px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #fff;
                color: #0077F2;
              }
            }
          }
        }
      }
      .original-cost {
        margin-left: 20px;
        line-height: initial;
        color: #999;
        .original-cost-first {
          font-size: 12px;
        }
        .original-cost-last {
          font-size: 20px;
          text-decoration: line-through;
          font-weight: 600;
        }
      }
      .concessional-rate {
        margin-left: 20px;
        line-height: initial;
        color: #f59a23;
        .concessional-rate-first {
          font-size: 12px;
        }
        .concessional-rate-last {
          font-size: 24px;
          font-weight: 600;
        }
      }
    }
  }
}
// 优惠码
.discount-code {
  visibility: hidden;
  .ant-input-wrapper {
    height: 24px;
    .ant-input-affix-wrapper {
      display: flex;
      align-items: center;
      .ant-input {
        height: 100%;
        font-size: 12px;
        box-shadow: none;
      }
      .ant-input:hover {
        border-color: #d9d9d9;
        box-shadow: none;
      }
      .ant-input:focus {
        border-color: #d9d9d9;
        box-shadow: none;
      }
    }
    .ant-input-group-addon {
      .ant-btn {
        font-size: 12px;
        color: #0077F2;
        background-color: white;
        border-color: #d9d9d9;
        box-shadow: none;
      }
    }
  }
}

.discount-code-visible {
  visibility: visible !important;
  animation: discount-code-visible .3s linear 0s forwards;
  transition: width 0s ease 1s;
}

@keyframes discount-code-visible {
  0% {
    width: 0;
  }
    
  55% {
    width: 80px;
  }

  100% {
    width: 160px;
  }
}

// 立即购买
.purchase-btn {
  background-color: #f59a23;
  border-color: #f59a23;
}
.purchase-btn:hover {
  background-color: #f59a23 !important;
  border-color: #f59a23;
}
.purchase-btn:focus {
  background-color: #f59a23 !important;
  border-color: #f59a23;
}
.pay-tag{
  margin: 0px 0px 0px 8px !important;
  padding: 0 4px;
  border-radius: 5px;
  line-height: 14px;
}

.member-edit-default{
  .ant-input-number-group{
    .ant-input-number-group-addon{
      padding: 0px;
      background-color: #fff;
      border: 0px solid #fff;
      border-radius: 0px;
      .ant-btn{
        height: 30px;
        width: 30px;
        padding: 0px;
        border-radius: 0px;
      }
      .ant-btn:focus{
        border-color: #d9d9d9;
        color: #333;
      }
      .ant-btn:hover{
        border-color: #d9d9d9;
        color: #333;
      }
    }
    .ant-input-number{
      border-left: 0px solid #fff !important;
      border-right: 0px solid #fff !important;
      border-color: #d9d9d9;
      box-shadow: none;
      .ant-input-number-input{
        height: 28px;
        text-align: center;
        padding: 0px;
      }
    }
  }
}

.member-edit-primary{
  .ant-input-number-group{
    .ant-input-number-group-addon{
      padding: 0px;
      background-color: #fff;
      border: 0px solid #fff;
      border-radius: 0px;
      .ant-btn{
        height: 30px;
        width: 30px;
        padding: 0px;
        border-radius: 0px;
        border-color: #1890ff;
      }
      .ant-btn:focus{
        border-color: #1890ff;
      }
      .ant-btn:hover{
        border-color: #1890ff;
      }
    }
    .ant-input-number{
      border-left: 0px solid #fff !important;
      border-right: 0px solid #fff !important;
      border-color: #d9d9d9;
      box-shadow: none;
      .ant-input-number-input{
        height: 28px;
        text-align: center;
        padding: 0px;
      }
    }
  }
}

.CouponCard {
  margin: 0px 0px 5px 5px;
  width: 240px;
  height: 100px;
  border-radius: 6px;
  .ant-card-body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      padding: 0;
      height: 100%;
  }
  .CouponCard-capacity {
      font-size: 18px;
      font-weight: bold;
      color: #3279fe;
  }
  .CouponCard-validity {
      font-size: 12px;
      color: #666;
  }
  .CouponCard-pricebtn {
      margin-top: 10px;
      padding: 0 5px;
      min-width: 76px;
      border-radius: 4px;
      font-size: 12px;
      .CouponCard-discountPrice {
          color: #F59A23;
      }
      .CouponCard-price {
          margin-right: 10px;
          color: #999;
          text-decoration: line-through;
      }
  }
  .limited-time-offer {
      position: absolute;
      font-size: 12px;
      top: 0;
      left: 0;
      padding: 0 4px;
      color: white;
      border-top-left-radius: 6px;
      border-bottom-right-radius: 6px;
      background-color: #F59A23;
  }
}
.CouponCard-select {
  border: 2px solid #F59A23;
  border-radius: 6px;
  box-shadow: 0 5px 5px 1px #8cbcf74a;
}
.CouponCard-select:hover {
  border-radius: 6px;
  border-color:#F59A23 !important;
}
.CouponCard-select::before {
  position: absolute;
  bottom: -16px;
  right: -1px;
  border-radius: 6px;
  font-family: "iconfont" !important;
  font-size: 50px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e719";
  color: #F59A23;
}
.refresh-icon {
  position: relative;
  animation: refreshRotate 1s infinite forwards;
}
.refresh-position {
  position: absolute !important;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
@keyframes refreshRotate {
  0% {
  }
  10% {
      transform: rotate(36deg);
  }
  20% {
      transform: rotate(72deg);
  }
  30% {
      transform: rotate(108deg);
  }
  40% {
      transform: rotate(144deg);
  }
  50% {
      transform: rotate(180deg);
  }
  60% {
      transform: rotate(216deg);
  }
  70% {
      transform: rotate(252deg);
  }
  80% {
      transform: rotate(288deg);
  }
  90% {
      transform: rotate(324deg);
  }
  100% {
      transform: rotate(360deg);
  }
}