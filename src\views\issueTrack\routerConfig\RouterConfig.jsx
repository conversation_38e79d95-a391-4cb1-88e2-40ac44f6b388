import React, { lazy } from "react";
import { Skeleton } from "antd";
import { issueViewLoader } from "./loader";
import { globalUtil } from "../../../utils/globalUtil";

const routes = [
  {
    path: "",
    name: "issues",
    element: lazy(() => import("../views/IssueHome/IssueHome")),
    // skeleton: <Skeleton active />,
    loader: ({ request, params }) => {
      const url = new URL(request.url);
      const queryId = url.searchParams.get("queryId");
      const teamId =  process.env.REACT_APP_WORKORDER_TEAMID;
      const nodeId =  process.env.REACT_APP_WORKORDER_NODEID;
      // console.log("进入了主页", url)
      return issueViewLoader(
        globalUtil.getQueryClient(),
        teamId,
        nodeId,
        queryId
      );
    },
    children: [
      // 短列表
      {
        path: "",
        name: "issuebrowse",
        element: lazy(() => import("../views/Issuelist/Issuelist")), // 短列表
        children: [
          {
            path: "issueId/:nid",
            name: "issuedetail",
            // isNeedPageprv: true,
            // pageprvParams: { nodeId: "nid" },
            element: lazy(() => import("../views/IssueDetail/IssueDetailHome")),
          },
        ],
      },
    ],
  }
];

export default routes;
