import * as httpBase from "../utils/httpBase";
import api_name from "./api_name";

const {api_tms_team} = httpBase;

// setting-105 get_team_detail 获取团队基本信息
// https://confluence.ficent.com/pages/viewpage.action?pageId=78375988
export const setting_105_get_team_detail=(data) => httpBase.get(api_tms_team+api_name.setting_105_get_team_detail,data)
// team-701 get_product_list 获取团队版本套餐
// https://confluence.ficent.com/pages/viewpage.action?pageId=98410384
export const team_701_get_product_list = (data) => httpBase.get(api_tms_team+api_name.team_701_get_product_list,data)
// team-702 get_team_package_duration_rebate 根据团队人数计算购买时长的折扣比例
// https://confluence.ficent.com/pages/viewpage.action?pageId=98410510
export const team_702_get_team_package_duration_rebate = (data) => httpBase.get(api_tms_team+api_name.team_702_get_team_package_duration_rebate,data)
// team-703 calc_price 团队套餐价格计算
// https://confluence.ficent.com/pages/viewpage.action?pageId=98410387
export const team_703_calc_price = (data) => httpBase.post(api_tms_team+api_name.team_703_calc_price,data)
// team-706 get_free_team_count_by_user 获取团队规模最小值
// https://confluence.ficent.com/pages/viewpage.action?pageId=98410522
export const team_706_get_free_team_count_by_user = (data) => httpBase.get(api_tms_team+api_name.team_706_get_free_team_count_by_user,data)
// team-710 get_team_validity_package 获取当前团队购买的有效期内的套餐功能
// https://confluence.ficent.com/pages/viewpage.action?pageId=98412475
export const team_710_get_team_validity_package = (data) => httpBase.get(api_tms_team+api_name.team_710_get_team_validity_package,data)
// get_user_coupon_list 获取用户(已绑定)的优惠券列表
export const team_734_get_user_coupon_list = (data) => httpBase.get(api_tms_team+api_name.team_734_get_user_coupon_list,data)
// bind_user_coupon 绑定优惠券
export const team_735_bind_user_coupon = (data) => httpBase.post(api_tms_team+api_name.team_735_bind_user_coupon,data)
// unbind_user_coupon 解绑优惠券
export const team_736_unbind_user_coupon = (data) => httpBase.post(api_tms_team+api_name.team_736_unbind_user_coupon,data)