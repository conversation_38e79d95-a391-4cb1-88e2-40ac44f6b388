/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2023-05-10 14:00:37
 * @LastEditors: Walt <EMAIL>
 * @LastEditTime: 2023-05-10 14:00:37
 * @Description: 请填写文件内容描述！
 */
/* eslint-disable react-hooks/exhaustive-deps */
import { Layout, Tree } from 'antd';
import { useRef } from "react";
import { ResizableBox } from "react-resizable";
import {eTreeWidth} from "@/utils/enum"
import "./CustomSideBar.scss"

const { Sider } = Layout;

/**
 * @param {expandiWidth: 展开状态的宽度} param0
 * @param {collapsed: true:收缩 false:展开} param1
 * @returns 
 */
export default function CustomSideBar({ children, width, collapsed = false, onResize, onResizeStop }) {

  const ref = useRef(null)

  // 拖拽的句柄： <DraggableCore> not mounted on DragStart!
  // https://github.com/react-grid-layout/react-resizable/issues/200
  const handle = (resizeHandle, ref) => {
    return <div className="ia-splitter-handle" ref={ref}>
      <div className="ia-splitter-handle-highlight"></div>
    </div>
  };

  return (
    <ResizableBox
      ref={ref}
      width={width || eTreeWidth} //接口返回的width为null
      className="custom-box-right"
      height={Infinity}
      handle={handle}
      handleSize={[0, 2]} //解决拖拽宽度和拖拽排序冲突的问题
      resizeHandles={['e']}
      axis="x"
      minConstraints={[250, Infinity]}
      maxConstraints={[640, Infinity]}
      onResize={onResize}
      onResizeStop={onResizeStop}
      // zIndex: 1解决左侧更多弹窗被覆盖的问题
      style={{ zIndex: 1, flex: "auto" }} // 关键点
    >
      <Sider
        // 设置为 null 时隐藏 trigger
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        width={"100%"}
        theme="light"
        className='custom-box-sider'
       >
        {children}
      </Sider>
    </ResizableBox>
  )
}