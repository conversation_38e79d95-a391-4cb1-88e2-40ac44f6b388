import { createTheme } from '@mui/material/styles';
import qs from "qs";

/**
 * @palette mui按钮颜色配置
 * @main 主体颜色背景色
 * @contrastText 文字颜色
 * @dark 鼠标经过颜色
 * 注意：此处仅做mui Button的颜色配置，并不做variant(contained、outlined、text)属性的配置
 * 配置格式命名请参照：color_主体颜色_文字颜色_鼠标经过颜色
 */

export const theme = createTheme({
  palette: {
    color_0077f2_ffffff_0077f2: { // 按钮常用色调
      main: '#0077f2',
      contrastText: '#ffffff',
      dark: "#0077f2"
    },
    color_2ac465_ffffff_2ac465: { // 登录按钮色调
      main: '#2ac465',
      contrastText: '#ffffff',
      dark: "#2ac465"
    },
    color_ffffff_ffffff_ffffff: { // 纯白，contained模式下全白，推荐outlined和text模式下使用且当前模块背景不是白色
      main: '#ffffff',
      contrastText: '#ffffff',
      dark: "#ffffff"
    },
    color_ffffff_0077f2_ffffff: { 
      main: '#ffffff',
      contrastText: '#0077f2',
      dark: "#ffffff",
    },
    color_ffffff_333333_ffffff: { 
      main: '#ffffff',
      contrastText: '#333333',
      dark: "#ffffff",
    },
    color_f2f2f2_333333_f2f2f2: { // 帮助中心-标签
      main: '#f2f2f2',
      contrastText: '#333333',
      dark: "#f2f2f2"
    }
  },
});

// 解析mui style：variant、color
export const formatVariantColor = (value="") => {
  if(value) {
    let variantColor = qs.parse(value)
    return variantColor
  }
  return { variant: "", color: "" }
}