// iTeam简介-手风琴模板（默认手风琴居左排列）
.FunctionIntroductionAccordion {
    position: relative;
    padding: 50px 0;
    width: 100%;
    &-title {
        font-size: 34px;
        text-align: center;
    }
    &-content {
        padding: 0 20px;
        margin: auto;
        width: 1260px;
        &-tabs {
            margin-top: 10px;
            .MuiTabs-scroller {
                .MuiButtonBase-root {
                    font-size: 14px;
                    font-weight: bold;
                    color: #333;
                }
                .Mui-selected {
                    color: #1976d2;
                }
            }
        }
        &-tabsBody {
            // padding: 0 40px;
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            &-left {
                width: 590px;
                &-Collapse {
                    background-color: transparent;
                    .ant-collapse-item {
                        margin-bottom: 10px;
                        border: none;
                        border-radius: 4px !important;
                        box-shadow: 0px 0px 4px 1px rgba(0,0,0,.06);
                        .ant-collapse-header {
                            .ant-collapse-header-text {
                                font-weight: bold;
                            }
                        }
                    }
                }
            }
            &-right {
                width: 590px;
                &-image {
                    box-shadow: 0 4px 4px 0 rgb(77 77 77 / 1%), 0 2px 8px 0 rgb(77 77 77 / 7%);
                }
            }
        }
    }
}

// 手风琴居左排列
.FunctionIntroductionAccordion-left {
    .FunctionIntroductionAccordion-content {
        &-tabsBody {
            flex-direction: row !important;
        }
    }
}

// 手风琴居右排列
.FunctionIntroductionAccordion-right {
    .FunctionIntroductionAccordion-content {
        &-tabsBody {
            flex-direction: row-reverse !important;
        }
    }
}