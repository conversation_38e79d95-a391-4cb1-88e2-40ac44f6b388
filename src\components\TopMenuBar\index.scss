.TMS-AppBar {
    margin: auto;
    // width: 1220px !important;
}

.TMS-menu-grid {
    .MuiGrid-container {
        .MuiGrid-root:not(:first-child) {
            padding: 20px 30px;
        }
    }
}

// 操作系统按钮
.tms-operating-system {
    margin-right: 6px !important;
    padding: 0 !important;
    // color: #666 !important;
    .MuiButton-startIcon {
        margin-right: 2px;
    }
}

// 横向菜单
.MenuHorizontal-Popover {
    .MuiPaper-root {
        width: 100%;
        min-height: 300px;
    }
    .MenuHorizontal-Popover-content {
        display: flex;
        justify-content: space-between;
        padding: 40px 20px;
        margin: auto;
        width: 1260px;
    }
    .MenuHorizontal-Popover-stack {
        &-li {}
    }
    .Popover-right-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        &-btn {
            margin-top: 20px;
            text-transform: none;
        }
    }
    .Popover-links {
        display: flex;
        flex-direction: column;
        &-link {
            margin-top: 10px;
            font-size: 12px;
            color: #666
        }
    }
}

// 头像
.login-Popover-content {
    padding: 10px;
    min-width: 300px;
    &-top {
        border-bottom: 1px solid #f2f2f2;
        .MuiTypography-root {
            padding: 3px 0;
        }
        .MuiTypography-root:last-child {
            margin-bottom: 5px;
        }
    }
    &-bottom {
        .MuiTypography-root {
            padding: 3px 0;
            cursor: pointer;
        }
        .MuiTypography-root:hover {
            background-color: #f2f2f2;
        }
    }
}

// 即将推出
.menu-coming-F59A23 {
    margin-left: 4px;
    color: #f59a23;
}