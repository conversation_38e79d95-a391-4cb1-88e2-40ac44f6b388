// 解决问题
.solution {
    padding: 50px 0;
    text-align: center;
    .solution-stack {
        margin: auto;
        width: 1260px;
        .solution-stack-li {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 16px;
            width: 200px;
            height: 100px;
            background-color: #eff1f7;
            border-radius: 4px;
        }
        .solution-stack-li:nth-child(6n) {
            margin-left: 0;
        }
    }
}