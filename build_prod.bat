echo off
set filename=%date:~0,4%%date:~5,2%%date:~8,2%%time:~0,2%%time:~3,2%
set "filename=%filename: =0%"
set REACT_APP_VERSION=%filename%

@REM P环境

@REM build P code
echo "P build start" 
call npm run build:prod
echo "P build finish"

@REM delete portal.zip
echo "delete portal-prod-*.zip start"
del portal-prod-*.war
echo "delete finish"

@REM Compressed file
echo "tar start"
@REM tar cvf portal-prod-%filename%.zip -C ROOT-PROD .
jar -cvfM portal-prod-%filename%.war -C ROOT-PROD .
echo "tar finish"

echo ***
echo ***
echo ***
echo build Prod Code success!

pause