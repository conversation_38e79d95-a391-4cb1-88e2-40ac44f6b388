import React, { forwardRef, useEffect, useImperative<PERSON>and<PERSON>, useRef, useState } from "react";
import {Image} from "antd";
import $ from "jquery";
import { nanoid } from "nanoid";
import { globalUtil } from "@/utils/globalUtil";
import FroalaEditor from 'react-froala-wysiwyg';
import FroalaEditorView from 'react-froala-wysiwyg/FroalaEditorView';
import 'froala-editor/css/froala_editor.min.css';
import 'froala-editor/css/froala_style.min.css';

import 'froala-editor/js/plugins.pkgd.min.js';
import 'froala-editor/css/plugins.pkgd.min.css';
import "./plugin/zh_cn";

import "./TEditor.scss";

import {base64encode, copyToClip} from "@/utils/toolUtil"
import { eFileObjId } from "@/utils/enum";

// import {useSelector, shallowEqual} from "react-redux";
import {task_306_update_user_daily_task_finish_flag} from "@/api/http";

// import 'froala-editor/js/plugins/align.js';
// import 'froala-editor/js/plugins/char_counter.js';
// import 'froala-editor/js/plugins/code_beautifier';
// import 'froala-editor/js/plugins/code_view';
// import 'froala-editor/js/plugins/colors';
// import 'froala-editor/js/plugins/cryptojs';
// import 'froala-editor/js/plugins/draggable';
// import 'froala-editor/js/plugins/edit_in_popup';
// import 'froala-editor/js/plugins/emoticons';
// import 'froala-editor/js/plugins/entities';
// import 'froala-editor/js/plugins/file';
// import 'froala-editor/js/plugins/files_manager';
// import 'froala-editor/js/plugins/font_family';
// import 'froala-editor/js/plugins/font_size';
// import 'froala-editor/js/plugins/forms';
// import 'froala-editor/js/plugins/fullscreen';
// import 'froala-editor/js/plugins/help';
// import 'froala-editor/js/plugins/image_manager';
// import 'froala-editor/js/plugins/image';
// import 'froala-editor/js/plugins/inline_class';
// import 'froala-editor/js/plugins/inline_style';
// import 'froala-editor/js/plugins/line_breaker';
// import 'froala-editor/js/plugins/line_height';
// import 'froala-editor/js/plugins/link';
import 'froala-editor/js/plugins/lists.min.js';
// import 'froala-editor/js/plugins/markdown';
// import 'froala-editor/js/plugins/paragraph_format';
// import 'froala-editor/js/plugins/paragraph_style';
// import 'froala-editor/js/plugins/print';
// import 'froala-editor/js/plugins/quick_insert';
// import 'froala-editor/js/plugins/quote';
// import 'froala-editor/js/plugins/save';
// import 'froala-editor/js/plugins/special_characters';
// import 'froala-editor/js/plugins/table';
import 'froala-editor/js/plugins/track_changes.min.js';
// import 'froala-editor/js/plugins/trim_video';
// import 'froala-editor/js/plugins/url';
// import 'froala-editor/js/plugins/video';
// import 'froala-editor/js/plugins/word_paste';

import './plugin/codeSnippet';
// import './plugin/relink'; 
import './plugin/quickInsertVideo';
import './plugin/task';
import './plugin/task.css';
import './plugin/date';
import './plugin/more';
// import './plugin/resource';

import {lineNumbersBlock} from "./lineNumbersBlock";

// full toolbarButtons config
// toolbarButtons: {
//   'moreText': {
//     'buttons': ['bold', 'italic', 'underline', 'strikeThrough', 
//                 'subscript', 'superscript', 'fontFamily', 'fontSize', 
//                 'textColor', 'backgroundColor', 'inlineClass', 'inlineStyle', 
//                 'clearFormatting']
//   },
//   'moreParagraph': {
//     'buttons': ['alignLeft', 'alignCenter', 'formatOLSimple', 'alignRight', 
//                 'alignJustify', 'formatOL', 'formatUL', 'paragraphFormat', 
//                 'paragraphStyle', 'lineHeight', 'outdent', 'indent', 
//                 'quote']
//   },
//   'moreRich': {
//     'buttons': ['insertLink', 'insertImage', 'insertVideo', 'insertTable', 
//                 'emoticons', 'fontAwesome', 'specialCharacters', 'embedly', 
//                 'insertFile', 'insertHR']
//   },
//   'moreMisc': {
//     'buttons': ['undo', 'redo', 'fullscreen', 'print', 
//                 'getPDF', 'spellChecker', 'selectAll', 'html', 
//                 'help'],
//     'align': 'right',
//     'buttonsVisible': 2
//   }
// },

// full pluginsEnabled config
// pluginsEnabled: ["align","colors","draggable","embedly","emoticons","entities","fileManager","file","fontAwesome","fontFamily",
// "fontSize","fullscreen","Help","image","imageTUI","imageManager","inlineStyle","inlineClass","lineBreaker","lineHeight","link","lists",
// "paragraphFormat","paragraphStyle","print","quickInsert","quote","specialCharacters","spellChecker","trackChanges","table","url","video","wordPaste",
// "save","charCounter","codeBeautifier","codeView"],  

// full imageEditButtons config
// imageEditButtons: ['imageReplace', 'imageAlign', 'imageCaption', 'imageRemove', '|', 'imageLink', 'linkOpen', 'linkEdit', 'linkRemove', '-', 'imageDisplay', 'imageStyle', 'imageAlt', 'imageSize'],

// const task_regx = "(?<=(<ul(.+?)?data-list-style=\"task\"(.+?)>.*?))<li(.+?)?data-list-style=\"unchecked\"(.+?)?data-taskid=(.+?)?>(?=(.*?</ul>))"

/**
 * @param {String} content 文章内容
 * @param {function} onChange 修改文章内容
 * @returns 
 */
export function TEditorPreview({content="",isedit=false,uploadParams,onRefresh,...props}){
  // const reduxState = useSelector((state) => ({
  //   currentUserId: state.getIn(["login", "userInfo", "userId"]),
  // }), shallowEqual);

  // 当点击图片放大
  const [visible, setVisible] = useState(false);
  const [imgpaths, setImgpaths] = useState([]);
  const [current,setCurrent] = useState(0);
  const ref = useRef();
  const [previewContent,setPreviewContent] = useState(content);

  useEffect(()=>{
    // 图片预览
    $(ref.current).on("click",".fr-view img",(e)=>{
      if(e.target.nodeName.toLocaleLowerCase() === "img"){
        // 获取当前富文本所有的图片地址
        let imageList = []
        $(ref.current).find(".fr-view img").each((index,elem) => {
          imageList.push($(elem).attr("src"));
          if($(elem).is(e.target)){
            setCurrent(index)
          }
        })
        setImgpaths(imageList)
        setVisible(true)
      }
    })

    // 如果是当前用户，高亮mention
    // 获取当前user id
    // if(reduxState.currentUserId){
    //   $(ref.current).append(`<style>
    //     [mention-userid='${reduxState.currentUserId}']{
    //       background: #0052CC;
    //       color: #ffffff;
    //     }
    //     .hljs-ln{
    //       border-collapse:collapse;
    //     }
    //     .hljs-ln-n:before{
    //       content:attr(data-line-number);
    //       color: #ccc;
    //       margin-right: 5px;
    //     }
    //   </style>`)
    // }

    // 任务完成或取消
    isedit && $(ref.current).on("click","[data-list-style='task']>li",(e)=>{
      if(e.offsetX < 0){
        var listStyle = $(e.currentTarget).data("list-style"); 
        let taskId = $(e.currentTarget).data("taskid")
        let finishFlg = listStyle == "checked"?0:1;
        let request = {
          taskId,
          finishFlg,
          teamId: uploadParams?.teamId
        }
        $(e.target).attr("data-list-style",listStyle == "checked"?"unchecked":"checked");
        task_306_update_user_daily_task_finish_flag(request).then(result => {
          if(result.resultCode == 200){
            onRefresh?.()
          }
        })
      }
    })

    $(ref.current).on("click","pre>.copy-code-btn",(e)=>{
      let copyValue = $(e.target).parents("pre").find("code").text();
      copyToClip(copyValue, () => {
        globalUtil.success("复制成功");
      })
    })

    return ()=>{
      $(ref.current).off("click",".fr-editor-preview .fr-view img")
      isedit && $(ref.current).off("click","[data-list-style='task']>li");
      $(ref.current).off("click","pre>.copy-code-btn");
    }
  },[ref])

  useEffect(()=>{
    // 正则匹配 code 标签，添加
    // https://beta.reactjs.org/reference/react-dom/flushSync
    // flushSync()
    let contentElem = $(`<div>${content}<div>`);
    let codeElems = contentElem.find("code");
    $.each(codeElems,function(index,item){
      var codeElem = $(item)
      var className = codeElem.attr("class");
      var language = className?.split(" ").find(css=> css.match(/(^|\s)language-\S+/g));
      if(language){
        $(codeElem).attr("lang",language.split("-")[1])
      }
      $(codeElem).after("<span class='copy-code-btn'>复制代码</span>")
    })

    contentElem.find('code.hljs').each(function(i, block) {
      lineNumbersBlock(block,{singleLine: true});
    })

    let newContent = contentElem.prop("outerHTML");
    setPreviewContent(newContent);
  },[content])

  return <React.Fragment>
    <div className="fr-editor-preview" ref={ref}> 
    <FroalaEditorView
      config={{key: process.env.REACT_APP_FROALA_KEY}}
      model={previewContent} 
      {...props}/>
    <div style={{display:"none"}}>
      <Image.PreviewGroup 
        preview={{
          visible,
          onVisibleChange: (value) => setVisible(value),
          current: current
        }}>
          {imgpaths.map(imgpath => <Image width={200} src={imgpath}/>)}
      </Image.PreviewGroup>
    </div>
  </div>
  </React.Fragment>
}

/**
 * @description 编辑器
 */
const TEditor = ({toolbarContainer, placeholderText, defaultContent, contentChanged, blur, uploadCallback, autofocus=true, ...props}, ref) => {
  let tRef = useRef(null);
  const {current: toolbarId} = useRef(nanoid())

  useImperativeHandle(ref, () => ({
    // 获取编辑器对象
    getEditor: () => tRef && tRef.current,
    // 获取内容
    getContent: () => tRef && tRef.current.editor.html.get(),
    // 插入内容
    setContent: (content="") => tRef && tRef.current.editor.html.set(content),
    // 在光标之后插入内容
    insert: (content="") => tRef && tRef.current.editor.html.insert(content),
    // 编辑器聚焦
    focus: () => tRef && tRef.current.editor.events.focus(),
    // toolbar disable
    toolbarDisable: () => tRef && tRef.current.editor.toolbar.disable(),
    // toolbar enable
    toolbarEnable : () => tRef && tRef.current.editor.toolbar.enable(),
    // 获取内容转base 64
    getContentBase64: () => tRef && base64encode(tRef.current.editor.html.get())
  }));

  // useEffect(() => {
  //   toolbarId.current = nanoid()
  // },[])

  // fix 如果objId相同，附件上传会绑定到对应附件上
  // Object.assign(props.uploadParams,{objId: "1"});

  /**
   * first 更多插件
   * second 保存
   * third 撤销
   * fourth 下一步
   * 格式刷
   * 清除格式
   * 
   * 标题
   * 字体大小
   * 粗体
   * 斜体
   * 删除线
   * 下划线
   * 更多文本样式
   * 
   * 字体颜色
   * 背景色
   * 对齐方式
   * 无序列表
   * 有序列表
   * 任务列表
   * 缩进
   * 插入链接
   * 插入引用
   * 插入分割线
   * 查找替换
   * 大纲
   */
  return <div className="teditor">
    <div id={`tool${toolbarId}`} className="toolbar"></div>
    <FroalaEditor 
      ref={tRef}
      tag='textarea' 
      config={{
        teamId: props.uploadParams?.teamId || null, // 团队Id，Resource plugin 需要使用
        nodeId: props.uploadParams?.nodeId || null,
        key: process.env.REACT_APP_FROALA_KEY,
        heightMin: props.heightMin || "172px",
        toolbarContainer: toolbarContainer || `#tool${toolbarId}`,
        autofocus,
        // useClasses: false,
        placeholderText: placeholderText || '',
        // // pluginsEnabled: ["align","colors","draggable","embedly","emoticons","entities","file","fontAwesome","fontFamily",
        // //                 "fontSize","fullscreen","image","imageTUI","imageManager","inlineStyle","inlineClass","lineBreaker","lineHeight","link","lists",
        // //                 "paragraphFormat","paragraphStyle","quickInsert","quote","table","url","video","wordPaste","help"],  
        linkAlwaysBlank: true,
        // listAdvancedTypes: false,
        paragraphFormat: {
          N: '正文',
          H1: '标题 1',
          H2: '标题 2',
          H3: '标题 3',
          H4: '标题 4'
        },
        // toolbarButtons: {
        //   'moreText': {
        //     'buttons': ['bold', 'italic', 'underline', 'strikeThrough', 
        //                 'subscript', 'superscript', 'fontFamily', 'fontSize', 
        //                 'textColor', 'backgroundColor','clearFormatting']
        //   },
        //   'moreParagraph': {
        //     'buttons': ['align', 'formatOL', 'formatUL', 'paragraphFormat', 
        //                 'paragraphStyle', 'lineHeight', 'outdent', 'indent', 
        //                 'quote']
        //   },
        //   'moreRich': {
        //     'buttons': ['code','insertLink', 'insertImage', 'insertVideo', 'insertTable', 
        //                 'emoticons', 'fontAwesome', 'specialCharacters', 'embedly', 
        //                 'insertFile', 'insertHR']
        //   },
        //   'moreMisc': {
        //     'buttons': ['fullscreen', 'help'],
        //     'align': 'right',
        //     'buttonsVisible': 2
        //   }
        // },
        // fontSizeDefaultSelection:"12",
        // attribution: false,
        // quickInsertEnabled: false, // 快速插入
        // charCounterCount: false,

        // "documentReady": true,
        fontFamilySelection: true,
        fontSizeSelection: true,
        "lineBreakerOffset": 10,
        "lineBreakerHorizontalOffset": 5,
        "wordPasteModal": false,
        "wordPasteKeepFormatting": true,
        "colorsButtons": ["colorsBack"],
        "colorsHEXInput": false,
        "codeMirror": false,
        "listAdvancedTypes": true,
        "tableDefaultWidth": "60%",
        // "imageOutputSize": true,
        "toolbarButtons": [
          [ "undo","redo" , "lineHeight", "code"], // "fullscreen",
          [ "trackChanges", "bold", "italic", "underline", "strikeThrough", "textColor", "backgroundColor", "clearFormatting" ],
          [ "align" ],
          [ "formatOL", "formatUL", "taskUL","indent", "outdent" ],
          [ "paragraphFormat" ],
          [ "fontFamily" ],
          [ "fontSize" ],
          [ "insertLink", "insertImage", "insertTable", "quote" ], // "pickaday"
          [ "insertMore" ]
        ],
        "quickInsertButtons": ["image", "quickInsertVideo", "embedly", "table", "ul", "ol", "hr"],
        "quickInsertEnabled": true,
        "quickInsertTags": ["p", "div", "h1", "h2", "h3", "h4", "h5", "h6", "pre", "blockquote"],
        "toolbarInsertMoreButtons":["pickaday"], // "taskUL",
        "tableColors": [
          "#61BD6D", "#1ABC9C", "#54ACD2", "#2C82C9", "#9365B8", "#475577", "#CCCCCC", "REMOVE"
        ],
        "tableEditButtons": [
          "tableHeader", "tableRemove", "|",
          "tableRows", "tableColumns", "tableStyle", 
          "-", 
          "tableCells", "tableCellBackground", "tableCellVerticalAlign", 
          "tableCellHorizontalAlign", "tableCellStyle"
        ],
        "tableInsertHelper": false,
        "tableInsertMaxSize": 20,
        "tableStyles": {
          "fr-dashed-borders": "点划线",
          "fr-alternate-rows": "隔行颜色"
        },
        "tableCellStyles": {
          "fr-highlighted": "高亮",
          "fr-thick": "加粗"
        },
        language: "zh_cn",
        requestHeaders: {
        },
        //上传图片
        imageInsertButtons: ['imageBack', '|', 'imageUpload', 'imageByURL'/* , 'imageManager' */],
        imageDefaultAlign: 'left',
        imageDefaultDisplay: 'block',
        imageAddNewLine: true,
        imageUploadRemoteUrls: true,
        imageResize: true,
        imageEditButtons: [ 'imageAlign', 'imageCaption', 'imageRemove', '|', 'imageLink', 'linkOpen', 'linkEdit', 'linkRemove', '-', 'imageDisplay', 'imageStyle', 'imageAlt', 'imageSize'],
        imageUploadParam: 'file',
        imageUploadParams: {
          ...(props.uploadParams || {}),
          nodeId: eFileObjId
        },
        imageUploadURL: `${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`,
        imageUploadMethod: 'POST',
        imageMaxSize: 50 * 1024 * 1024,
        imageAllowedTypes: ['jpeg', 'jpg', 'png', 'gif', 'tiff', 'bmp', 'jpeg;base64', 
                              'jpg;base64', 'png;base64', 'gif;base64', 'tiff;base64', 'bmp;base64'],
        videoDefaultAlign: 'left',
        videoDefaultDisplay: 'block',
        videoUploadParam:"file",
        videoUploadParams: {
          ...(props.uploadParams || {}),
          nodeId: eFileObjId
        },
        videoUploadURL: `${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`,
        videoUploadMethod: 'POST',
        videoMaxSize: 1024 * 1024 * 200,
        videoAllowedTypes: ['mp4', 'webm', 'ogg'],
        fileUploadParam: 'file',
        fileUploadURL: `${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`,
        fileUploadParams: {
          ...(props.uploadParams || {}),
          nodeId: eFileObjId
        },
        fileUploadMethod: 'POST',
        fileMaxSize: 200 * 1024 * 1024,
        fileAllowedTypes: ['*'],
        events: {
          initialized: function () {
            var editor = this;
            defaultContent && editor.html.set(defaultContent);
          },
          contentChanged: contentChanged,
          blur: blur,
          'image.uploaded': function(error){

          },
          'video.uploaded': function (response) {
            // File was uploaded to the server.
            // uploadCallback && uploadCallback(JSON.parse(response))
          },
          'file.uploaded': function (response) {
            // File was uploaded to the server.
            // uploadCallback && uploadCallback(JSON.parse(response))
          },
          'paste.afterCleanup': function (clipboard_html) {
            let new_html = $(`<div>${clipboard_html}</div>`);
            new_html.find("table").each((index,elem)=>{
              $(elem).removeAttr("border");
              $(elem).removeAttr("width");
            })
            // 清空 table border
            return $(new_html).html()
          }
        }
      }} />
  </div>
}

export default forwardRef(TEditor);