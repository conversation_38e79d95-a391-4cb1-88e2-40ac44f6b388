import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import * as http from "../../../api/http";

import { eEnableFlg, eConsolePropId, eConsoleUiControl, eCountType } from "../../../utils/enum";
import { eNodeTypeId } from "../../../utils/TsbConfig";
import { useNavigate } from "react-router-dom";
import { globalUtil } from "../../../utils/globalUtil";
import { issue106, issue107 } from "@/api/query/query_key";
import api_name from "@/api/api_name";
//https://tanstack.com/query/v4/docs/guides/infinite-queries


//issue011_edit_issue 编辑issue
export function useMutationIssue011OpNode() {
    const queryClient = useQueryClient();
    const mutation = useMutation(
        (params) => {
            return http.track_007_edit_issue(params)
                .then((res) => {
                    if (res.resultCode === 200) {
                        return res;
                    }
                    return res;
                });
        },
        {
            onSuccess: (data, vars) => {
                queryClient.invalidateQueries([issue106]); //刷新issue详情
                queryClient.invalidateQueries([issue107]); //刷新issue列表
            }
        }
    );
    return {
        onIssue011OperateNode: mutation.mutate,
        isMutating: mutation.isLoading,
        isMutateError: mutation.isError,
        mutateError: mutation.error,
        status: mutation.status
    };
}

// issue012 新建issue
export function useMutationIssue012CreateIssue(teamId, issueListNodeId, pageNo) {
    console.log(teamId, issueListNodeId, pageNo)
    const key_kanban = ['issueViewList']
    const queryClient = useQueryClient();
    const mutation = useMutation(
        (params) => {

            return http.track_006_create_issue(params)
                .then((res) => {
                    // if (res.resultCode === 200) {
                    //     globalUtil.success("提交成功");
                    //     return res;
                    // }
                    return res;
                });
        },
        {
            onSuccess: (data, vars) => {
                //需要能够区分是，增删改，从而对本地缓存作各自的处理
                queryClient.invalidateQueries(issue107)
                queryClient.invalidateQueries(key_kanban)
            },
            onError: (err, params) => {
                //onError(err,params,queryClient);
            }
        }
    );
    return {
        onIssue012CreateIssue: mutation.mutate,
        isMutating: mutation.isLoading,
        isMutateError: mutation.isError,
        mutateError: mutation.error,
        status: mutation.status
    };
}
//issue016_get_issue_info 获取issue详情
export function useQueryIssue016IssueInfo(teamId, issuegrpNodeId, nodeId, enabled) {
    const query = useQuery(
        [issue106, teamId, issuegrpNodeId, nodeId?.toString()],
        () => {
            return http.track_011_get_issue_info({ teamId: teamId, issuegrpNodeId: issuegrpNodeId, nodeId: nodeId }) //返回 result.apiNo
                .then((res) => {
                    if (res.resultCode === 200) {
                        return {
                            issueInfo: res.issueInfo,
                            issueHistoryList: res.issueHistoryList,
                            issueObjId: res.issueInfo.id,
                            issueFormColumn: res.formColumn,
                        };
                    }
                })
                .catch((e) => {
                    console.log("issue_016_get_issue_info" + e);
                })
        },{
          enabled
        }
    );
    return {
        issueInfo: query.data?.issueInfo,
        issueHistoryList: query.data?.issueHistoryList,
        issueObjId: query.data?.issueObjId,
        _issueFormColumn: query.data?.issueFormColumn,
        ...query
    };
}

//issue017 获取issue列表
export function useQueryIssue017_getIssueList(teamId, issueListNodeId, pageNo, pageSize, criteriaList, keywords, orderBy, ascendingFlg, countType, enabledFlg) {
    const query = useQuery(
        [issue107, teamId, issueListNodeId, pageNo, keywords, orderBy, ascendingFlg, criteriaList, countType],
        () => {
            console.log(criteriaList)
            let _criteriaListBackend;
            if(!countType){ // 右下角有过滤条件时，顶部过滤条件不生效
              _criteriaListBackend = criteriaList?.length > 0 ?
              // 先过滤begin为空的数据
              criteriaList.filter(el => el.begin != "").map(_criteria => ({
                  ..._criteria,
                  // 如果是日期 则为字符串，不需要把数组变成逗号
                  begin: _criteria.end ? _criteria.begin : _criteria.begin.join(","), //数组变成以逗号隔开
              })
              )
              : undefined;
            }
            countType = countType != eCountType.eTotal ? countType : undefined; // (调用getIssueList接口,不传条件，默认查询全部)
            //页码从1开始计数
            var params = { issuegrpNodeId: issueListNodeId, teamId: teamId, query: _criteriaListBackend, pageNum: pageNo, pageSize: pageSize, keywords: keywords, order: orderBy, desc: ascendingFlg ? "asc" : "desc", countType: countType }
            console.log("****调用Issue017****", _criteriaListBackend, enabledFlg);
            return http.track_008_get_issue_list(params).then((res) => {
                return {
                    tableColumn: res?.tableColumn??[],
                    issueList: res?.tableData?.rows??[],
                    totalCnt: res?.tableData?.total??0
                }
            });
        }, {
        enabled: enabledFlg
    }
    );
    return {
        tableColumn: query.data?.tableColumn,
        issueList: query.data?.issueList,
        totalCnt: query.data?.totalCnt,
        ...query
    };
}

/* //获取搜索详情信息
//https://confluence.ficent.com/pages/viewpage.action?pageId=78369599
//从后端(team-107)取回来的criteria，需要改为 col+begin/end的格式，进而用于通过issue保存查询条件(issue-510)上传
//https://confluence.ficent.com/pages/viewpage.action?pageId=98404466
export function useQueryTeam107_getQueryDetail(teamId, queryId, enabledFlg) {
    return useQuery(
        ['Team107', teamId, queryId],
        () => {
            return http.team_107_get_query_detail({ queryId: queryId, teamId: teamId, }).then((res) => {
                if (res.resultCode === 200) {
                    let _criteriaList = [];
                    res.criterias.forEach(_criteria => {
                        // 判断_criteriaList是否已有相同attrNid的数据
                        let findItemIndex = _criteriaList.findIndex(el => el.col == _criteria.attrNid)
                        // 如果有，则不用再次push，直接用当前index的数据进行处理
                        if (findItemIndex >= 0) {
                            if (_criteria.attrValueParse && _criteria.relOpType == 6) {
                                // 如果有区间relOpType == 6，》=则用end属性存储，不考虑存在多个条件
                                _criteriaList[findItemIndex].end = _criteria.attrValueParse
                            } else if (_criteria.attrValueParse && _criteria.relOpType == 4) {
                                // 如果有区间relOpType == 4，《= 则用begin属性存储，不考虑存在多个条件
                                _criteriaList[findItemIndex].begin = _criteria.attrValueParse
                            } else {
                                // 考虑有多条件，使用toString()确保被转换的数据为字符串
                                _criteriaList[findItemIndex].begin = _criteria.attrValueParse?.toString().split(",") //将用逗号隔开的字符串，转为数组
                            }
                        } else {
                            let criteriaUi = { col: _criteria.attrNid };
                            if (_criteria.attrValueParse && _criteria.relOpType == 6) {
                                criteriaUi.end = _criteria.attrValueParse
                            } else if (_criteria.attrValueParse && _criteria.relOpType == 4) {
                                criteriaUi.begin = _criteria.attrValueParse
                            } else {
                                criteriaUi.begin = _criteria.attrValueParse?.toString().split(",")
                            }
                            _criteriaList.push(criteriaUi)
                        }
                    });
                    return _criteriaList;
                }
            });
        }, {
        enabled: enabledFlg
    }
    )
}
 */

export function useQuerySetting409_getTeamAttrgrpProps(teamId, nodeId, subclassNid, enabledFlg) {
    const query = useQuery(
        ['issueTeamAttrProps', teamId, nodeId, subclassNid],
        () => {
            return http.setting_409_get_team_attrgrp_props({ teamId: teamId, nodeId: nodeId, subclassId: subclassNid })
                .then((res) => {
                    if (res.resultCode === 200) {
                        let _attrList = res.nodeList.map(_attr => {
                            let uiControlProp = _attr.propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control) // 匹配14 UI显示
                            if (uiControlProp?.propValue == eConsoleUiControl.ListBox) {
                                let selectionId = _attr.propertyList.find(el => el.propType == eConsolePropId.Prop_12_selection).propValue;  // 匹配12 匹配407接口列表id
                                return { ..._attr, name: _attr.nodeName, nodeId: _attr.nodeId, visible: true, type: eConsoleUiControl.ListBox, property_listId: selectionId }
                            }
                            if (uiControlProp?.propValue == eConsoleUiControl.Date) {
                                return { ..._attr, name: _attr.nodeName, nodeId: _attr.nodeId, visible: true, type: eConsoleUiControl.Date, }
                            }
                            return _attr;
                        });
                        return { subclassAttrList: _attrList, exprsList: res.exprs }
                    }
                })
        }, {
        enabled: enabledFlg
    });
    return {
        exprsList: query.data?.exprsList,
        subclassAttrList: query.data?.subclassAttrList,
        ...query
    };
}



export function useMutationTeam545_SaveQueryFieldList() {
    return useMutation(
        ({ teamId, projectId, selectedAttrNidList }) => {
            let params = { teamId: teamId, objType: eNodeTypeId.nt_317_objtype_issue_project, tableType: 1, projectId: projectId, fields: selectedAttrNidList, }

            return http.team_545_select_table_fields(params);
        }
    )
}

// 保存过滤条件
// export function useMutationIssue510_saveIssueQuery() {
//     return useMutation(
//         ({ teamId, nodeId, queryName, querySite, _criteriaListBackend }) => {
//             return http.issue_510_save_issue_query({
//                 issuegrpNodeId: nodeId,
//                 teamId: teamId,
//                 name: queryName,
//                 site: querySite, //保存位置
//                 query: _criteriaListBackend || null //转换后的过滤条件
//             });
//         }
//     )
// }

// 页码定位
export function useQueryIssue511_getPageById(teamId, issuegrpNodeId, nodeId, criteriaList, pageSize, keywords, orderBy, ascendingFlg, enabledFlg) {
    const query = useQuery(['Issue511', teamId, issuegrpNodeId, criteriaList, keywords],
        () => {
            console.log(criteriaList)
            let _criteriaListBackend = criteriaList?.length > 0 ?
                // 先过滤begin为空的数据
                criteriaList.filter(el => el.begin != "").map(_criteria => ({
                    ..._criteria,
                    // 如果是日期 则为字符串，不需要把数组变成逗号
                    begin: _criteria.end ? _criteria.begin : _criteria.begin.join(","), //数组变成以逗号隔开
                })
                )
                : null;

            var params = { teamId, issuegrpNodeId, nodeId, query: _criteriaListBackend, pageSize, keywords, order: orderBy, desc: ascendingFlg ? "asc" : "desc" }
            console.log("issue511页码", params);

            return http.track_010_get_page_with_id(params)
                .then((res) => {
                    if (res.resultCode === 200) {
                        return res.pageNum;
                    }
                })
                .catch((e) => {
                    console.log("issue_511_get_page_with_id" + e);
                })
        }, {
        enabled: enabledFlg,
    }
    );
    return {
        queryPage: query.data,
        ...query
    };
}


// 获取某个节点详情
export function useQueryGetObjNodeInfo(teamId, objNodeId) {
    const query = useQuery(['issueNodeInfo', teamId, objNodeId],
        () =>

            http.team_530_get_obj_node_info({ teamId, objNodeId })
                .then((res) => {
                    if (res.resultCode === 200) {

                        res.nodeId = res.parentId;
                        //如果有objId则调用107接口获取参数
                        let objId;
                        if (res.objId) {
                            objId = res.objId;
                        }
                        return { objInfo: res, queryObjId: objId };
                    }
                })
                .catch((e) => {
                    console.log("team_530_get_obj_node_info" + e);
                })
    );
    return {
        objInfo: query.data?.objInfo,
        queryObjId: query.data?.queryObjId,
        ...query
    };
}

//保存页面查询条件
export function useMutationTeam552saveQuery() {
    const mutation = useMutation(
        (params) => {
            return http.team_552_save_front_query(params)
                .then((res) => {
                    if (res.resultCode === 200) {
                        return res.id;
                    }
                })
                .catch((e) => {
                    console.log("team_552_save_front_query" + e);
                    return false;
                })
        }
    );
    return {
        onTeam552saveQuery: mutation.mutate,
        isMutating: mutation.isLoading,
        isMutateError: mutation.isError,
        mutateError: mutation.error,
        status: mutation.status
    };
}

// 获取query查询条件
export function useQueryGetFrontQuery(teamId, queryId, enabledFlg) {
    const query = useQuery(['query', teamId, queryId],
        () => {
            var params = { teamId, queryId }
            return http.team_553_get_front_query(params)
                .then((res) => {
                    if (res.resultCode === 200) {
                        return res;
                    }
                })
                .catch((e) => {
                    console.log("team_553_get_front_query" + e);
                })
        }, {
        enabled: enabledFlg,
    }
    );
    return {
        issueQuery: query.data,
        ...query
    };
}

/* // 收藏某一个资源
export function useMutationTeam113FavoriteObj() {
    const queryClient = useQueryClient();
    const mutation = useMutation(
        (params) => {
            return http.team113_favorite_obj(params)
                .then((res) => {
                    if (res.resultCode === 200) {
                        globalUtil.success(res.resultMessage);
                        // this.refearchList()
                        return res;
                    }
                })
        }, {
        onSuccess: (data, vars) => {
            queryClient.invalidateQueries(issue107)
            // queryClient.invalidateQueries(key_status)
        },
    }
    );
    return {
        favoriteObjMutation: mutation.mutate,
        isMutating: mutation.isLoading,
        isMutateError: mutation.isError,
        mutateError: mutation.error,
        status: mutation.status
    };
}

// 取消收藏某一个资源
export function useMutationTeam114CancelFavoriteObj() {
    const queryClient = useQueryClient();
    const mutation = useMutation(
        (params) => {
            return http.team_114_cancel_favorite_obj(params)
                .then((res) => {
                    if (res.resultCode === 200) {
                        globalUtil.success(res.resultMessage);
                        // refearchList()
                        return res;
                    }
                });
        }, {
        onSuccess: (data, vars) => {
            queryClient.invalidateQueries(issue107)
        },
    }
    );
    return {
        cancelFavoriteObjMutation: mutation.mutate,
        isMutating: mutation.isLoading,
        isMutateError: mutation.isError,
        mutateError: mutation.error,
        status: mutation.status
    };
} */

// 获取社交状态
export function useQueryGetFavoriteStatus(teamId, objNodeId, enabled) {
    const query = useQuery(['favoriteStatus', teamId, objNodeId],
        () => {
            var params = { teamId, objNodeId }
            return http.team_120_get_obj_favorite_status(params)
                .then((res) => {
                  return res || {};
                })
                .catch((e) => {
                    console.log("team120_get_obj_favorite_status" + e);
                })
        }, {
        enabled: enabled,
    }
    );
    return {
        favoriteStatus: query.data,
        ...query
    };
}

// 获取项目详细信息
// export function useQueryGetGroupInfo(teamId, issueGrpId, enabledFlg) {
//     const query = useQuery(['issueProInfo', teamId, issueGrpId],
//         () => {
//             return http.issue_504_get_issuegrp_info({ teamId, issueGrpId })
//                 .then((res) => {
//                     if (res.resultCode === 200) {

//                         return res;
//                     }
//                 })
//                 .catch((e) => {
//                     console.log("issue_504_get_issuegrp_info" + e);
//                 })
//         }, {
//         enabled: enabledFlg,
//     }
//     );
//     return {
//         projectInfo: query.data,
//         ...query
//     };
// }

//team-503 get_attachment_list 获取附件列表
export function useQueryTeam503GetAttachmentList(teamId, nodeId, objType, enabledFlg) {
    const query = useQuery(
        [api_name.team_503_get_attachment_list, teamId, nodeId],
        () => {
            return http.team_503_get_attachment_list({ teamId, nodeId, objType })
                .then((res) => {
                    if (res.resultCode === 200) {
                        return res.attachmentList;
                    }
                })
                .catch((e) => {
                    console.log("team_503_get_attachment_list" + e);
                })
        }, {
        enabled: enabledFlg,
    }
    );
    return {
        attachmentList: query.data || [],
        ...query
    };
}

//删除附件
export function useMutationTeam512delAttachment() {
    const mutation = useMutation(
        (params) => {
            return http.team_512_del_attachment(params)
                .then((res) => {
                    if (res.resultCode === 200) {
                        globalUtil.success("删除成功!");
                        return res;
                    }
                });
        }
    );
    return {
        delAttachmentMutation: mutation.mutate,
        isMutating: mutation.isLoading,
        isMutateError: mutation.isError,
        mutateError: mutation.error,
        status: mutation.status
    };
}

/* //team_505_get_obj_list 获取资源引用列表
export function useQueryTeam505GetRelObjList(teamId, objId, objType, enabledFlg) {
    const query = useQuery(
        ["issueRelObjList", teamId, objId],
        () => {
            return http.team_505_get_obj_list({ teamId, objId, objType })
                .then((res) => {
                    if (res.resultCode === 200) {
                        return res.objrelList;
                    }
                })
                .catch((e) => {
                    console.log("team_505_get_obj_list" + e);
                })
        }, {
        enabled: enabledFlg,
    }
    );
    return {
        relObjList: query.data,
        ...query
    };
} 

// 删除资源引用
export function useMutationTeam511delObjRel() {
    const mutation = useMutation(
        (params) => {
            return http.team_511_del_obj_rel(params)
                .then((res) => {
                    if (res.resultCode === 200) {
                        return res;
                    }
                });
        }
    );
    return {
        delObjRelMutation: mutation.mutate,
        isMutating: mutation.isLoading,
        isMutateError: mutation.isError,
        mutateError: mutation.error,
        status: mutation.status
    };
}*/

//team_544_get_table_field_list 获取显示字段
export function useQueryTeam544GetTableFieldList(teamId, objType, projectId) {
    const query = useQuery(
        ["tableFieldList", teamId, objType, projectId],
        () => {
            return http.team_544_get_table_field_list({ teamId, objType, tableType: 1, projectId })
                .then((res) => {
                    if (res.resultCode === 200) {
                        return { fieldList: res.fieldList, configFlg: res.configFlg };
                    }
                })
                .catch((e) => {
                    console.log("team_544_get_table_field_list" + e);
                })
        }
    );
    return {
        fieldList: query.data?.fieldList,
        configFlg: query.data?.configFlg,
        ...query
    };
}



// 保存attr节点prop值
export function useMutationSetting404UpdateAttrPropValue() {
    const key = ["issueTeamAttrProps"]
    const navigate = useNavigate();
    const queryClient = useQueryClient();
    const mutation = useMutation(
        (params) => {
            return http.setting_404_update_attr_prop_value(params)
                .then((res) => {
                    if (res.resultCode === 200) {
                        // message.success(res.resultMessage);
                        return res;
                    }
                });
        }, {
        onSuccess: (data, vars) => {
            queryClient.invalidateQueries(key)
        },
    }
    );
    return {
        updateAttrPropValueMutation: mutation.mutate,
        isMutating: mutation.isLoading,
        isMutateError: mutation.isError,
        mutateError: mutation.error,
        status: mutation.status
    };
}

// 变更attr显示排序(上移/下移)
export function useMutationSetting402UpdateAttrSeqNo() {
    const key = ["issueTeamAttrProps"]
    const navigate = useNavigate();
    const queryClient = useQueryClient();
    const mutation = useMutation(
        (params) => {
            return http.setting_402_update_attr_seq_no(params)
                .then((res) => {
                    if (res.resultCode === 200) {
                        // message.success(res.resultMessage);
                        return res;
                    }
                });
        }, {
        onSuccess: (data, vars) => {
            // queryClient.invalidateQueries(key)
        },
    }
    );
    return {
        updateAttrSeqNoMutation: mutation.mutate,
        isMutating: mutation.isLoading,
        isMutateError: mutation.isError,
        mutateError: mutation.error,
        status: mutation.status
    };
}


//获取issue看板信息
// export function useQueryIssue500getIssueView(teamId, issuegrpNodeId, sortTypeValueBegin, sortTypeValueEnd, criteriaList, keywords, pageNo, pageSize) {
//     const query = useQuery(
//         ["issueViewList", teamId, issuegrpNodeId, criteriaList, keywords, pageNo],
//         () => {
//             let _criteriaListBackend = criteriaList?.length > 0 ?
//                 // 先过滤begin为空的数据
//                 criteriaList.filter(el => el.begin != "").map(_criteria => ({
//                     ..._criteria,
//                     // 如果是日期 则为字符串，不需要把数组变成逗号
//                     begin: _criteria.end ? _criteria.begin : _criteria.begin.join(","), //数组变成以逗号隔开
//                 })
//                 )
//                 : null;
//             let params = { teamId, issuegrpNodeId, sortTypeValueBegin, sortTypeValueEnd, query: _criteriaListBackend, keywords, pageNo, pageSize }
//             return http.track_012_get_issue_view(params)
//                 .then((res) => {
//                     if (res.resultCode === 200) {
//                         let maxCount = Math.max(...res.groupList.map(group => group.issueTotalNum))
//                         let total = 0
//                         res.groupList?.filter(el => el.type != "null").map(group => {
//                             total = total + group.issueTotalNum
//                         })
//                         let findIssueType = res.tableColumn?.find(el => el.attrNid == eConsoleNodeId.Nid_11109_Issue_Type)
//                         return { kanbanTotal: total, kanbanMaxCount: maxCount, kanbanGroupList: res.groupList, iconSelectionLid: findIssueType?.selectionLid };
//                     }
//                 })
//                 .catch((e) => {
//                     console.log("issue_500_get_issue_view" + e);
//                 })
//         }
//     );
//     return {
//         kanbanTotal: query.data?.kanbanTotal,
//         kanbanMaxCount: query.data?.kanbanMaxCount,
//         kanbanGroupList: query.data?.kanbanGroupList,
//         iconSelectionLid: query.data?.iconSelectionLid,
//         ...query
//     };
// }

// 获取阶段看板信息
// export function useQueryIssue501getPhaseView(teamId, issuegrpNodeId, sortTypeValueBegin, sortTypeValueEnd, criteriaList, keywords, pageNo, pageSize) {
//     const query = useQuery(
//         ["phaseViewList", teamId, issuegrpNodeId, criteriaList, keywords, pageNo],
//         () => {
//             let _criteriaListBackend = criteriaList?.length > 0 ?
//                 // 先过滤begin为空的数据
//                 criteriaList.filter(el => el.begin != "").map(_criteria => ({
//                     ..._criteria,
//                     // 如果是日期 则为字符串，不需要把数组变成逗号
//                     begin: _criteria.end ? _criteria.begin : _criteria.begin.join(","), //数组变成以逗号隔开
//                 })
//                 )
//                 : null;
//             let params = { teamId, issuegrpNodeId, sortTypeValueBegin, sortTypeValueEnd, query: _criteriaListBackend, keywords, pageNo, pageSize }
//             return http.track_015_get_phase_view(params)
//                 .then((res) => {
//                     if (res.resultCode === 200) {
//                         let count = Math.max(...res.groupList.map(group => group.issueTotalNum))
//                         return { kanbanTotal: count, kanbanGroupList: res.groupList };
//                     }
//                 })
//                 .catch((e) => {
//                     console.log("track_015_get_phase_view" + e);
//                 })
//         }
//     );
//     return {
//         kanbanTotal: query.data?.kanbanTotal,
//         kanbanGroupList: query.data?.kanbanGroupList,
//         ...query
//     };
// }

// 获取项目自定义表单字段
export function useQueryIssue506_getSubclass(teamId, issueListNodeId, enabledFlg) {
    const query = useQuery(['Issue506', teamId, issueListNodeId],
        () => {
            return http.track_005_get_subclass_by_issue_node({ teamId, nodeId: issueListNodeId, })
                .then((res) => {
                    if (res.resultCode === 200) {
                        return res;
                    }
                });
        }, {
        enabled: enabledFlg,
    }
    );
    return {
        projectId: query.data?.projectId,
        subclassNid: query.data?.subclassNid,
        ...query
    };
}

// 获取项目自定义表单字段
// export function useQueryIssue513GetPartitionDetail(teamId, nodeId, enabledFlg) {
//     const query = useQuery(['Issue513', teamId, nodeId],
//         () => {
//             return http.issue_513_get_issue_partition_detail({ teamId, nodeId, })
//                 .then((res) => {
//                     if (res.resultCode === 200) {
//                         return res;
//                     }
//                 });
//         }, {
//         enabled: enabledFlg,
//     }
//     );
//     return {
//         attrList: query.data?.attrList,
//         criteriaList: query.data?.criteriaList,
//         bizNodeId: query.data?.bizNodeId,
//         ...query
//     };
// }

function getAttrPropByType(propList = [], type) {
    return propList.find(itemx => itemx.propType.toString() == type.toString()) || {};
}

// 获取attr组(tab)的所有property - 工作区设置端
export function useQuerySetting408GetAttrProps(teamId, attrsGrpId) {
    const query = useQuery(['setting408', teamId, attrsGrpId],
        () => {
            return http.setting_408_get_console_attrgrp_props({ teamId, attrsGrpId })
                .then((result) => {
                    if (result.resultCode === 200) {
                        let _attrList = result.nodeList.filter(item => {
                            return getAttrPropByType(item.propertyList, eConsolePropId.Prop_255_ext_attr_flg).propValue === "0"
                                || getAttrPropByType(item.propertyList, eConsolePropId.Prop_9_visible).propValue === "1"
                        })
                        let tableData = _attrList.map((item, index) => {
                            let attrsGrpId = item.attrsGrpId;
                            let displayName = item.name;
                            let grouping = getAttrPropByType(item.propertyList, eConsolePropId.Prop_255_ext_attr_flg);
                            let fieldType = getAttrPropByType(item.propertyList, eConsolePropId.Prop_14_ui_control);
                            let isEnable = getAttrPropByType(item.propertyList, eConsolePropId.Prop_9_visible);
                            let dataType = getAttrPropByType(item.propertyList, eConsolePropId.Prop_2_data_type);
                            let displayWidth = getAttrPropByType(item.propertyList, eConsolePropId.Prop_3_width);
                            let defaultValue = getAttrPropByType(item.propertyList, eConsolePropId.Prop_5_default_value);
                            let isSearch = getAttrPropByType(item.propertyList, eConsolePropId.Prop_59_queryable);
                            let format = getAttrPropByType(item.propertyList, eConsolePropId.Prop_13_format);
                            let optionalInput = getAttrPropByType(item.propertyList, eConsolePropId.Prop_12_selection);
                            let propModifiable = getAttrPropByType(item.propertyList, eConsolePropId.Prop_60_modifiable);
                            let mustFill = getAttrPropByType(item.propertyList, eConsolePropId.Prop_239_required);
                            let operation = ["上移", "下移"];
                            console.log("defaultValue", defaultValue);
                            return {
                                key: index,
                                nodeId: item.nodeId,
                                fieldName: {
                                    propType: "",
                                    propValue: item.nodeName,
                                },
                                fieldId: {
                                    propType: "",
                                    propValue: item.nodeId,
                                },
                                displayName, grouping, fieldType, isEnable, dataType, displayWidth, defaultValue, isSearch, format, optionalInput, operation, attrsGrpId, propModifiable, mustFill,
                            }
                        })
                        return { attrPropsList: result.nodeList, tableData: tableData };
                    }
                });
        }
    );
    return {
        attrPropsList: query.data?.attrPropsList,
        tableData: query.data?.tableData,
        ...query
    };
}

// setting-228 get_team_user_config 获取团队成员配置信息
export function useQuerySettin228_getTeamUserConfig(teamId, nodeId, enabled = true) {
    return useQuery(["setting_228_get_team_user_tree_width", teamId, nodeId], () => {
      return http.setting_228_get_team_user_tree_width({ teamId, nodeId })
        .then((result) => {
          if (result.resultCode === 200) {
            return result;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    }, {
      enabled: enabled
    });
}

//setting-407 get_console_selection_list 获取下拉字典
export function useQuerySetting407_getCodeValueList(teamId) {
    return useQuery(
        ["setting_407_get_console_selection_list", teamId],
        async() => {
            let result = await http.setting_407_get_console_selection_list({ teamId })
            return result?.optionList??[];
        }
    )
}

// team-571 get_space_valid_user_list 获取协作群有效的人员列表
// https://confluence.ficent.com/pages/viewpage.action?pageId=98415129
export function useQueryTeam571_GetSpaceVaildUserList(teamId, nodeId, searchAll, enabled) {
  return useQuery(team_571_get_space_vaild_user_list_query(teamId, nodeId, searchAll, enabled))
}

export const team_571_get_space_vaild_user_list_query = (teamId, nodeId, searchAll=eEnableFlg.disable, enabled = true) => ({
  queryKey:  [api_name.team_571_get_space_valid_user_list, teamId, nodeId, searchAll],
  queryFn: async () => {
    return http.team_571_get_space_valid_user_list({teamId, nodeId, searchAll})
        .then((res) => {
                return res?.userList || [];
        })
  },
  enabled: enabled,
})

