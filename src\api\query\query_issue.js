import { track_005_get_subclass_by_issue_node, track_009_get_issue_total } from '../http_issue';
import { issue512, issue506 } from "@/api/query/query_key";

// 获取项目自定义表单字段
export const issue_506_get_subclass_by_issue_node_query = (teamId, nodeId) => ({
  queryKey: [issue506, teamId, nodeId],
  queryFn: async () => {
    let result = await track_005_get_subclass_by_issue_node({ teamId, nodeId })
    if (result.resultCode == 200) { //eslint-disable-line
      return { projectId: result.projectId, subclassNid: result.subclassNid }
    }
  },
  cacheTime: Infinity
})

// issue-512 get_issue_total 按条件查询工单total
export const issue_512_get_issue_total_query = (teamId, issuegrpNodeId) => ({
  queryKey: [issue512, teamId, issuegrpNodeId],
  queryFn: async () => track_009_get_issue_total({ teamId, issuegrpNodeId }),
  cacheTime: Infinity
})