import React, { useEffect, useRef, useState } from "react";
import {Space,Button,Avatar,Comment,Tooltip,List,Row, Spin, Popconfirm, Col } from 'antd';
import {UserOutlined,DislikeOutlined, LikeOutlined, DislikeFilled, LikeFilled, LoadingOutlined} from '@ant-design/icons';
import TEditor,{TEditorPreview} from '@/components/TEditor/TEditor';
import { useQuery } from "@tanstack/react-query";
import {team_619_user_info_query} from "@/api/query/query"
import moment from "moment";
import { validateAuthActions } from "@/utils/commonUtils";
import { useAuthContext } from "@/context/AppAuthContextProvider";
import "./index.scss"
// import { shallowEqual, useSelector } from "react-redux";

/**
 * @description 带头像的编辑评论
 */
export function TCommentEditorHeader({ onSaveComment, uploadParams, uploadFileCallBack }) {
  // const state = useSelector((state) => ({
  //   userInfo: state.getIn(["login", "userInfo"]),
  // }), shallowEqual);
  const team619QueryData = useQuery(team_619_user_info_query())

  return (<div className="tms-comment">
    <Row gutter={10} justify="start" wrap={false}>
      <Col>
        {/* <Avatar size={32} src={state.userInfo.avatar} /> */}
        <Avatar size={32} src={team619QueryData?.data?.userInfo?.avatar} />
      </Col>
      <Col flex="auto">
        <TCommentEditor className="tms-comment-input" onSaveComment={onSaveComment} uploadParams={uploadParams} uploadFileCallBack={uploadFileCallBack}/>
      </Col>
    </Row>
  </div>)
}

/**
 * @description 评论编辑
 */
export function TCommentEditor({onSaveComment,uploadParams,uploadFileCallBack,...props}){

  const authActions = useAuthContext();

  const [inputVisible,setInputVisible] = useState(false);
  const [commentFlag, setCommentFlag] = useState(false); // 保存评论加载中flag
  const tRef = useRef(null); 
  const onSaveClick = () => {
    setCommentFlag(true)
    onSaveComment && onSaveComment({comment: tRef.current.getContent()}).then((result)=>{
      tRef.current && tRef.current.setContent("");
      setInputVisible(!result)
      setCommentFlag(false)
    })
  }

  const onCancelClick = () => {
    setInputVisible(false)
  }

  // 点击评论
  const handleOnClick = () =>{
    if(!validateAuthActions(authActions)) return;
    setInputVisible(true)
  }

  return <div {...props}>
    {!inputVisible && <div className="tms-comment-textare" onClick={()=> handleOnClick()}>
      编写评论......
    </div>}
    {
      inputVisible && <div className="comment-input">
        <Spin spinning={commentFlag} indicator={<LoadingOutlined/>}>
          <TEditor ref={tRef} uploadParams={uploadParams} uploadCallback={uploadFileCallBack}/>
          <Row gutter={10} justify="end" align="middle" className="comment-input-footer" style={{margin:"0"}}>
            <Col>
              <Button type="default" onClick={onCancelClick}>取消</Button>
            </Col>
            <Col>
              <Button type="primary" onClick={onSaveClick}>保存</Button>
            </Col>
          </Row>
        </Spin>
      </div>
    }
  </div>
}

/**
 * @description 对评论进行评论/编辑
 * @returns {"comment":""}
 */
function TCommentEditorByComment({editItem,onSaveClick,onCancelClick,uploadParams,uploadFileCallBack}){
  const tRef = useRef(null); 
  const [commentFlag, setCommentFlag] = useState(false); // 保存评论加载中flag
  
  // 编辑评论数据回调
  useEffect(()=>{
    if(editItem){
      setTimeout(() => {
        tRef && tRef.current.setContent(editItem.item.content)
        tRef.current.focus();
      }, 100);
    }
  },[editItem])
  const _onSaveClick = () => {
    setCommentFlag(true)
    onSaveClick && onSaveClick({comment: tRef.current.getContent()}).then((res) => {
      setCommentFlag(false)
    })
  }
  return  (<div className="tms-comment-input">
  <div className="comment-input">
    <Spin spinning={commentFlag} indicator={<LoadingOutlined/>}>
      <TEditor ref={tRef} uploadParams={uploadParams} uploadCallback={uploadFileCallBack}/>
      <Row justify="end" className="comment-input-footer">
        <Space size={10} direction="horizontal" >
          <Button type="default" onClick={onCancelClick}>取消</Button>
          <Button type="primary" onClick={_onSaveClick}>保存</Button>
        </Space>
      </Row>
    </Spin>
  </div>
</div>)
}


/**
 * @description 评论Item
 * data={
 *  author:"",
 *  avatar:"",
 *  content:"",
 *  datetime："",
 *  likeFlg: 0,
 *  dislikeFlg: 0,
 *  likeCnt: 0,
 *  dislikeCnt: 0,
 *  deleteFlg:0,
 *  editFlg:0,
 * }
 */
export const TCommentItem = ({config,children,onSaveComment,onEditComment,onDelComment,uploadParams,uploadFileCallBack,onLikeDisliskClick,onRefresh,...props}) => {

  const authActions = useAuthContext();

  const [showEditorVisible,setShowEditorVisible] = useState(false);
  const [showEditVisible,setShowEditVisible] = useState(false);
  const {id,author,avatar="",content="",datetime="",likeFlg=0,likeCnt=0,dislikeFlg=0,dislikeCnt=0,deleteFlg=0,editFlg=0} = config;

  // 回复
  const _onReplyClick = () => {
    if(!validateAuthActions(authActions)) return;
    setShowEditorVisible(true)
  }

  // 回复保存
  const _onSaveClick = (args) => {
    onSaveComment?.({parent:props.item,...args}).then(result => setShowEditorVisible(!result))
  }

  // 回复取消
  const _onCancelClick = () => {
    setShowEditorVisible(false)
  }

  // 编辑
  const _onEditClick = () => {
    if(!validateAuthActions(authActions)) return;
    setShowEditVisible(true)
  }

  // 编辑保存
  const _onEditComment = (args) => {
    onEditComment?.({ parent: props.item, ...args }).then(result => setShowEditVisible(!result))
  }

  // 编辑取消
  const _onCancelEditClick = () => {
    setShowEditVisible(false)
  }

  const getTimeDes = (dataStr) => {
    let time = moment(dataStr)
    // 刚刚 1min前
    let diff = moment().diff(time,"minute")
    if(diff < 1){
      return "刚刚"
    }
    // 最迟1小时前,显示xxmin
    if(diff < 60){
      return time.startOf('minute').fromNow();
    }
    // 今天xxx
    if(moment().diff(time,"day") < 3){
      return time.calendar(); 
    }
    return time.format('YYYY-MM-DD HH:mm:ss')
  }

  // 点赞
  const _onLikeDisliskClick = (opType) => {
    if(!validateAuthActions(authActions)) return;
    console.log("props", props);
    onLikeDisliskClick?.({ commentId: props.item.id, opType: opType, nodeId: props.item.nodeId})
  }

  // 删除评论
  const _onDelComment = () => {
    if(!validateAuthActions(authActions)) return;
    console.log("props", props);
    onDelComment?.({ commentId: props.item.id, nodeId: props.item.nodeId })
  }



  // 点赞、dislike、回复
  const _actions = [
    <span onClick={_onLikeDisliskClick.bind(this, likeFlg == 1 ? 5 : 4)}>
      {React.createElement(likeFlg == 1 ? LikeFilled : LikeOutlined)}
      <span className="comment-action">{likeCnt == 0 ? "" : likeCnt}</span>
    </span>,
    <span onClick={_onLikeDisliskClick.bind(this, dislikeFlg == 1 ? 7 : 6)}>
      {React.createElement(dislikeFlg == 1 ? DislikeFilled : DislikeOutlined)}
      <span className="comment-action">{dislikeCnt == 0 ? "" : dislikeCnt}</span>
    </span>,
    <span key="comment-basic-reply-to" onClick={_onReplyClick}>回复</span>,
    <span>{deleteFlg ? <span key="comment-basic-reply-to" onClick={_onEditClick}>编辑</span> : ""}</span>,
    <span>{editFlg ? <Popconfirm title='确定删除？' onConfirm={_onDelComment}><span key="comment-basic-reply-to">删除</span></Popconfirm> : ""}</span>,
  ];

  return <Comment
    id={"comment-"+id}
    className="common-comment"
    actions={_actions}
    author={<span className="comment-name">{author}</span>}
    avatar={<Avatar src={avatar} icon={<UserOutlined />} />}
    content={showEditVisible ?
      <Comment
        content={<TCommentEditorByComment editItem={props} uploadParams={uploadParams} uploadFileCallBack={uploadFileCallBack} onSaveClick={_onEditComment} onCancelClick={_onCancelEditClick}/>}
      /> :
      <TEditorPreview content={content} isedit uploadParams={{teamId:uploadParams?.teamId}} onRefresh={()=> onRefresh()}/>}
    datetime={<span>{getTimeDes(datetime)}</span>}>
    {/* 回复 */}
    {showEditorVisible && <Comment
      avatar={<Avatar icon={<UserOutlined />} />}
      content={<TCommentEditorByComment uploadParams={uploadParams} uploadFileCallBack={uploadFileCallBack} onSaveClick={_onSaveClick} onCancelClick={_onCancelClick}/>}
    />}
    {children}
  </Comment>
}

/**
 * @description 评论列表
 */
export default function TCommentList({dataSource=[],onSaveComment,onEditComment,onDelComment,uploadParams,uploadFileCallBack,onLikeDisliskClick,onRefresh}){

  const formatData = (cur=[],arr=[]) => {
    let data = []
    cur.forEach(item => {
      let childs = arr.filter(itemx => itemx.parentId == item.id);
      data = [...data,...childs] 
      if(childs && childs.length){
        let _data = formatData(childs,arr);
        data = [...data,..._data] 
      }
    });
    return data;
  }

  const getDataSource = (prev=[]) => {
    let cur = prev.filter(item => prev.every(itemx => itemx.id != item.parentId));
    cur.forEach(item => {
      item.children = formatData([item],prev)
    });
    return cur;
  }

  const getParentName = (id) => {
    return dataSource.find(item => id == item.id).userName
  } 
  
  const renderTCommentItem = (item) => {
    let children = item.children?.map(el => renderTCommentItem({ ...el, author: <span className="comment-name">{el.author}<span className="comment-huifu">回复</span>{getParentName(el.parentId)}</span> }))
    let config = {
      id: item.id,
      author: item.author,
      avatar: item.avatar,
      content: item.content,
      datetime: item.datetime,
      likeFlg: item.likeFlg,
      dislikeFlg: item.dislikeFlg,
      likeCnt: item.likeCnt,
      dislikeCnt: item.dislikeCnt,
      deleteFlg: item.deleteFlg,
      editFlg: item.editFlg,
    }
    return <li>
      <TCommentItem
        key={item.id}
        config={config}
        item={item}
        uploadParams={uploadParams}
        onSaveComment={onSaveComment}
        onEditComment={onEditComment}
        onDelComment={onDelComment}
        uploadFileCallBack={uploadFileCallBack}
        onLikeDisliskClick={onLikeDisliskClick}
        onRefresh={onRefresh}
        children={children} />
    </li>
  }

  return (<div>
    <List
      className="comment-list"
      itemLayout="horizontal"
      dataSource={getDataSource(dataSource)}
      renderItem={renderTCommentItem}
    />
  </div>)
}