import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import CssBaseline from '@mui/material/CssBaseline';
import { theme } from "../src/utils/muiThemeUtils";
import { ThemeProvider } from '@mui/material/styles';
import {HashRouter} from 'react-router-dom';
import { QueryClient, QueryClientProvider} from "@tanstack/react-query";

import {globalUtil} from "./utils/globalUtil"

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      retry: false,
      staleTime: 0,
      cacheTime: 0
    }
  }
});

globalUtil.setQueryClient(queryClient);

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <QueryClientProvider client={queryClient}>
    <ThemeProvider theme={theme}>
      <CssBaseline/>
      <App />
    </ThemeProvider>
  </QueryClientProvider>
);
