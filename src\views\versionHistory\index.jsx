import React, { useEffect, useState, useRef } from "react";
import { <PERSON>, Pa<PERSON>ation, <PERSON><PERSON>, Drawer } from "antd";
import {
  Box,
  Typography,
  Stack,
} from '@mui/material';
import moment from 'moment';
import "./index.scss";
import TopMenuBar from "../../components/TopMenuBar";
import {
  portal_1101_get_news_list,
  portal_1102_get_news_detail,
} from "../../api/http";
import {TEditorPreview} from "../../components/TEditor/TEditor";
import PortalLink from "../../components/PortalLink";

export default function Home(){
  const [newsList, setNewsList] = useState([]);
  const [popUpNews, setPopUpNews] = useState([]);
  const [total, setTotal] = useState();
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [versionDetailOpen, setVersionDetailOpen] = useState(false);
  const [newsId, setNewsId] = useState();

  useEffect(() => {
    getNewsList(pageNum, pageSize)
  },[]);

  // 获取版本历史列表
  const getNewsList = (pageNum, pageSize) => {
    let params = {
      teamId: 6666,
      objNodeId: 32378151744773,
      newsType: 1,
      pageNum: pageNum,
      pageSize: pageSize,
      keywords: ""
    }
    portal_1101_get_news_list(params).then(res => {
      if(res.resultCode == 200){
        setNewsList([...res.newsList])
        setPopUpNews([...res.newsList.filter(el => !el.linkUrl)])
        setTotal(res.total)
      }
    });
  }

  const pageChange = (page, pageSize) => {
    setPageNum(page)
    setPageSize(pageSize)
    getNewsList(page, pageSize)
  }

  const versionDetail = (newsId) => {
    setNewsId(newsId)
    setVersionDetailOpen(true)
  }
  return (
    <div className="versionHistory">
      <Box sx={{ height: 76 }}/>
      <Box sx={{ width: '100%' }}>
        <Box 
        className="versionHistory-TopMenuBar-box">
          <TopMenuBar/>  
        </Box>
        <div className="versionHistory-title">
          <span>版本历史</span>
        </div>
        <div className="versionHistory-contentBox">
          <List
            className="versionHistory-contentBox-List"
            size="small"
            dataSource={newsList}
            renderItem={
              (version, index) => (
                <List.Item 
                  key={version.id} 
                  extra={
                    <span className="versionList-li-dt" style={{ color: "#999" }}>
                      {moment(new Date(version.publishDt)).format("YYYY-MM-DD")}
                    </span>
                  }>
                    <div className="versionList-li">
                      <div className="versionList-li-text">
                        <span style={{ fontWeight: 'bold' }}>{version.title}</span>
                        {version.linkUrl 
                         ? 
                          <PortalLink 
                            className="fontsize-14" 
                            target="_blank" 
                            style={{ marginLeft: 10, color: '#0077f2' }} 
                            to={version.linkUrl} 
                            name="查看详情"
                          />
                         : 
                          <Button 
                            className="fontsize-14" 
                            type="link" 
                            style={{ margin: "0 0 0 10px", padding: 0, color: "#0077f2" }}
                            onClick={() => versionDetail(version.id)}
                          >
                            查看详情
                          </Button>  
                        }
                      </div>
                      <div style={{ whiteSpace: 'pre-wrap' }}>
                        <TEditorPreview content={version.subTitle}/>
                      </div>
                    </div>
                  </List.Item>
              )
            }
          />
          <div className="versionHistory-contentBox-Pagination">
            <Pagination 
              position="bottomCenter" 
              size="small" 
              current={pageNum}
              pageSize={pageSize}
              total={total} 
              showTotal={(total) => {
                return `共 ${total} 条`;
              }}
              showSizeChanger 
              showQuickJumper 
              onChange={pageChange}
            />
          </div>
        </div>
        <VersionDetailDrawer 
          objNodeId={newsId} 
          open={versionDetailOpen} 
          popUpNews={popUpNews}
          onClose={() => setVersionDetailOpen(false)}
        />
      </Box>
    </div>
  )
}

// 版本历史详情
export function VersionDetailDrawer({objNodeId, open, popUpNews, onClose}) {
  const [newsDetail, setNewsDetail] = useState({});
  const [largeView, setLargeView] = useState(0);
  const [previousAndNextInfo, setPreviousAndNextInfo] = useState({
    previousInfo: {},
    nextInfo: {},
  });
  useEffect(() => {
    if(open) {
      getNewsDetail(6666, objNodeId)
      getPreviousAndNext(popUpNews, objNodeId)
    }
  },[open])
  // 获取版本历史详情
  const getNewsDetail = (teamId, objNodeId) => {
    portal_1102_get_news_detail({teamId, objNodeId}).then(res => {
      if(res.resultCode == 200){
        setNewsDetail({...res.newsDetail})
      } else {
        setNewsDetail({})
      }
    });
  }

  // 上一篇、下一篇
  const getPreviousAndNext = (popUpNews, objNodeId) => {
    let index_ = popUpNews.findIndex(el => el.id === objNodeId)
    let previousInfo = {}
    let nextInfo = {}
    if(typeof index_ === "number") {
      if(index_ === 0) {
        nextInfo = popUpNews[index_ + 1]
      } else if (index_ === popUpNews.length - 1) {
        previousInfo = popUpNews[index_ - 1]
      } else {
        previousInfo = popUpNews[index_ - 1]
        nextInfo = popUpNews[index_ + 1]
      }
      setPreviousAndNextInfo({
        previousInfo,
        nextInfo,
      })
    }
  }

  // 切换文档(上一篇/下一篇)
  const versionDocChange = (docInfo) => {
    getNewsDetail(6666, docInfo?.id)
    getPreviousAndNext(popUpNews, docInfo?.id)
  }
  return (
    <Drawer 
      className="versionDetail-drawer"
      width={largeView == 0 ? '50%' : '90%'}
      onClose={() => {
        setPreviousAndNextInfo({
          previousInfo: {}, nextInfo: {}
        })
        onClose()
      }}
      open={open}
      destroyOnClose={false}
      title={
        <div style={{display:'flex',alignItems:'center'}}>
          <span className="fontsize-20">{newsDetail.title}</span>
          <a 
            onClick={() => setLargeView(largeView == 1 ? 0 : 1)} 
            style={{ paddingLeft: 20,color:'#000' }} 
            title={largeView == 0 ? '放大' : '缩小'} 
            className={'fontsize-16 iconfont '+(largeView == 0 ? 'fangda' : 'suoxiao')}
          />
        </div>
      }
      footer={
        <>
          <div className="previous-doc">
            <span 
              className="fontsize-12"
            >
              {previousAndNextInfo.previousInfo?.title}
            </span>
            <Button 
              size="small" 
              type="link" 
              disabled={!previousAndNextInfo.previousInfo?.id} 
              onClick={() => versionDocChange(previousAndNextInfo.previousInfo)}
            >
              上一篇
            </Button>
          </div>
          <div className="next-doc">
            <Button 
              size="small" 
              type="link" 
              disabled={!previousAndNextInfo.nextInfo?.id} 
              onClick={() => versionDocChange(previousAndNextInfo.nextInfo)}
            >
              下一篇
            </Button>
            <span 
              className="fontsize-12"
            >
              {previousAndNextInfo.nextInfo?.title}
            </span>
          </div>
        </>
      }
      > 
      <Box className="versionDetailDrawer">
        <Box className="versionDetailDrawer-heard">
          <Box className="versionDetailDrawer-heard-information">
            <Stack
            sx={{ flexWrap: "wrap", alignItems: 'center' }}
            direction={{ xs: "column", sm: "row" }}
            spacing={3}
            >
              <Box className="versionDetailDrawer-heard-stack-li">
                <Typography className="fontsize-14" variant="subtitle2">发布时间：</Typography>
                <Typography className="fontsize-14" variant="body2">{newsDetail.publishDt}</Typography>
              </Box>
              <Box className="versionDetailDrawer-heard-stack-li">
                <Typography className="fontsize-14" variant="subtitle2"><span title="访问量" className="iconfont yanjing_xianshi" style={{ fontSize: 22, color: "#999" }}/></Typography>
                <Typography className="fontsize-14" variant="body2">{newsDetail.visitCnt || 0}</Typography>
              </Box>
            </Stack>
          </Box>
        </Box>
        {/* <div style={{ padding: '6px 0'}}>
          <TEditorPreview content={newsDetail.subTitle}/>
        </div> */}
        <Box className="versionDetailDrawer-body">
          {newsDetail.body && <TEditorPreview content={newsDetail.body}/>}
        </Box>
      </Box>
    </Drawer>
  )
}