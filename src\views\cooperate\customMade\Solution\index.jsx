import React, { useEffect, useState } from "react";
import { Box, Typography, Stack } from "@mui/material";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../../utils/commonUtils";

export default function MultiScenario(props) {
  const { data } = props;
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  }, [data?.widgetList]);

  return (
    <>
      {/* 解决方案-start */}
      <Box className="solution" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography variant="h5">{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
        {!!filterWidgetList(moduleData, 2)[0]?.value && <Typography sx={{ mt: "20px", color: "#666" }} variant="body2">
          {filterWidgetList(moduleData, 2)[0]?.value}
        </Typography>}
        <Box className="solution-stack">
          <Stack
          sx={{ flexWrap: "wrap", justifyContent: "center", mt: "40px" }}
          direction={{ xs: "column", sm: "row" }}
          spacing={2}
          >
            {(filterWidgetList(moduleData, 6)[0]?.children || []).map((item, index) => (
              <Box className="solution-stack-li" key={item.widgetId}>
                <Typography variant="body2">{item.value}</Typography>
              </Box>
            ))}
          </Stack>
        </Box>
      </Box>
      {/* 解决方案-end */}
    </>
  );
}
