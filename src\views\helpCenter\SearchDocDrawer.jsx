import CloseIcon from '@mui/icons-material/Close';
import {
  <PERSON>, <PERSON><PERSON>, Drawer, <PERSON>ack, Typography
} from "@mui/material";
import { Divider, Input, List, Skeleton } from 'antd';
import { useEffect, useRef, useState } from "react";
import InfiniteScroll from 'react-infinite-scroll-component';
import { useNavigate } from "react-router-dom";
import {
  search_003_full_search
} from "../../api/http_helpcenter";
import { useDebounce } from "../../hook/index";
import "./index.scss";

// 搜索-右侧抽屉
export default function SearchDocDrawer({docNodeId, searchValue, setSearchInputValue, open, onClose, docSelectdChange}) {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const docTeamId = process.env.REACT_APP_WORKORDER_TEAMID; // 文档接口teamId
  const inputRef = useRef(null)
  const [data, setData] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [total, setTotal] = useState(0);
  const [dataList, setDataList] = useState([]);
  const loadMoreData = () => {
    if (loading) {
      return;
    }
    setLoading(true);
    setPageNo((pageNo) => pageNo+1)
    search_003_full_search({
      teamId: docTeamId,
      anchorNodeId: docNodeId,
      pageNo: pageNo + 1,
      pageSize: 20,
      keywords: searchValue,
    }).then((res) => {
      if(res.resultCode === 200) {
        let data_ = res.dataList.filter(el => !dataList.some(e => e.nodeId === el.nodeId))
        setDataList([...dataList, ...data_])
        setTotal(res.total)
        setLoading(false);
      } else {
        setTotal(0)
        setLoading(false);
      }
    })
  };
  useEffect(() => {
    if(open) {
      // loadMoreData();
      setTimeout(() => {
        inputRef.current.focus({ cursor: "end" })
      }, 300);
      fullSearch(docTeamId, docNodeId, pageNo, 20, searchValue)
    }
    return () => {
      setPageNo(1)
    }
  }, [open]);

  // 搜索
  const fullSearch = (teamId, anchorNodeId, pageNo, pageSize, keywords) => {
    let params = {
      teamId,
      anchorNodeId,
      pageNo,
      pageSize,
      keywords,
    }
    search_003_full_search(params).then((res) => {
      if(res.resultCode === 200) {
        setDataList([...res.dataList])
        setTotal(res.total)
      } else {
        setDataList([])
        setTotal(0)
      }
    })
  }
  
  const searchValueChange = useDebounce((e) => {
    setPageNo(1)
    fullSearch(docTeamId, docNodeId, pageNo, 22, e.target.value)
    setSearchInputValue(e.target.value)
  }, 500)

  // 跳转点击的文档
  const searchDocNodeClick = (node) => {
    navigate(`/help/doc/${node.nodeId}`)
    docSelectdChange(node)
    onClose()
  }
  return (
    <Drawer
    anchor="right"
    open={open}
    onClose={onClose}
    >
      <Box className="SearchDocDrawer">
        <div style={{ padding: '10px 0 0 10px', fontWeight: 'bold' }}>全文检索-帮助中心</div>
        <Box className="SearchDocDrawer-heard">
          <Stack
          sx={{ alignItems: 'center', width: '100%' }}
          direction={{ xs: "column", sm: "row" }}
          spacing={1}
          >
            <span className="iconfont sousuo" style={{ fontSize: 20 }}/>
            <Input 
            className="fontsize-12"
            size="small"
            style={{ width: 300, height: 30, borderRadius: 5 }}
            ref={inputRef}
            // className="SearchDocDrawer-heard-search-inp" 
            placeholder="输入文档关键字查找"
            defaultValue={searchValue}
            onChange={searchValueChange}/>
          </Stack>
          <Button 
          className="SearchDocDrawer-close" 
          variant="text" startIcon={<CloseIcon/>}
          onClick={onClose}/>
        </Box>
        <Box className="SearchDocDrawer-body">
          <Typography sx={{ fontSize: '14px', color: '#999' }} variant="body2">共{total}条结果</Typography>
          <div id="scrollableDiv" style={{ height: 'calc(100vh - 160px)', overflow: 'auto' }}>
            <InfiniteScroll
            dataLength={dataList.length}
            next={loadMoreData}
            height={'calc(100vh - 160px)'}
            hasMore={dataList.length < total}
            loader={<Skeleton avatar paragraph={{ rows: 1 }} active/>}
            endMessage={<Divider plain>没有更多了</Divider>}
            scrollableTarget="scrollableDiv"
            >
              <List
              dataSource={dataList}
              renderItem={(item) => (
                <List.Item key={item.nodeId} onClick={() => searchDocNodeClick(item)}>
                  <List.Item.Meta 
                    style={{ cursor: 'pointer' }}
                    title={<span dangerouslySetInnerHTML={{ __html: item.title }}></span>}
                    description={<div dangerouslySetInnerHTML={{ __html: item.body }}></div>}
                  />
                </List.Item>
              )}
            />
            </InfiniteScroll>
          </div>
        </Box>
      </Box>
    </Drawer>
  )
}