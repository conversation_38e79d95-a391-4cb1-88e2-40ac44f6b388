.helpCenter {
    // height: 100vh;
    // overflow: auto;
    // overflow-x: hidden;
}

.helpCenter-TopMenuBar-box {
    position: fixed;
    top: 0;
    margin: auto;
    padding: 0 20px;
    // max-width: 1260px !important;
    z-index: 1;
    background-color: white;
    box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
    width: 100vw;
    left: 0;
}

.helpCenter {
    background-color: #fff;
    height: 100%;
    .helpCenter-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 100px;
        // margin: auto;
        // min-width: 1200px;
        border-bottom: 1px solid #f2f2f2;
        .helpCenter-search {
            position: relative;
            display: flex;
            align-items: center;
            height: 30px;
            .helpCenter-search-inp {
                height: 30px;
                border: 1px solid #aaa;
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
                .MuiInputBase-input {
                    padding: 4px 4px;
                    font-size: 14px;
                }
            }
            .helpCenter-search-inp::after {
                display: none !important;
            }
            .helpCenter-search-inp::before {
                display: none !important;
            }
            .helpCenter-search-btn {
                padding: 0 10px;
                min-width: auto;
                height: 30px;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
                box-shadow: none;
                .MuiButton-startIcon {
                    margin: 0;
                }
            }
        }
        .helpCenter-chip {
            padding: 2px 6px;
            box-shadow: none;
            border-radius: 20px;
            .MuiChip-label {
                margin-left: 4px;
                padding: 0;
            }
        }
    }
    // 帮助文档sider
    .helpCenter-sider {
        margin: 0 0 0 100px;
        padding-bottom: 44px;
    }
    .helpCenter-content {
      padding: 5px 5px 0px 10px;
      width: 100%;
      overflow: auto;
      height: calc(100vh - 184px);
  }
}

// 文档底部-备案
.helpCenter-TMSRecord {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;;
    height: 44px;
}

.des-information {
    margin-top: 10px;
    .des-information-stack-li {
        display: flex;
        align-items: center;
        .MuiTypography-root:last-of-type {
            margin-right: 6px;
        }
    }
    .des-information-stack-li:not(:first-child) {
        color: #999;
    }
}

// 文字内容
.doc-content {
    padding: 0px;
}

// 图片内容
.doc-contentimg {
    margin-top: 20px;
    border: 1px solid #999;
}

// 点赞按钮
.a-literary-eulogy {
    position: relative;
    display: flex;
    justify-content: end;
    align-items: center;
    .MuiButtonBase-root {
        .MuiButton-startIcon {
            margin-right: 4px;
        }
    }
    .likes-animation {
        // display: block !important;
        position: absolute;
        font-size: 16px;
        color: pink;
        // animation: likes-animation 1s linear 1 forwards;
    }
    .likes-style {
        display: none;
        font-size: 16px;
    }
    // @keyframes likes-animation {
    //     0% {
    //         right: 0;
    //         bottom:20px;
    //         transform: scale(.5);
    //     }
    //     55%{
    //         right: 10px;
    //         opacity:1;
    //         transform: scale(1);
    //     }
    //     100% {
    //         right: 0;
    //         bottom:100px;
    //         opacity:0;
    //         transform: scale(.3);
    //     }
    // }
    .approve-of-btn {
        padding: 0;
        min-width: auto;
        border: none;
        font-size: 14px;
        color: #999;
        .MuiButton-startIcon {
            margin: 0;
            .iconfont {
                font-size: 16px;
            }
        }
    }
    .approve-of-btn:hover {
        background-color: transparent;
        border: none;
    }
    .oppose-btn {
        margin-left: 20px;
        color: white;
        background-color: #bbd8fc;
        .MuiButton-startIcon {
            .iconfont {
                transform: rotate(180deg);
            }
        }
    }
    .oppose-btn:hover {
        background-color: #bbd8fc;
        box-shadow: none;
    }
}

// 上一篇、下一篇
.doc-changing-over {
    display: flex;
    align-items: center;
}

// 相关资源
.related-doc {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    .MuiTypography-root:not(:first-child) {
        margin: 6px 0 0 10px;
        cursor: pointer;
    }
    .MuiTypography-root:not(:first-child):hover {
        text-decoration: underline;
        color: #0077f2;
    }
}

// 搜索-右弹抽屉
.SearchDocDrawer {
    width: 800px;
    .SearchDocDrawer-close {
        min-width: 30px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        font-size: 24px;
        color: #333;
        background-color: transparent;
        .MuiButton-startIcon {
            margin: 0;
        }
    }
    .SearchDocDrawer-heard {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #f2f2f2;
        .SearchDocDrawer-heard-search-inp {
            width: 100%;
            height: 100%;
            border: none;
            .MuiInputBase-input {
                padding: 4px 4px;
                font-size: 14px;
            }
        }
        .SearchDocDrawer-heard-search-inp::after {
            display: none !important;
        }
        .SearchDocDrawer-heard-search-inp::before {
            display: none !important;
        }
    }
    .SearchDocDrawer-body {
        padding: 10px;
    }
}

// 文件夹子节点
.FolderChildNode {
    .FolderChildNode-title {
        font-size: 24px;
    }
    .FolderChildNode-node {
        margin: 32px 0 0 40px;
        &-li {
            padding: 3px 6px;
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 14px;
            border-radius: 5px;
            cursor: pointer;
        }
        &-li:hover {
            background-color: #eff0f0;
        }
    }
}

.FolderChildNode-link {
    color: #333;
}
.FolderChildNode-link:hover {
    // text-decoration-line: underline;
}

.HelpBreadcrumb {
    margin-bottom: 10px;
    .HelpBreadcrumb-link:hover {
        text-decoration: underline;
    }
}