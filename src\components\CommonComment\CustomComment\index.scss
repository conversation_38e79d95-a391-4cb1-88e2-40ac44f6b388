.tms {
  &-comment {
    margin-top: 10px;
    margin-bottom: 10px;

    &-input{
      // flex: auto;
      // margin-right: 10px;
    }
    &-textare {
      flex: auto;
      border: 1px solid #d5d5d5;
      height: 60px;
      font-size: 14px;
      color: #999999;
      border-radius: 5px;
      padding: 10px;
    }
  }
}

.comment-input {

  .fr-second-toolbar{
    display: none;
  }

  &-footer {
    border: 1px solid #efefef;
    border-top: none;
    height: 50px;
    box-sizing: border-box;
    padding-right: 10px;
  }
}

.comment-name {
  color: #666666!important;
}

.comment-huifu {
  color: #ccc;
  margin: 0 10px;
}

.common-comment {
  .ant-comment-actions {
    margin-top: 0;
  }
}

.common-comment.focus {
  border-bottom: 1px solid #dfe1e5;
  background: #ebf2f9;
  margin-left: -10px;
  padding-left: 10px;
}