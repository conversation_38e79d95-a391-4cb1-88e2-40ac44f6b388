/* eslint-disable no-loop-func */
import React, { useEffect, useRef, useState } from "react";
import { Button, Input, Modal, Form, Select, Drawer, DatePicker, Upload, Checkbox, Table, InputNumber } from "antd";
import { PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useParams } from "react-router-dom";
import { nanoid } from "nanoid";
import { getIcon } from "@/views/issueTrack/utils/ArrayUtils";
import { eNodeTypeId } from "@/utils/TsbConfig";
import { eConsoleNodeType, eConsolePropId, eConsoleUiControl, eSelectionListId, eDebounceTime, eConsoleNodeId } from "@/utils/enum";
import TEditor from "@/components/TEditor/TEditor";
import DefaultAvatar from "@/components/DefaultAvatar";
import { useMutationIssue012CreateIssue } from "@/views/issueTrack/service/issueHooks";
import { compareObj, getPropValueByIdType, deleteInvalid } from "@/views/issueTrack/utils/ArrayUtils";
import "./../IssueDetail/IssueDetail.scss"
import { globalUtil } from "@/utils/globalUtil";
import UploadLoading from "@/components/UploadLoading/index";
import {useDebounce} from "../../../../hook/index"
import  ImgUpload from "@/views/issueTrack/views/helper/ImgUpload"
import { isEmpty } from "@/utils/ArrayUtils";
import DraggableDrawer from "@/components/DraggableDrawer";
import { eFileObjId } from "@/utils/enum"

// 新建Issue弹出层
// propertyList 应该改为 subclassAttrList
// 新建issue选择人员更改为当前空间有效人员 Walt 2023-03-27
export default function CreateIssueModal({ selectionList, spaceUserList, createIssueModalVisibleFlg, setCreateIssueModalVisibleFlg, onPostIssueCreated, onClose, subclassAttrList, navId, navQuery }) {

  const [fromRef] = Form.useForm();
  let editRef = useRef(null);

  const teamId =  process.env.REACT_APP_WORKORDER_TEAMID;
  const issueListNodeId =  process.env.REACT_APP_WORKORDER_NODEID;

  // const { teamId, nodeId: issueListNodeId } = useParams();
  const [formId, setFormId] = useState(nanoid());
  const [attrList, setAttrList] = useState(
    subclassAttrList.filter(attr => attr.nodeType == eConsoleNodeType.NodeType_1_Attribute &&
      // 匹配是否启用
      (attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_9_visible)?.propValue || 1) == 1 &&
      (isShowModifiable(attr) ? (attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_60_modifiable)?.propValue || 1) == 1
        : (attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_60_modifiable)?.propValue) == "0"))
      .map(attr => ({
        ...attr,
        uiControl: (attr.propertyList.find(el => el.propType == eConsolePropId.Prop_14_ui_control))?.propValue,
        selectionLid: attr.propertyList.find(_el => _el.propType == eConsolePropId.Prop_12_selection)?.propValue || "",
        requiredFlg: (attr.propertyList.find(el => el.propType == eConsolePropId.Prop_239_required))?.propValue || 0, //是否必填
        itemValue: attr.propertyList.find(el => el.propType == eConsolePropId.Prop_5_default_value)?.propValue || "", //默认值
        itemValueBak: attr.propertyList.find(el => el.propType == eConsolePropId.Prop_5_default_value)?.propValue || "" //原值,与itemValue相同
      }))
  );
  
  // 默认初始数据
  const [initialValues, setInitialValues] = useState({})
  const [objList, setObjList] = useState([]); //附件列表
  const [objRelList, setObjRelList] = useState([]); //引用资源列表
  const [createAnotherIssueFlg, setCreateAnotherIssueFlg] = useState(false); //是否继续新建另一个issue
  const [objRelModalContentChangedFlg, setObjRelModalContentChangeFlg] = useState(true); //资源引用弹框是否有内容变更
  const [uploadLoading, setUploadLoading] = useState(false); // 提交加载中

  const [fileListGroup, setFileListGroup] = useState([]); // 页面截图

  const { onIssue012CreateIssue } = useMutationIssue012CreateIssue(teamId, issueListNodeId, 1)

  const _load_issue012_create_issue_debounce = useDebounce(_load_issue012_create_issue, 500);

  // 是否过滤不可修改项
  function isShowModifiable(attr) {
    let modifiable = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_60_modifiable)?.propValue || 1
    let defaultValue = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_5_default_value)?.propValue
    // 如果不可修改有默认值 则不过滤
    if (modifiable == "0" && defaultValue != '') { 
      return false
    } else { // 1.不可修改，且没有默认值:创建时间/修改时间(创建时不显示，issue详情显示)， 2.可修改，创建和详情都显示
      return true
    }
  }

  // 默认值
  useEffect(() => {
    if (!isEmpty(attrList)) {
      let fields = {}
      attrList.map((item, index) => {
        if (item.itemValue) {
          return fields[item.nodeId] = item.itemValue
        }
      })
      // 新建工单跳转携带的默认值
      if(!!navId){
        fields[eConsoleNodeId.Nid_11204_WorkOrder_PageURL] = navQuery.href; // 页面地址
        setFileListGroup([{
          nodeId: eConsoleNodeId.Nid_11218_WorkOrder_ScreenShort,
          fileList: [navQuery.fileObj] 
        }])
      }
      setInitialValues(fields)
      fromRef.setFieldsValue(fields)
    }
  }, []);

  // 编辑器上传图片
  function uploadFileCallBack(data) {
    let link = data.link
    let name = link.substring(link.lastIndexOf("/") + 1)
    let fileObj = { objId: data.id, objType: 23, objName: name, seqNo: 1 }
    setObjList([...objList, fileObj]);
  }

  // 编辑器
  function editContentChange(index, attr, e) {
    let nodeId = attr.nodeId
    fromRef.setFieldValue(nodeId, editRef.current.getContent())
  }

  // 文本textBox
  function showTextBoxUI(attr, index) {
    let format = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_13_format)
    let value = getPropValueByIdType(selectionList, format.selectionLid, format.propValue)
    switch (value) {
      case "数字":
        return <InputNumber type={"number"} autoComplete="off"/>
      case "整数":
        return <InputNumber type={"number"} precision={0} autoComplete="off"/>
      default:
        return <Input style={{ width: 500, borderRadius: 3 }} maxLength={50} autoComplete="off"/>
    }
  }
  
  // 日期 date
  function showDateUI(attr, index) {
    let dateItem = attr.propertyList.find(prop => prop.propType == eConsolePropId.Prop_13_format)
    let format = dateItem ? getPropValueByIdType(selectionList, dateItem.selectionLid, dateItem.propValue) : "YYYY-MM-DD"
    var time = "HH:mm:ss"
    let showTime = format.indexOf(time) == -1 ? false : true
    return <DatePicker placeholder="请选择时间" format={format} showTime={showTime} />
  }

  // 是否可修改
  function modifyFlg(item) {
    let modifiableProp = item.propertyList.find(el => el.propType == eConsolePropId.Prop_60_modifiable)
    if (modifiableProp?.propValue == "0") {
      return true
    } else {
      return false
    }
  }

  // 提交表单
  const handleSubmitButtonClick = async () => {
    fromRef.submit();
  };

  // 012接口 - 新建issue
  async function _load_issue012_create_issue() {
    setUploadLoading(true);
    let formData = fromRef.getFieldsValue(); // 需要全部表单值，因为页面截图没有值
    // 处理日期数据，避免影响数据源，产生报错date.clone is not a function
    formData = { ...formData }
    for (let attr in formData) {
      attrList.filter(el => el.uiControl == eConsoleUiControl.Date).map((item, index) => {
        if (attr == item.nodeId && formData[attr]) {
          formData[attr] = formData[attr].format('YYYY-MM-DD')
        }
      });
      attrList.filter(el => el.uiControl == eConsoleUiControl.Image).map((item, index) => {
        if (attr == item.nodeId) {
          formData[attr] = fileListGroup.find(group => group.nodeId == item.nodeId)?.fileList.map(file => file.url).join();
        }
      });
    }
    // 清除空的表单值
    formData = deleteInvalid(formData);
    let params = { formId: formId, issuegrpNodeId: issueListNodeId, teamId: teamId, objRelList: [...objRelList, ...objList], ...formData }
    console.log(params)
    await new Promise((resolve, reject)=>  {
      onIssue012CreateIssue(params, {
        onSuccess: (data, vars) => {
          if (data.resultCode == 200) {
            console.log(data, vars)
            setFormId(nanoid());
            onPostIssueCreated(data.objNodeId); //列表更新
            // 如果勾选继续新建
            if (createAnotherIssueFlg) {
              editRef.current.setContent(null); //清空编辑器数据
              fromRef.resetFields(); //清空数据
              fromRef.setFieldsValue(initialValues);//恢复默认值
              setObjList([]); //清空附件列表
              setObjRelList([]); //清空资源引用列表
            } else {
              setCreateIssueModalVisibleFlg(false);
            }
          } else {
            console.log(data, vars);
          }
          resolve();
        }
      })
    })
    setUploadLoading(false);
  }

  //上传文件
  function uploadFile(info) {
    if (info.file.status !== 'uploading') {
      console.log('uploading', info.file, info.fileList);
    }
    if (info.file.status === 'done') {
      if (info.file.response.resultCode == 200) {
        let link = info.file.response.link
        let name = link.substring(link.lastIndexOf("/") + 1)
        let fileObj = { objId: info.file.response.id, objType: 23, objName: name, seqNo: 1 }
        setObjList([...objList, fileObj]);
        globalUtil.success(`${info.file.name} 文件上传成功！`);
      }
    } else if (info.file.status === 'error') {
      console.log('error', info.file, info.fileList);
      globalUtil.error(`${info.file.name} 文件上传失败！`);
    }
  }

  // 关闭新建问题弹窗
  function handleCloseCreateIssueModal() {
    let formData = fromRef.getFieldsValue(true);
    //比较Form初始值和最终的值是否相等
    let isNotChange = compareObj(formData, initialValues);
    onClose(isNotChange && objRelModalContentChangedFlg && isEmpty(fileListGroup)); // 传递表单是否没有改动的状态, true：没有改动(表单没有改动，直接关闭表单)，false：改动了 (弹出警告提示框)
  }

  // 页面截图组件上传的附件追加
  const uploadImagePush = (attrFileObj) => {
    let _fileListGroup = [...fileListGroup]
    if(!isEmpty(attrFileObj.fileList)){
      const idx = _fileListGroup.findIndex((item) => item.nodeId == attrFileObj.nodeId)
      _fileListGroup.splice(idx, 1, attrFileObj);
    } else {
      _fileListGroup = _fileListGroup.filter(item => item.nodeId != attrFileObj.nodeId );
    }
    setFileListGroup([..._fileListGroup])
  }

  return <>
    <DraggableDrawer className="tms-drawer add-issue"
      width={'60%'}
      onClose={handleCloseCreateIssueModal}
      open={true}
      closable={true}
      destroyOnClose={true}
      title={"新建工单"}
      footer={<div style={{ textAlign: "right" }} >
        <Checkbox style={{ paddingRight: 20 }} onChange={(e) => setCreateAnotherIssueFlg(e.target.checked)}>继续新建</Checkbox>
        <Button type="primary" style={{ borderRadius: 5 }} onClick={handleSubmitButtonClick}>提交</Button>
      </div>}
    >
      <UploadLoading spinning={uploadLoading} delay={eDebounceTime.fiveHundred}>
        <div className="issue-detail" >
          <Form
            form={fromRef}
            labelCol={{ span: 3 }}
            onFinish={_load_issue012_create_issue_debounce}
            scrollToFirstError// 提交失败自动滚动到第一个错误字段 tmsbug-4081:issue必填项为空，无法提交
          >
            {attrList.map((attr, index) => {
              return (
                <Form.Item name={attr.nodeId}
                  key={index}
                  label={[<span key={index}>{attr.nodeName}</span>]}
                  rules={[
                    {
                      required: attr.requiredFlg == 1,
                      message: "请填写" + attr.nodeName
                    }
                  ]}
                  // valuePropName="fileList"
                >
                  {
                    attr.uiControl == eConsoleUiControl.TextBox ?
                      showTextBoxUI(attr, index)
                      :
                      attr.uiControl == eConsoleUiControl.RichTextBox ?
                        <TEditor ref={editRef}
                          placeholderText=""
                          contentChanged={(e) => editContentChange(index, attr, e)}
                          uploadParams={{
                            teamId,
                            nodeId: issueListNodeId,
                            moduleName: "issues",
                            objId: "1",
                            objType: eNodeTypeId.nt_31704_objtype_issue_item
                          }}
                          uploadCallback={uploadFileCallBack}/> :
                        attr.uiControl == eConsoleUiControl.ListBox ?
                          attr.selectionLid == "-701" ?
                            <Select
                              showSearch
                              style={{ width: 300, borderRadius: 3 }}
                              //select选择框搜索
                              filterOption={(input, option) =>
                                (option.children[1].props.children || "").toLowerCase().indexOf(input.toLowerCase()) >= 0
                              }
                            >
                              {spaceUserList.map((_user, index) => {
                                return <Select.Option key={index} value={_user.userId}>
                                  <DefaultAvatar avatarSrc={_user.avatar} avatarFlg={1} />
                                  <span style={{ marginLeft: 5 }}>{_user.userName}</span>
                                </Select.Option>
                              })}
                            </Select>
                            :
                            <Select disabled={modifyFlg(attr)}
                              showSearch
                              style={{ width: 300, borderRadius: 3 }}
                              filterOption={(input, option) =>
                                (option.children || "").toLowerCase().indexOf(input.toLowerCase()) >= 0
                              }
                            >
                              {selectionList.filter(item => item.selectionId == attr.selectionLid).map((item, index) => {
                                if (item.id == eSelectionListId.Selection_1908_IssueType) {
                                  return <Select.Option key={index} value={item.propType}>
                                    {getIcon(item.propType)} {item.propValue}
                                  </Select.Option>
                                } else {
                                  return <Select.Option key={index} value={item.propType}>
                                    {item.propValue}
                                  </Select.Option>
                                }
                              })
                              }
                            </Select> :
                          // 时间选择
                          attr.uiControl == eConsoleUiControl.Date ?
                            showDateUI(attr, index) : 
                          attr.uiControl == eConsoleUiControl.Image ? 
                            <ImgUpload
                              visible={createIssueModalVisibleFlg}
                              fileObj={navQuery.fileObj}
                              uploadImagePush={uploadImagePush}
                              attrNodeId={attr.nodeId}
                            />
                          :  
                            <></>
                  }
                </Form.Item>
              )
            })}
            <Form.Item name="fujian" label="附件">
              <Upload name={'file'}
                multiple={true}
                action={`${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`}
                data={{ teamId: teamId, moduleName: "issues", nodeId: eFileObjId, objType: eNodeTypeId.nt_31704_objtype_issue_item }}
                onChange={uploadFile}
              >
                <Button type="link" icon={<PlusOutlined />}>上传附件</Button>
              </Upload>
            </Form.Item>
          </Form>
        </div>
      </UploadLoading>
    </DraggableDrawer>
  </>;
}

