import React, { useEffect, useState } from "react";
import { Box, Typography, Button } from "@mui/material";
import "./index.scss";
import {
  isArrayUtils,
  filterWidgetList,
  formatTreeData,
} from "../../../../utils/commonUtils";
import { formatVariantColor } from "../../../../utils/muiThemeUtils";

export default function BottomAdvert(props) {
  const { data, openContactUsModal, openApplyDrawer } = props;
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData]);
    }
  }, [data?.widgetList]);
  return (
    <>
      {/* 底部广告-start */}
      <Box
        className="bottom-iTeam"
        style={{
          background:
            filterWidgetList(moduleData, 12)[0]?.value || "transparent",
        }}
      >
        {!!filterWidgetList(moduleData, 1)[0]?.value && (
          <Typography sx={{ color: "white" }} variant="h5">
            {filterWidgetList(moduleData, 1)[0]?.value}
          </Typography>
        )}
        {filterWidgetList(moduleData, 4).length > 0 && (
          <Box className="bottom-iTeam-btn">
            {filterWidgetList(moduleData, 4).map((item, index) => (
              <Button
                key={item.widgetId}
                sx={{ ml: index > 0 ? "10px" : ""}}
                variant={formatVariantColor(item.style)?.variant}
                color={formatVariantColor(item.style)?.color}
                onClick={item.value == '咨询客服' ? openContactUsModal : openApplyDrawer}
              >
                {item.value}
              </Button>
            ))}
          </Box>
        )}
      </Box>
      {/* 底部广告-end */}
    </>
  );
}
