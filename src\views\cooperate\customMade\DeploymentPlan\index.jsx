import React, { useEffect, useState } from "react";
import { Image } from "antd";
import {
  Box,
  Typography,
  Stack,
} from "@mui/material";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../../utils/commonUtils";

export default function DeploymentPlan({data}) {
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  }, [data?.widgetList]);

  return (
    <>
      {/* 部署方案-start */}
      <Box className="deployment-plan" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography variant="h5" sx={{ fontSize: "34px" }}>{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
        {!!filterWidgetList(moduleData, 2)[0]?.value && <Typography sx={{ mt: "10px", color: "#666" }} variant="body2">
          {filterWidgetList(moduleData, 2)[0]?.value}
        </Typography>}
        {filterWidgetList(moduleData, 6).length > 0 && <Box className="deployment-plan-stack">
          <Stack
          sx={{ flexWrap: "wrap", justifyContent: "center", mt: "40px" }}
          direction={{ xs: "column", sm: "row" }}
          spacing={4}
          >
            {filterWidgetList(moduleData, 6).map((item, index) => (
              <Box className="deployment-plan-stack-li" key={item.widgetId}>
                <Box className="deployment-plan-stack-li-header">
                  <Typography className="fontsize-18" variant="h6" sx={{ color: 'white' }}>{filterWidgetList(item?.children, 1)[0]?.value}</Typography>
                  <Typography sx={{ mt: "10px", fontSize: 12 }} variant="body2">
                    {filterWidgetList(item?.children, 2)[0]?.value}
                  </Typography>
                </Box>
                <Box className="deployment-plan-stack-li-content">
                  <Stack
                  sx={{ flexWrap: "wrap", justifyContent: "space-between" }}
                  direction={{ xs: "column", sm: "row" }}
                  // spacing={2}
                  >
                    {filterWidgetList(item?.children, 6).map((item1, index1) => (
                      <Box className="deployment-plan-stack-li-content-li" key={item1.widgetId}>
                        <Image className="deployment-plan-stack-li-content-li-icon" preview={false} width={30} height={30} src={filterWidgetList(item1?.children, 3)[0]?.value}/>
                        <Typography className="fontsize-18" variant="h6">{filterWidgetList(item1?.children, 1)[0]?.value}</Typography>
                        <Typography sx={{ fontSize: 14, color: "#666" }} variant="body2">
                          {filterWidgetList(item1?.children, 2)[0]?.value}
                        </Typography>
                      </Box>
                    ))}
                  </Stack>
                </Box>
              </Box>
            ))}
          </Stack>
        </Box>}
      </Box>
      {/* 部署方案-end */}
    </>
  );
}
