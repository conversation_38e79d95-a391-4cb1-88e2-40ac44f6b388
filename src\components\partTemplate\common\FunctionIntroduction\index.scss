// iTeam简介
.FunctionIntroduction {
    position: relative;
    padding: 50px 0;
    width: 100%;
    &-title {
        font-size: 34px;
        text-align: center;
    }
    &-content {
        padding: 0 20px;
        margin: auto;
        width: 1260px;
        &-tabs {
            margin-top: 10px;
            .MuiTabs-scroller {
                .MuiButtonBase-root {
                    font-size: 14px;
                    font-weight: bold;
                    color: #333;
                }
                .Mui-selected {
                    color: #1976d2;
                }
            }
        }
        &-tabsBody {
            // padding: 0 40px;
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            &-left {
                width: 360px;
                .tabsBody-left-title {
                }
                .tabsBody-left-textList {
                    margin-top: 20px;
                    .tabsBody-left-text {
                        display: flex;
                        align-items: center;
                        margin-top: 10px;
                    }
                }
            }
            &-right {
                width: 800px;
                height: fit-content;
                border-bottom-left-radius: 5px;
                border-bottom-right-radius: 5px;
                &-topbar {
                    position: relative;
                    padding: 8px 80px;
                    height: 30px;
                    background-color: #f2f2f2;
                    box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
                    .white-bar {
                        margin: auto;
                        width: 100%;
                        height: 100%;
                        background-color: white;
                    }
                    .dot-list {
                        position: absolute;
                        display: flex;
                        align-items: center;
                        top: 50%;
                        left: 10px;
                        transform: translate(0,-50%);
                        .dot-li {
                            margin-right: 14px;
                            width: 8px;
                            height: 8px;
                            border-radius: 50%;
                        }
                        .dot-red {
                            background-color: #ff5f57;
                        }
                        .dot-yellow {
                            background-color: #ffbe2e;
                        }
                        .dot-green {
                            background-color: #28c940;
                        }
                    }
                }
                .slick-current {
                }
                .slick-active {
                    box-shadow: 0 4px 4px 0 rgb(77 77 77 / 1%), 0 2px 8px 0 rgb(77 77 77 / 7%);
                }
            }
        }
    }
}

// iTeam简介居左-默认模板
.FunctionIntroduction-left {
    .FunctionIntroduction-content{
        &-tabsBody {
            flex-direction: row !important;
        }
    }
}

// iTeam简介居右
.FunctionIntroduction-right {
    .FunctionIntroduction-content{
        &-tabsBody {
            flex-direction: row-reverse !important;
        }
    }
}