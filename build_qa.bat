echo off
set filename=%date:~0,4%%date:~5,2%%date:~8,2%%time:~0,2%%time:~3,2%
set "filename=%filename: =0%"
set REACT_APP_VERSION=%filename%

echo "Q build start" 
call npm run build:qa
echo "P build finish"

echo "delete portal.war start"
del portal.war
echo "delete finish"

@REM Compressed file
echo "tar start"
@REM tar cvf portal-qa-%filename%.zip -C ROOT-QA .
jar -cvfM portal.war -C ROOT-QA .
echo "tar finish"

set winpath=%cd%

echo "start Upload files to TMS qa server 1"
"E:\software\WinSCP\WinSCP.com" ^
  /log="E:\temp\WinSCP\%filename%.log" ^
  /ini=nul ^
  /command ^
  "open sftp://tomcat:Y%%28Qfa04NtzhzJJm@*************/ -hostkey=""ssh-ed25519 255 wIYJMS7VFLplj/TZBoBo8Jo+GM+GbayWYtXEvDJrGNg""" ^
  "lcd %winpath%" ^
  "cd /uploads/tomcat-webapps-portal" ^
  "put portal.war" ^
  "exit"

echo "start Upload files to TMS qa server 2"
"E:\software\WinSCP\WinSCP.com" ^
  /log="E:\temp\WinSCP\%filename%.log" ^
  /ini=nul ^
  /command ^
  "open sftp://tomcat:vNha%%3DCzO5F8bgc%%7ErJs@************/ -hostkey=""ssh-ed25519 255 80LCM6yjJj4afr+GS6iQ5cz4Lr1Bl0Iz+8Gg8WnmImo""" ^
  "lcd %winpath%" ^
  "cd /uploads/tomcat-webapps-portal" ^
  "put portal.war" ^
  "exit"

echo ***
echo ***
echo ***
echo build Q Code success!

pause