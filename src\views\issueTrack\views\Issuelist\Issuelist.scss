//在一定范围内，隐藏滚动条
.issue-list {
  &::-webkit-scrollbar {
    // display: none;
    width: 4px;
    height: 3px;
  }
  &::-webkit-scrollbar-thumb {
    display: none;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ededed;
    border-radius: 10px;
  }
}
// 鼠标移上去显示
.issue-list:hover {
  // &::-webkit-scrollbar {
  //   display: block;
  // }
  &::-webkit-scrollbar-thumb {
    display: block;
  }
}
// 短列表sider
.issue-list-sider {
  border-right: 1px solid #f2f2f2;
  background-color: white;
  height: 100%;
  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
  }
  &-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
    border-bottom: 1px solid #f2f2f2;
    .ant-select-selection-item {
      padding-right: 12px !important;
    }
    .color333 {
      color: #333 !important;
    }
  }
  &-pagination {
    background: #f5f5f5;
    text-align: center;
    height: 26px;
    .ant-pagination-item-active {
      font-weight: 500;
      background: inherit;
      border-color: #f5f5f5;
    }
  }
}
// 短列表
.issue-list {
  height: 0px;
  width: 100%;
  overflow-y: overlay; // todo: 考虑部分浏览器不兼容的问题
  flex: auto;
  // 选中的状态
  .tms-item-checked {
    padding: 8px 20px 8px 18px !important;
  }
  &-item {
    width: 100%;
    padding: 8px 20px;
    box-sizing: border-box;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 17px;
    box-shadow: 0px 1px 0px 0px #f2f2f2;
    // border-bottom: 1px solid #F2F2F2;
    &-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 3px;
      a {
        font-size: 12px;
        color: #3279fe !important;
      }
      .ant-btn-icon-only {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 16px !important;
        width: 16px !important;
      }
      .iconfont {
        line-height: unset !important;
      }
      .tree-dir-more-btn {
        display: none;
      }
    }
    &-type {
      width: 14px;
      height: 14px;
    }
    &-num {
      margin-left: 6px;
    }
    &-des {
      // padding-left: 20px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  // 鼠标移动至list-item
  &-item:hover {
    // 背景变色
    background-color: #f2f2f2;
    // 更多icon显示
    .tree-dir-more-btn {
      display: block;
    }
    // 收藏icon隐藏
    .shoucang1 {
      display: none;
    }
    // icon抖动处理
    .ant-btn-icon-only {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 16px !important;
      width: 16px !important;
    }
  }
}
// 调整issuelist分页三个点的宽度，使其能刚好适应一行而不至于溢出
.ant-pagination-prev,
.ant-pagination-next,
.ant-pagination-jump-prev,
.ant-pagination-jump-next {
  min-width: 0px !important;
}
// 定义刷新加载的动画
.refresh-icon {
  position: relative;
  animation: refreshRotate 1s infinite forwards;
}
.refresh-position {
  position: absolute !important;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
@keyframes refreshRotate {
  0% {
  }
  10% {
    transform: rotate(36deg);
  }
  20% {
    transform: rotate(72deg);
  }
  30% {
    transform: rotate(108deg);
  }
  40% {
    transform: rotate(144deg);
  }
  50% {
    transform: rotate(180deg);
  }
  60% {
    transform: rotate(216deg);
  }
  70% {
    transform: rotate(252deg);
  }
  80% {
    transform: rotate(288deg);
  }
  90% {
    transform: rotate(324deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 空白页
.blank-page {
  text-align: center;
  padding-top: 200px;

  &-title {
    color: #999;
    font-size: 18px;
    padding-bottom: 10px;
  }

  &-des {
    font-size: 12px;
    color: #666;

    a {
      color: #0077f2;
    }
  }
}
