import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Button
} from "@mui/material";
import "./Coop_Part_0_Banner.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../utils/commonUtils";
import { formatVariantColor } from "../../../utils/muiThemeUtils";

export default function Coop_Part_0_Banner(props) {
  const { data, openContactUsModal, openApplyDrawer } = props;
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  },[data?.widgetList])
  return (
    <>
      {/* 招募主体区-start */}
      <Box className="recruit-main" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography variant="h3" sx={{ fontSize: "40px", color: 'white' }}>{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
        {!!filterWidgetList(moduleData, 2)[0]?.value && <Typography sx={{ mt: "10px", color: '#cecfd0' }} variant="h6">
          {filterWidgetList(moduleData, 2)[0]?.value}
        </Typography>}
        {filterWidgetList(moduleData, 4).length > 0 && <Box className="recruit-main-btn">
          {filterWidgetList(moduleData, 4).map((item, index) => (
            <Button 
            key={item.widgetId}
            sx={{ ml: index > 0 ? "10px" : ""}}
            variant={formatVariantColor(item.style)?.variant}
            color={formatVariantColor(item.style)?.color}
            onClick={item.value == '咨询客服' ? openContactUsModal : openApplyDrawer}
            >
              {item.value}
            </Button>
          ))}
        </Box>}
      </Box>
      {/* 招募主体区-end */}
    </>
  );
}
