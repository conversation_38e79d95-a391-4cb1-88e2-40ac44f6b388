.productsDetail {
    height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
}

.productDetail-TopMenuBar-box {
    position: fixed;
    top: 0;
    // left: 50%;
    // transform: translate(-50%, 0);
    margin: auto;
    padding: 0 20px;
    // width: 100%;
    // max-width: 1260px !important;
    z-index: 1;
    background-color: white;
    box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
    width: 100vw;
    left: 0;
}

// 实时演示
.demonstrate {
    padding: 50px 0;
    text-align: center;
}
.demonstrate-page {
    margin: 10px auto 0;
    width: 1200px;
    // min-height: 600px;
    box-shadow: 1px 1px 1px -1px rgb(0 0 0 / 5%), 0px 1px 1px 0px rgb(0 0 0 / 4%), 0px 1px 3px 1px rgb(0 0 0 / 6%) !important;
    &-top {
        position: relative;
        padding: 8px 120px;
        width: 100%;
        background-color: #f2f2f2;
        .white-bar {
            margin: auto;
            width: 100%;
            height: 16px;
            background-color: white;
        }
        .dot-list {
            position: absolute;
            display: flex;
            align-items: center;
            top: 50%;
            left: 10px;
            transform: translate(0,-50%);
            .dot-li {
                margin-right: 14px;
                width: 10px;
                height: 10px;
                border-radius: 50%;
            }
            .dot-red {
                background-color: #ff5f57;
            }
            .dot-yellow {
                background-color: #ffbe2e;
            }
            .dot-green {
                background-color: #28c940;
            }
        }
    }
}

// 集成
.integration {
    padding: 50px 0;
    text-align: center;
    background-color: #f1f7fb;
}
.integration-stack-li {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 90px;
}

// 示例
.give-typical-examples {
    padding: 50px 0;
    text-align: center;
}


// 为什么选择单个产品
.why-choose {
    padding: 50px 0;
    &-title {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    &-tabs {
        margin-top: 40px;
        .MuiTabs-scroller {
            .MuiButtonBase-root {
                font-size: 14px;
                font-weight: bold;
                color: #333;
            }
            .Mui-selected {
                color: #1976d2;
            }
        }
    }
    .why-choose-tabbar__body {
        position: relative;
        margin: 40px auto 0;
        padding: 0 20px;
        width: 1260px;
        .why-choose-tabbar__item {
            .why-choose-advantages {
                display: flex;
                flex-wrap: wrap;
                margin: -20px;
                .why-choose-advantages-elem {
                    padding: 20px;
                    width: 33%;
                    box-sizing: border-box;
                    .why-choose-advantage {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        height: 100%;
                        min-height: 320px;
                        padding: 28px;
                        border-radius: 4px;
                        box-shadow: 0 4px 16px 0 rgb(77 77 77 / 12%), 0 1px 2px 0 rgb(77 77 77 / 6%);
                        background-color: #fff;
                        box-sizing: border-box;
                        .why-choose-advantage__title {
                            font-size: 20px;
                            font-weight: bold;
                        }
                        .why-choose-advantage__text {
                            color: #5f5f5f;
                            font-weight: 400;
                        }
                    }
                    .why-choose-advantage-cta {
                        color: #fff;
                        height: 100%;
                        padding: 36px 28px;
                        border-radius: 4px;
                        box-shadow: 0 4px 16px 0 rgb(77 77 77 / 12%), 0 1px 2px 0 rgb(77 77 77 / 6%);
                        background: linear-gradient(180deg,#1876d2 0,#03a9f4 99.18%);
                        box-sizing: border-box;
                        .why-choose-advantage-cta__title {
                            font-size: 20px;
                            font-weight: bold;
                        }
                        .why-choose-advantage-cta__text {}
                        .why-choose-advantage-cta__link {
                            margin-top: 36px;
                            display: inline-block;
                            color: #fff;
                        }
                    }
                }
            }
        }
    }
}

// 产品互通
.ProductInterworking {
    padding: 50px 0;
    text-align: center;
    &-stack {
        padding: 0 20px;
        margin: auto;
        width: 1260px;
        .ProductInterworking-stack-li {
            margin-bottom: 40px;
            width: 570px;
            text-align: left;
        }
        .ProductInterworking-stack-li:nth-child(3n) {
            margin-left: 0 !important;
        }
    }
}