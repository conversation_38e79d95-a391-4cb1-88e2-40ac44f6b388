import MobileDetect from 'mobile-detect';
import * as qs from "qs"
import sm4 from "@/utils/sm4";

/**
 * @desc json转url后的拼接参数
 */
export function JsonTurnUrl(json){
    if(json instanceof Object){
      return qs.stringify(json);
    }
    throw("JsonTurnUrl 入参错误，请使用{'x':'x'}形式");
}

/**
 * @desc url后的拼接参数转json
 * @param {String} url 
 * @returns {Object} {xxx:xxx}
 */
export function UrlTurnJson(url){
    const index = url.indexOf('?');
    const obj = {};
    if(index === -1) return obj;
    const queryStr = url.slice(index + 1);
    return qs.parse(queryStr);
}

/**
 * 判断是否是微信浏览器
 */
export const isWeiXin = () => {
    var ua = window.navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        return true; //是微信浏览器
    } else {
        return false; //其他设备
    }
}

/**
 * 判断设备是PC端还是移动端: false PC端 | true 移动端
 */
export const isMobile = () => {
    return !!navigator.userAgent.match(/(iPhone|iPad|Android|ios|iOS|WebOS|Windows Phone|Phone)/i);
}

/**
 * 
 * 判断设备是否是平板
 */
export const isPad = () => {
    return navigator.userAgent.indexOf('iPad') > -1
}

/**
 * 判断设备是Android
 */
export const isAndroid = () => {
    return /android/i.test(navigator.userAgent.toLowerCase());
};

/**
 * 判断设备是iOS
 */
export const isIOS = () => {
    let reg = /iPhone|iPad|iOS|Macintosh/i;
    return reg.test(navigator.userAgent.toLowerCase());
};

/**
 * 获取浏览器类型及其版本
 */
export const getExplorerInfo = () => {
    let t = navigator.userAgent.toLowerCase();
    return 0 <= t.indexOf("mise")
        ? {
            // IE<11
            type: "IE",
            version: Number(t.match(/mise ([\d]+)/)[1]),
        }
        : !!t.match(/trident\/.+?rv:(([\d.]+))/)
            ? {
                // IE 11
                type: "IE",
                version: 11,
            }
            : 0 <= t.indexOf("edge")
                ? {
                    type: "Edge",
                    version: Number(t.match(/edge\/([\d]+)/)[1]),
                }
                : 0 <= t.indexOf("firefox")
                    ? {
                        type: "Firefox",
                        version: Number(t.match(/firefox\/([\d]+)/)[1]),
                    }
                    : 0 <= t.indexOf("chrome")
                        ? {
                            type: "Chrome",
                            version: Number(t.match(/chrome\/([\d/]+)/)[1]),
                        }
                        : 0 <= t.indexOf("opera")
                            ? {
                                type: "Safari",
                                version: Number(t.match(/version\/([\d]+)/)[1]),
                            }
                            : {
                                type: t,
                                version: -1,
                            };
}


/**
 * 版本信息获取、各主流浏览器
 */
export const getDevice = () => {

    var getBrowser = function () {
        var u = navigator.userAgent
        var bws = [{
            name: 'sgssapp',
            it: /sogousearch/i.test(u)
        }, {
            name: 'wechat',
            it: /MicroMessenger/i.test(u)
        }, {
            name: 'weibo',
            it: !!u.match(/Weibo/i)
        }, {
            name: 'uc',
            it: !!u.match(/UCBrowser/i) || u.indexOf('UBrowser') > -1
        }, {
            name: 'sogou',
            it: u.indexOf('MetaSr') > -1 || u.indexOf('Sogou') > -1
        }, {
            name: 'xiaomi',
            it: u.indexOf('MiuiBrowser') > -1
        }, {
            name: 'baidu',
            it: u.indexOf('Baidu') > -1 || u.indexOf('BIDUBrowser') > -1
        }, {
            name: '360',
            it: u.indexOf('360EE') > -1 || u.indexOf('360SE') > -1
        }, {
            name: '2345',
            it: u.indexOf('2345Explorer') > -1
        }, {
            name: 'edge',
            it: u.indexOf('Edge') > -1
        }, {
            name: 'ie11',
            it: u.indexOf('Trident') > -1 && u.indexOf('rv:11.0') > -1
        }, {
            name: 'ie',
            it: u.indexOf('compatible') > -1 && u.indexOf('MSIE') > -1
        }, {
            name: 'firefox',
            it: u.indexOf('Firefox') > -1
        }, {
            name: 'safari',
            it: u.indexOf('Safari') > -1 && u.indexOf('Chrome') === -1
        }, {
            name: 'qqbrowser',
            it: u.indexOf('MQQBrowser') > -1 && u.indexOf('QQ') === -1
        }, {
            name: 'qq',
            it: u.indexOf('QQ') > -1
        }, {
            name: 'chrome',
            it: u.indexOf('Chrome') > -1 || u.indexOf('CriOS') > -1
        }, {
            name: 'opera',
            it: u.indexOf('Opera') > -1 || u.indexOf('OPR') > -1
        }]
        for (var i = 0; i < bws.length; i++) {
            if (bws[i].it) {
                return bws[i].name
            }
        }

        return 'other'
    };
    // 系统区分
    var getOS = function () {
        var u = navigator.userAgent
        if (!!u.match(/compatible/i) || u.match(/Windows/i)) {
            return 'windows'
        } else if (!!u.match(/Macintosh/i) || u.match(/MacIntel/i)) {
            return 'macOS'
        } else if (!!u.match(/iphone/i) || u.match(/Ipad/i)) {
            return 'ios'
        } else if (u.match(/android/i)) {
            return 'android'
        } else if (u.match(/Ubuntu/i)) {
            return 'Ubuntu'
        } else {
            return 'other'
        }
    };
    //判断数组中是否包含某字符串
    Array.prototype.contains = function (needle) {
        for (i in this) {
            if (this[i].indexOf(needle) > 0)
                return i;
        }
        return -1;
    }

    var device_type = navigator.userAgent; //获取userAgent信息
    var md = new MobileDetect(device_type); //初始化mobile-detect
    var os = md.os(); //获取系统
    console.log(os)
    var model = "";
    if (os == "iOS") { //ios系统的处理
        os = +md.version("iPhone");
        console.log(os)
        model = md.mobile();
    } else if (os == "AndroidOS") { //Android系统的处理
        os = md.os() + md.version("Android");
        var sss = device_type.split(";");
        var i = sss.contains("Build/");
        if (i > -1) {
            model = sss[i].substring(0, sss[i].indexOf("Build/"));
        }
    }
    else {
        os = getOS();
        model = getBrowser();
    }
    return { "os": os, "dev": model }
}

/**
 * 复制文本到剪切板中
 *
 * @export
 * @param {*} value 需要复制的文本
 * @param {*} cb 复制成功后的回调
 */
export function copyToClip(value, cb) {
    // 动态创建 textarea 标签
    const textarea = document.createElement("textarea");
    // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
    textarea.readOnly = "readonly";
    textarea.style.position = "absolute";
    textarea.style.left = "-9999px";
    // 将要 copy 的值赋给 textarea 标签的 value 属性
    textarea.value = value;
    // 将 textarea 插入到 body 中
    document.body.appendChild(textarea);
    // 选中值并复制
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    document.execCommand("Copy");
    document.body.removeChild(textarea);
    if (cb && Object.prototype.toString.call(cb) === "[object Function]") {
        cb();
    }
}

/** 
 * @desc String(中文) 转 base64
 */
export function base64encode(str) {
    // Firstly, escape the string using encodeURIComponent to get the UTF-8 encoding of the characters, 
    // Secondly, we convert the percent encodings into raw bytes, and add it to btoa() function.
    let utf8Bytes = encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (match, p1) {
      return String.fromCharCode('0x' + p1);
    });
  
    return window.btoa(utf8Bytes);
}

// 判断字符串是否是base64字符串
export function isBase64(str) {
  try {
    // 可以匹配任何 MIME 类型（例如 "image/png"、"audio/mp3"、"video/mp4" 等等）并去除，atob()解析去掉MIME类型前缀 Base64 编码字符串
    const base64Data = str.replace(/^data:\w+\/\w+;base64,/, ""); 
    return window.btoa(window.atob(base64Data)) === base64Data;
  } catch (err) {
    return false;
  }
}

// 密码 加密
export const pwdEncrypt = (data) =>{
    if (!data) return data;
    try{
      let _data = sm4.encrypt(data, process.env.REACT_APP_NET_PWD_KEY, {padding: "pkcs#5"})
      return _data;
    }catch(e){ // 如果加密失败，则返回原数据
      console.log("pwdEncrypt", e, data)
      return data
    }
  }
  
  // 密码 解密
  export const pwdDecrypt = (data) =>{
    if (!data) return data;
    try{
      // SM4解密可能出现多种异常情况:
      // 1. 抛出异常 - 由catch捕获
      // 2. 返回空字符串 - 需要检查
      // 3. 返回明显不合理的结果 - 需要额外判断
      const _data = sm4.decrypt(data, process.env.REACT_APP_NET_PWD_KEY, {padding: "pkcs#5"});
      // 检查解密结果是否为空或异常（如果原始数据不是SM4加密的，可能返回空串或无意义结果）
      if (_data == '' || _data === undefined || _data === null) {
        return data; // 返回原始数据
      }
      // 解密成功，返回解密结果
      return _data;
    }catch(e){// 如果解密失败，则返回原数据
      console.log("pwdDecrypt", e, data)
      return data
    }
  }
  