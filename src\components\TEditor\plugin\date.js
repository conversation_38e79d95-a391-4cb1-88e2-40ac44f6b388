import Pikaday from 'pikaday';
import "pikaday/css/pikaday.css";
import "./pickaday.css";
import moment from 'moment';
/**
 * @description 时间组件 
 */
 (function (global, factory) {
  factory(require('froala-editor'))
}(this, (function (FE) { 'use strict';

  FE = FE && FE.hasOwnProperty('default') ? FE['default'] : FE;

  // Define the plugin.  
  // The editor parameter is the current instance.

  FE.PLUGINS.pickaday = function (editor) {
    var $ = editor.$; 

    function showDatepicker(e){
      var $elem = $(e.currentTarget).find("input")
      var _date = $(e.currentTarget).data("pickaday-date");
      var picker = new Pikaday(
        {
            field: $elem[0],
            trigger: e.target,
            firstDay: 1,
            yearRange: [1990,2100],
            keyboardInput: false,
            format:"yyyy-MM-DD",
            setDefaultDate: true,
            defaultDate: new Date(_date),
            onSelect: function(date) {
              $(e.currentTarget).find(".fr-pickaday-date")?.html(picker.toString())
              $(e.currentTarget).attr("data-pickaday-date", picker.toString())
            },
            onClose: function(){
              picker?.destroy()
            }
        });
      picker.show()
    }

    function _inPickaday() {
      if (!editor.selection.isCollapsed()) return false;
      var s_el = editor.selection.element();
      var e_el = editor.selection.endElement();
      if (s_el && editor.node.hasClass(s_el, 'fr-pickaday')) return s_el;
      if (e_el && editor.node.hasClass(e_el, 'fr-pickaday')) return e_el;
      var range = editor.selection.ranges(0);
      var container = range.startContainer;

      if (container.nodeType == Node.ELEMENT_NODE) {
        if (container.childNodes.length > 0 && range.startOffset > 0) {
          var node = container.childNodes[range.startOffset - 1];

          if (editor.node.hasClass(node, 'fr-pickaday')) {
            return node;
          }
        }
      }

      return false;
    }

    function addDate(){
      var el = _inPickaday();

      var range = editor.selection.ranges(0);

      var date_format = moment(new Date()).format("YYYY-MM-DD");

      if (!el) {

        editor.html.insert("<span data-pickaday-date=\""
          .concat(date_format,"\" contenteditable=\"false\" class=\"fr-pickaday fr-deletable\"")
          .concat(">","<span contenteditable=\"false\" style=\"margin: 0px 2px;\">")
          .concat("<span contenteditable=\"false\" class=\"fr-pickaday-content fr-pickaday-font\">")
          .concat("<span class=\"fr-pickaday-date\">",date_format,"</span>")
          .concat("<input class=\"fr-pickaday-input\" style=\"display:none\"/>")
          .concat("</span>","</span>","</span>","&nbsp;"),true)
      } else {
        if (range.startOffset === 0 && editor.selection.element() === el) {
          $(el).before(FE.MARKERS + FE.INVISIBLE_SPACE);
        } else if (range.startOffset > 0 && editor.selection.element() === el && range.commonAncestorContainer.parentNode.classList.contains('fr-pickaday')) {
          // Inside emoticon move out side of it.
          $(el).after(FE.INVISIBLE_SPACE + FE.MARKERS);
        }

        editor.selection.restore();
        editor.html.insert("<span data-pickaday-date=\""
        .concat(date_format,"\" contenteditable=\"false\" class=\"fr-pickaday fr-deletable\"")
        .concat(">","<span contenteditable=\"false\" style=\"margin: 0px 2px;\">")
        .concat("<span contenteditable=\"false\" class=\"fr-pickaday-content fr-pickaday-font\">")
        .concat("<span class=\"fr-pickaday-date\">",date_format,"</span>")
        .concat("<input class=\"fr-pickaday-input\" style=\"display:none\"/>")
        .concat("</span>","</span>","</span>","&nbsp;").concat(FE.MARKERS),true)        
      }
    }

    function _init() {
      // editor.events.on('keydown', _checkCharNumber, true);
      editor.events.$on(editor.$el, 'mousedown', '.fr-pickaday', function (e) {
        showDatepicker(e);
      })
    }

    return {
      _init: _init,
      insert: addDate,
    };
  };

  FE.DefineIcon('pickaday', {
    NAME: 'pickaday',
    PATH: `M 20.625 4.3125 L 16.6875 4.3125 L 16.6875 2.8125 C 16.6875 2.710938 
          16.601562 2.625 16.5 2.625 L 15.1875 2.625 C 15.085938 2.625 15 2.710938 
          15 2.8125 L 15 4.3125 L 9 4.3125 L 9 2.8125 C 9 2.710938 8.914062 2.625 
          8.8125 2.625 L 7.5 2.625 C 7.398438 2.625 7.3125 2.710938 7.3125 2.8125 
          L 7.3125 4.3125 L 3.375 4.3125 C 2.960938 4.3125 2.625 4.648438 2.625 5.0625 
          L 2.625 20.625 C 2.625 21.039062 2.960938 21.375 3.375 21.375 L 20.625 21.375 
          C 21.039062 21.375 21.375 21.039062 21.375 20.625 L 21.375 5.0625 C 21.375 
          4.648438 21.039062 4.3125 20.625 4.3125 Z M 19.6875 19.6875 L 4.3125 19.6875 
          L 4.3125 10.78125 L 19.6875 10.78125 Z M 4.3125 9.1875 L 4.3125 6 
          L 7.3125 6 L 7.3125 7.125 C 7.3125 7.226562 7.398438 7.3125 7.5 7.3125 
          L 8.8125 7.3125 C 8.914062 7.3125 9 7.226562 9 7.125 L 9 6 L 15 6 L 15 7.125 
          C 15 7.226562 15.085938 7.3125 15.1875 7.3125 L 16.5 7.3125 C 16.601562 
          7.3125 16.6875 7.226562 16.6875 7.125 L 16.6875 6 L 19.6875 6 L 19.6875 
          9.1875 Z M 4.3125 9.1875`
  });
  FE.RegisterCommand('pickaday', {
    icon: 'pickaday',
    title: '插入时间',
    type: 'button',
    undo: false,
    focus: true,
    refreshAfterCallback: false,
    callback: function callback() {
      this.pickaday.insert();
    },
    // refresh:function(){
    //   // console.log("我正在点击！！！！！！！！！")
    // },
    plugin: 'pickaday'
  });
})));