import React, { useEffect } from "react";
import {
  createHash<PERSON><PERSON>er, 
  RouterProvider, 
  useSearchParams,
  useLocation,
  Outlet,
  matchPath
} from 'react-router-dom';
import {ConfigProvider,message} from "antd";
import zhCN from 'antd/lib/locale/zh_CN';
import "antd/dist/antd.css";
import "./assets/iconfont.scss";
import './App.scss';
import routerConfig from "./router/Route";
import { getMatchRoutes } from "@/router/usePathPattern";
import Loading from './components/Loading';
import { globalUtil } from "./utils/globalUtil";
import {getRouters} from "./router/RouterMap";
import "slick-carousel/slick/slick.css"; 
import "slick-carousel/slick/slick-theme.css";
import AppAuthContextProvider, { useAuthContext } from "@/context/AppAuthContextProvider";
import { isMobile, isPad } from "./utils/toolUtil";
import 'moment/locale/zh-cn';

function Config(){
  const location = useLocation();
  const [search,_] = useSearchParams();
  const [msgApi, contextHolder] = message.useMessage();
  const authActions = useAuthContext();

  useEffect(() => {
    // 如果当前是移动手机设备直接跳转移动端网页
    if(isMobile() && !isPad()){
      window.location.href = window.location.protocol+"//"+window.location.host + "/app" + "/#" + location.pathname
    }
    
    globalUtil.setMsgApi(msgApi)
    // 判定当前是否有?bypass-token
    if(location.search.indexOf("bypass-token") != -1){
      let bypass_token = search.get("bypass-token")
      localStorage.setItem("bypass-token", bypass_token)
    }  
    authActions.loadUserInfo()
  },[])

  useEffect(()=>{
    try {
      let routes = getMatchRoutes(location) || [];
      let route = routes.reverse().find(item => !!item.route.meta)?.route;
      console.log("document.title", route);
      if(route?.meta){
        document.title = `${route.meta.title}-云团队`;
      }
    } catch (error) {
      console.error(error);
      document.title = `云团队`;
    }

  },[location])

  return <React.Fragment>
    {contextHolder}
    <Outlet/>
  </React.Fragment>
}

const router = createHashRouter(getRouters([{path:"/", element: Config, children: routerConfig}]));

function App() {
  return <React.Suspense fallback={<Loading/>}>
    <ConfigProvider locale={zhCN}>
      <AppAuthContextProvider>
        <RouterProvider router={router} fallbackElement={<Loading/>}/>
      </AppAuthContextProvider>
    </ConfigProvider>
  </React.Suspense>
}

export default App;
