import React, { useEffect, useState } from "react";
import {Box, Typography, Stack,} from "@mui/material";
import "./Coop_Part_2_GiveAll.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../utils/commonUtils";

//倾其所有
export default function GiveEverything(props) {
  const { data } = props;
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  },[data?.widgetList])
  return (
    <>
      {/* 倾其所有-start */}
      <Box className="give-everything" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography variant="h5" sx={{ fontSize: "34px" }}>{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
        {!!filterWidgetList(moduleData, 2)[0]?.value && <Typography sx={{ mt: '10px', color: '#666' }} variant="body2">{filterWidgetList(moduleData, 2)[0]?.value}</Typography>}
        {filterWidgetList(moduleData, 6).length > 0 && <Box className="give-everything-stack">
          <Stack
          sx={{ flexWrap: "wrap", justifyContent: 'center', mt: "40px" }}
          direction={{ xs: "column", sm: "row" }}
          spacing={3}
          >
            {filterWidgetList(moduleData, 6).map((item, index) => (
              <Box className="give-everything-stack-li" key={item.widgetId}>
                <Box className="give-everything-stack-li-img">
                  <img height="100%" src={filterWidgetList((item?.children || []), 3)[0]?.value}/>
                </Box>
                <Box className="give-everything-stack-li-body">
                  <Typography sx={{ mt: '10px', fontWeight: 'bold' }} variant="subtitle1">{filterWidgetList((item?.children || []), 1)[0]?.value}</Typography>
                  <Typography className="fontsize-12" sx={{ color: '#999' }} variant="body2">{filterWidgetList((item?.children || []), 2)[0]?.value}</Typography>
                </Box>
              </Box>
            ))}
          </Stack>
        </Box>}
      </Box>
      {/* 倾其所有-end */}
    </>
  );
}
