.versionHistory {
    overflow: hidden;
    &-title {
        padding: 20px 40px;
        margin: auto;
        width: 1260px;
        font-size: 18px;
        font-weight: bold;
    }
    &-contentBox {
        padding: 0 20px;
        margin: 0 auto;
        width: 1260px;
        &-List {
            .ant-spin-container {
                .ant-list-items {
                    .ant-list-item {
                        padding: 10px 20px;
                        align-items: baseline;
                    }
                }
            }
        }
        &-Pagination {
            margin: 20px 0;
            text-align: center;
        }
    }
}

.versionHistory-content-pagination {
    .MuiPagination-ul {
        .Mui-selected {
            color: white;
            background-color: #0077f2;
        }
        .Mui-selected:hover {
            color: white;
            background-color: #0077f2;
        }
    }
}

.page-input {
    margin: 0 10px;
    width: 32px;
    height: 32px;
    text-align: center;
    border: 1px solid rgba(0,0,0, 0.23);
    border-radius: 5px;
    .MuiInputBase-input {
        text-align: center;
        color: #999;
    }
}
.page-input::before, .page-input::after {
    display: none;
}

.versionHistory-TopMenuBar-box {
    position: fixed;
    top: 0;
    margin: auto;
    z-index: 1;
    width: 100vw;
    left: 0;
    padding: 0 20px;
    background-color: white;
    box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
}

.versionHistory-bottom {
    position: fixed;
    left: 50%;
    bottom: 50px;
    padding: 10px;
    transform: translate(-50%, 0);
    background-color: white;
}

// 版本历史弹窗
// 搜索-右弹抽屉
.versionDetail-drawer {
    .ant-drawer-content-wrapper .ant-drawer-content .ant-drawer-wrapper-body {
        .ant-drawer-header {
            .ant-drawer-header-title {
                flex-direction: row-reverse;
            }
        }
        .ant-drawer-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .previous-doc {
                width: 48%;
                text-align: right;
            }
            .next-doc {
                width: 48%;
                text-align: left;
            }
        }
    }
}
.versionDetailDrawer {
    .versionDetailDrawer-heard {
        .versionDetailDrawer-heard-information {
            margin-top: 10px;
            .versionDetailDrawer-heard-stack-li {
                display: flex;
                align-items: center;
                .MuiTypography-root:last-of-type {
                    margin-right: 6px;
                }
            }
            .versionDetailDrawer-heard-stack-li:not(:first-child) {
                color: #999;
            }
        }
    }
    .versionDetailDrawer-body {
        // padding: 20px 40px;
        margin-top: 10px;
    }
}