import React from "react";
import { getIconByNodeType } from "@/utils/TsbConfig";
import {Iconkuaijiejiaobiao} from "@/components/IconUtil";

function AppNodeResourceIcon({nodeType,isShortcut=false,...props}) {
  let iconClass = getIconByNodeType(nodeType);
  if(!iconClass) return <></>
  return <span {...props} className={["iconfont",props.className || ""].join(" ")}>
    <span style={{display:"inline",position:"relative"}}>
    <span {...props} className={[iconClass].join(" ")} style={{fontSize:"inherit",color:"inherit",lineHeight:"inherit"}}/>
      {isShortcut?<Iconkuaijiejiaobiao style={{position: "absolute",bottom: "0", left: "0"}}/>:<></>}
    </span>
  </span>
}

export default React.memo(AppNodeResourceIcon)