import * as httpBase from "../utils/httpBase";
import api_name from "./api_name";
const {api_tms_team} = httpBase;

/* 登录 */
// team-521 get_security_code 获取手机/邮箱验证码
// https://confluence.ficent.com/pages/viewpage.action?pageId=78379037
export const team_521_get_security_code = (data) => httpBase.get(api_tms_team+api_name.team_521_get_security_code,data)
// team-614 get_wx_login_param 获取微信登录参数(用于二维码生成)
// https://confluence.ficent.com/pages/viewpage.action?pageId=98409735
// export const team_614_get_wx_login_param = (data) => httpBase.get(api_tms_team+api_name.team_614_get_wx_login_param,data)
// team-616 get_wx_login_state 获取微信登录(扫码)状态
// https://confluence.ficent.com/pages/viewpage.action?pageId=98409737
// export const team_616_get_wx_login_state = (data) => httpBase.get(api_tms_team+api_name.team_616_get_wx_login_state,data,{},false)
// team-618 user_login 手机登录（密码或验证码）
// https://confluence.ficent.com/pages/viewpage.action?pageId=98409741
export const team_618_user_login = (data) => httpBase.post(api_tms_team+api_name.team_618_user_login,data)
// team-628 get_login_register_method 获取登录/注册平台支持的方式
// https://confluence.ficent.com/pages/viewpage.action?pageId=98409747
export const team_628_get_login_register_method = (data) => httpBase.get(api_tms_team+api_name.team_628_get_login_register_method,data)

// 
export const team_649_mp_create_qrcode = (data) => httpBase.post(api_tms_team+api_name.team_649_mp_create_qrcode,data)
// 
export const team_650_mp_get_wx_notice_state = (data) => httpBase.get(api_tms_team+api_name.team_650_mp_get_wx_notice_state,data,undefined,false,{ignore: true})