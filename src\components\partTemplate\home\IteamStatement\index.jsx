import React, { useEffect, useState } from "react";
import { <PERSON>, Typography, Button } from "@mui/material";
import "./index.scss";
import bannerBgImage from "../../../../assets/images/banner_bg.png";
import {
  isArrayUtils,
  filterWidgetList,
  formatTreeData,
} from "../../../../utils/commonUtils";

export default function IteamStatement({ data }) {
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData]);
    }
  }, [data?.widgetList]);

  return moduleData.length > 0 ? (
    <div
      className="IteamStatement"
      style={{
        padding: "50px 100px",
        background: `url(${bannerBgImage}) no-repeat top left`,
        backgroundSize: "100% 100%",
        // background: filterWidgetList(moduleData, 12)[0]?.value || "transparent",
      }}
    >
      <div className="IteamStatement-title1">
        {filterWidgetList(moduleData, 1)[0]?.value}
      </div>
      <div className="IteamStatement-title2">
        {filterWidgetList(moduleData, 2)[0]?.value}
      </div>
    </div>
  ) : (
    <></>
  );
}
