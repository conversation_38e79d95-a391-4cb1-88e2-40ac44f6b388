// 我们的用户
.our-users {
    padding: 40px 0;
    // width: 1260px;
    margin: auto;
    &-title {
        text-align: center;
    }
    &-content {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 40px;
        &-swiper {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            width: 1260px;
            .our-users-content-swiper-li {
                overflow: hidden;
                width: 400px !important;
                border-radius: 10px;
                box-shadow: 0 4px 4px 0 rgb(77 77 77 / 1%), 0 2px 8px 0 rgb(77 77 77 / 7%);
                &-topbar {
                    width: 100%;
                    height: 10px;
                    background-color: #0077f2;
                }
                &-imagebox {
                    padding: 20px;
                    &-image {
                        height: 200px;
                        background-color: #f2f2f2;
                        border-radius: 14px;
                    }
                }
                &-name {
                    padding: 10px 20px 0;
                    font-size: 20px;
                    font-weight: 500;
                }
                &-introduce {
                    padding: 0 20px 20px;
                }
            }
            .our-users-content-swiper-li:not(:last-child) {
                margin-right: 20px;
            }
            .our-users-content-swiper-li:nth-child(2n) {
                .our-users-content-swiper-li-topbar {
                    width: 100%;
                    height: 10px;
                    background-color: #2bcd6a;
                }
            }
        }
    }
}

.our-users-content-slider {
    width: 100%;
}