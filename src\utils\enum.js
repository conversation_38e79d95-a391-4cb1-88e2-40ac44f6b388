
/*1 数据字典相关*/
export const eConsoleNodeType = {
    NodeType_1_Attribute: 1,

};

export const eConsolePropId = {
    Prop_1_attr_type:          1,  //字段类型
    Prop_2_data_type:          2,  //数据类型
    Prop_3_width:              3,  //宽度
    Prop_4_case:               4,  //大小写
    Prop_5_default_value:      5,  //默认值
    Prop_6_max_len:            6,  //最大长度
    Prop_7_min_len:            7,  //最小长度
    Prop_9_visible:            9, // 是否启用
    Prop_11_attr_class:       11, //属性对应的类
    Prop_12_selection:        12, //下拉   (ID_OPTIONAL_ENTRY=12)
    Prop_13_format:           13, // 格式
    Prop_14_ui_control:       14, // 控件类型 TextBox, MultiTextBox, ListBox, MultiListBox, Date
    Prop_30_name:             30, //attr节点名称，默认为''
    Prop_31_attrgrp_id:       31, //900的子
    Prop_59_queryable:        59, //高级查询可查询
    Prop_60_modifiable:       60, //1:可修改, 0:只读
    Prop_61_query_relop_list: 61, //查询时使用到的关联操作符列表(大于，等于, 非空，为空等)
    Prop_233_readable:        233, //是否可以查看，当设置为false时，不会加载到attrs下
    Prop_236_table:           236, //表名
    Prop_237_column:          237, //字段名
    Prop_238_table_alias:     238, //表昵称
    Prop_239_required:        239, //必填
    Prop_255_ext_attr_flg:    255, //是否扩展attr  2: 是历史操作字段
};

export const eConsoleAttrType = {
    AttrType_1_SingleLineTextBox:           1, //单行输入
    AttrType_4_SingleSelect:                4, //单行下拉
    AttrType_6_ModifiableSelect:            6, //下拉可添加
    //AttrType_7_MultipleSelect:              7, //下拉可多选(对话框) 废弃不用
};

export const eConsoleDataType = {
    DataType_1_String:     1, //字符串
    DataType_2_Number:     2, //数值
    //DataType_4_Date:       4, //日期 废弃不用
};

// UI显示格式
export const eConsoleUiControl = {
    TextBox: "TextBox",       //单行文本
    MultiTextBox: "MultiTextBox",  //多行文本
    RichTextBox: "RichTextBox",  //多行文本
    ListBox: "ListBox",       //下拉单选
    MultiListBox: "MultiListBox",  //下拉多选
    Date: "Date",           //日期
    CascadeListBox: "CascadeListBox",  //系统模块
    Image: "Image", //页面截图
    KeywordSearch: "KeywordSearch", // 关键词搜素
    globalSearch: "globalSearch", // 全局搜索
};

export const eConsoleNodeId = {
    //api-mock
    Nid_11801_Mock_ProjectName:           11801,
    Nid_11802_Mock_ProjectType:           11802,
    Nid_11803_Mock_ProjectPrefix:           11803,
    Nid_11804_Mock_ProjectAutoNoType:           11804,
    Nid_11805_Mock_ProjectAutoNo:           11805,
    Nid_11806_Mock_ApiNameShowType:           11806,
    Nid_11807_Mock_ProjectCreator:           11807,
    Nid_11808_Mock_ProjectCreateDate:           11808,
    Nid_11809_Mock_ProjectModifyDate:           11809,
    Nid_11821_Mock_ApiName:           11821,
    Nid_11822_Mock_ApiDescription:           11821,
    Nid_11825_Mock_ApiCreator:           11821,
    Nid_11826_Mock_ApiCreateDate:           11821,
    Nid_11827_Mock_ApiModifyDate:           11821,
    //issue
    Nid_11101_Issue_IssueNo:              11101,
    Nid_11102_Issue_Title:           11102,
    Nid_11103_Issue_Description:     11103,
    Nid_11104_Issue_Assignee:           11104,
    Nid_11105_Issue_Creator:              11105,
    Nid_11106_Issue_Reporter:             11106,
    Nid_11107_Issue_Priority:             11107,
    Nid_11108_Issue_SevereType:           11108,
    Nid_11109_Issue_Type:               11109,
    Nid_11110_Issue_Status:             11110,
    Nid_11111_Issue_ExpectedStartDate:   11111,
    Nid_11112_Issue_ExpectedFinishDate:     11112,
    Nid_11113_Issue_PlanStartDate:       11113,
    Nid_11114_Issue_PlanFinishDate:         11114,
    Nid_11115_Issue_ActualStartDate:     11115,
    Nid_11116_Issue_ActualFinishDate:       11116,
    Nid_11117_Issue_CurrentFinishRatio:       11117,
    Nid_11118_Issue_CreateDate:          11118,
    Nid_11119_Issue_UpdateDate:          11119,
    //report
    Nid_11501_MailRpt_Subject:           11501,
    Nid_11502_MailRpt_:                  11502,
    Nid_11503_MailRpt_:                  11503,
    Nid_11504_MailRpt_:                  11504,
    Nid_11505_MailRpt_:                  11505,
    Nid_11506_MailRpt_:                  11506,
    Nid_11507_MailRpt_:                  11507,
    Nid_11508_MailRpt_:                  11508,
    Nid_11509_MailRpt_:                  11509,
    Nid_11510_MailRpt_:                  11510,
    Nid_11511_MailRpt_:                  11511,
    Nid_11512_MailRpt_:                  11512,
    Nid_11513_MailRpt_:                  11513,
    Nid_11514_MailRpt_:                  11514,
    Nid_11515_MailRpt_:                  11515,
    Nid_11516_MailRpt_:                  11516,
    Nid_11517_MailRpt_:                  11517,
    Nid_11518_MailRpt_:                  11518,
    Nid_11519_MailRpt_:                  11519,
    Nid_11520_MailRpt_:                  11520,
    Nid_11521_MailRpt_:                  11521,
    Nid_11522_MailRpt_:                  11522,
    Nid_11523_MailRpt_:                  11523,
    Nid_11601_MailRpt_:                  11601,
    Nid_11602_MailRpt_:                  11602,
    Nid_11603_MailRpt_:                  11603,
    Nid_11604_MailRpt_:                  11604,
    Nid_11605_MailRpt_:                  11605,
    Nid_11606_MailRpt_:                  11606,
    Nid_11607_MailRpt_:                  11607,
    Nid_11608_MailRpt_:                  11608,
    Nid_11609_MailRpt_:                  11609,
    Nid_11610_MailRpt_:                  11610,
    Nid_11611_MailRpt_:                  11611,
    Nid_11612_MailRpt_:                  11612,
    // workorder
    Nid_11104_WorkOrder_Manager:         11104, // 负责人字段id
    Nid_11110_WorkOrder_IssueStatus:     11110, // 状态字段id
    Nid_11105_WorkOrder_Creater:         11105, // 创建人字段id
    Nid_11202_WorkOrder_IssueSolveGrade: 11202, // 问题解决评分
    Nid_11203_WorkOrder_IssueAttitudeGrade:     11203, // 服务态度评分
    Nid_11246_WorkOrder_SystemModule:    11246, // 系统模块 selectionLid = 1930
    Nid_11233_WorkOrder_VisitTime:       11233, // 回访时段
    Nid_11204_WorkOrder_PageURL:         11204, // 页面URL
    Nid_11218_WorkOrder_ScreenShort:     11218, // 页面截图
    Nid_11234_WorkOrder_WorkOrderStatus: 11234, // 工单状态
    Nid_11236_WorkOrder_OpenType:        11236, // 开放类型
};

export const eSelectionListId = {
    Selection_Minus_701_Space_Users: -701,
    Selection_441_Case:  441, //大小写下拉
    Selection_451_YesNo:  451, //是否
    Selection_727_DateCompare:  727, //日期比较，等于/不等于/大于/小于等于/空/非空
    Selection_729_DateFormat:   729, //年月日/年月日时分秒/时分秒
    Selection_733_IssueStatus:  733, //问题跟踪状态
    Selection_741_TaskVarKey: 741, //abcd,x_i,y_i,z_i
    Selection_742_ObjType: 742,
    Selection_1905_Format: 1905,
    Selection_1906_IssuePriority: 1906,
    Selection_1907_IssueSeverLevel: 1907,
    Selection_1908_IssueType: 1908,
    Selection_19010_ObjType: 1910, //资源类型
    Selection_1911_Rel_ObjType: 1911, //可选择的资源类型下拉(在kpi任务中选择)
    Selection_1921_Res_ObjType: 1921, //可选择的资源类型下拉(通用)
    Selection_1949_Res_ObjType: 1949, //可选择的资源类型下拉(MessageModal 消息中心模块)
    Selection_1922_CharsetType: 1922,
    Selection_1928_MimeType: 1928,
    Selection_1939_Icon: 1939, //系统图标
    Selection_1948_Task_Icon: 1948, //标记设置图标
};

export const eDynamicConditionNodeId = 99990;

// 连接符(relational_operation 关联操作符)
export const CRITERIA_RELOP_TYPE = {
  op_place : "0",
  op_and : "1",
  op_or : "2",
}

// tmsbug-2128： 连接符，缺省项 斜杠 改为 空字符串，不用显式的显示一个斜杠，
export const CRITERIA_RELOP_TYPE_LIST = [
  { key: CRITERIA_RELOP_TYPE.op_place, value: CRITERIA_RELOP_TYPE.op_place, label: " ",  labelEN: " " },
  { key: CRITERIA_RELOP_TYPE.op_and, value: CRITERIA_RELOP_TYPE.op_and, label: "且", labelEN: "and" },
  { key: CRITERIA_RELOP_TYPE.op_or, value: CRITERIA_RELOP_TYPE.op_or, label: "或", labelEN: "or" },
];

// 动态条件nodeId
export const eDynamicCondition =  {
  nodeId: eDynamicConditionNodeId,
  nodeName: "动态条件",
  nodeType: 1,
  dataType: eConsoleUiControl.TextBox, //类型
  checked: true, //默认全部选中
  queryableFlg: true, //是否新建/编辑UI可操作字段
  list: [],         // TextBox、Date、MultiTextBox为手动选择项,没有下拉列表
  relopList: [],    //连接符
};

// kpi 初始值
export const eKpiInitCriteriaList = [
  {
      leftBracket: "",        //左括号(
      attrNid: eDynamicConditionNodeId,            //字段
      relOpType: undefined,   //条件
      attrValue: "%创建人% in (%任务参与成员%)",   //值
      rightBracket: "",       //右括号）
      logicalOpType: CRITERIA_RELOP_TYPE.op_and,      //连接符
      dataType: eConsoleUiControl.TextBox, //字段类型:默认文本
      attrValueList: [],                    //值Select的List
      isDisableDelete: false, //是否可删除， 否， TODO: 没有保存接口，编辑回显时无法保证是否可删除
  }, {
    leftBracket: "",        //左括号(
    attrNid: eDynamicConditionNodeId,            //字段
    relOpType: undefined,   //条件
    attrValue: "%创建时间% between %任务开始日期% and %任务结束日期%",   //值
    rightBracket: "",       //右括号）
    logicalOpType: CRITERIA_RELOP_TYPE.op_place,      //连接符
    dataType: eConsoleUiControl.TextBox, //字段类型:默认文本
    attrValueList: [],                    // 值Select的List
    isDisableDelete: false, //是否可删除, 否
  }
];

/*issue相关*/
export const eIssueViewMode = {
    ViewMode_1_ListDetail: "",    //列表及详情
    ViewMode_2_ListOnly: "list",     //长列表
    ViewMode_3_Kanban: "kanban",       //看板
    ViewMode_4_Phase: 4         //阶段
}

export const eIssueOpType = {
    OpType_0_CreateIssue: 0,
    OpType_1_UpdateIssue: 1,
    OpType_2_Comment: 2,
    OpType_3_ObjRel: 3,
    OpType_4_UploadAttachment: 4
};

export const eObjSocialOpType = {
    OpType_4_Like: 4, //点赞
    OpType_5_Cancel_Like: 5, //取消点赞
    OpType_6_Dislike: 6, //倒赞
    OpType_7_Cancel_Dislike: 7, // 取消倒赞
    OpType_8_Watch: 8, //关注(follow用于人, watch用于关注资源)
    OpType_9_Cancel_Watch: 9, //取消关注
}



/*kpi相关*/
export const eTaskVarType = {
    Type_1_FixedVal: 1,   //固定数值
    Type_2_Query: 2,   //动态统计
    Type_3_Formula: 3,  //计算公式
    Type_4_Accumulation: 4  //累计变量
};

export const eTaskGenTimeType = {
    Type_1_Immediate: 1,
    Type_2_FixedTime: 2,
    Type_3_AheadOf: 3
};

export const eTaskBrkpntScoreType = {
    Type_1_FixedVal: 1,
    Type_2_Formula: 2
};

export const eTaskStatus = {
    Status_0_NotStart: 0,
    Status_1_OnProgress: 1,
    Status_2_Ended: 2
}

export const eTaskGenAheadOfTimeUnit = {
    Unit_1_Day: 1,
    Unit_2_Hour: 2
}

export const eDbSrvFlavorType = {
    DbType_501_Mysql: 501,
    DbType_502_Pgsql: 502,
    DbType_503_Mssql: 503
}

export const ePagination = {
    PageSize_5: 5,
    PageSize_10: 10,
    PageSize_20: 20,
    PageSize_30: 30,
}

export const eEditingMode = {
    Creating_0: 0,
    Modifying_1: 1
}



// 接口返回状态码
export const eResultCode = {
  Result_Not_Read : 0,
  Result_Ok : 200,
  Result_No: 202,
  Result_Not_Found: 203,
  Result_500: 500,
}

// 页面错误码
export const ePageCode = {
  PageCode_Not_Found: 203, //资源不存在
  PageCode_Error: 204, //服务器出错了
  PageCode_Not_Read: 205, //页面无权限
}

// 默认宽度
export const eTreeWidth = 280;

export const eMaxBadgeNum = 99; // badge最大值

export const eResTypeAll = { propType: "-1", propValue: "全部" }; // 全部类型

export const eIssueEmptySelectionId = "-2";// issue搜索条件空selectionId

export const dateFormat = {
  1:'%Y',
  2:'%Y-%m',
  3:'%Y-%m-%d',
  4:'%Y-%m-%d',
}

export const eDocEditOpType = {
  /**
  * 1:新建子节点文档
  * 2:新建兄弟节点文档
  * 3:文档编辑
  */
    CREAT_SUB_DOC: "1",
    CREAT_BROTHER_DOC: "2",
    EDIT_DOC: "3",
}

// 社交操作类型 4点赞  5取消点赞  6 倒赞  7:取消倒赞  8关注，9取消关注
export const eSocialOpType = {
    op_type_4_like: "4",
    op_type_5_cancel_like: "5",
    op_type_6_dislike: "6",
    op_type_7_cancel_dislike: "7",
    op_type_8_follow: "8",
    op_type_9_cancel_follow: "9",
}

// 通用，1:是，0:否
export const eEnableFlg = {
    enable: 1,
    disable: 0
}

// 业务操作类型: 新建 add, 编辑: edit, 删除 delete
export const eOpType = {
    add: "add",
    edit: "edit",
    delete: "delete",
}

// ContextBoard 操作类型
export const eContextBoardOpType = {
  ctxType:"ctxType",
  createType:"createType",
}

// 是否显示历史操作字段
export const eSearchFlg = {
  isNotSearch : 0, // issue模块不使用
  isSearch : 1, // 高级搜索和Kpi模块使用
}

// 输入框输入最大长度
export const eInputMaxLength = {
  twenty: 20,
  fifty: 50,
  hundred: 100,
}


export const eCreateType = {
  
}

// inpuNumber输入框值
export const eInputNumber = {
  min: 1,
  step: 10,
  max: 100
}

// 防抖时间
export const eDebounceTime = {
  twoHundred: 200,
  fiveHundred: 500,
}

//api编号是否启用
export const eApiNoGenerateType = {
    type_1_auto: 1,   //自动
    type_2_manual: 2  //手动(即:不启用)
}

// 1.接口编号+标题
// 2.标题
export const eApiNameShowType = {
    type_1_apiNo_apiName: 1,
    //type_2_apiNo: 2,
    //type_3_apiName: 3
    type_2_apiName: 2,  //标题
}

// 视图模式
export const eViewMode = {
  ViewMode_List : "1",
  ViewMode_Thumb : "2",
}

export const eDashBoardMaxLength = 3; // 仪表盘最大数量

// 资源管理操作模式
export const eTreeOpType = {
  opLeftSider: "opLeftSider", // 左侧树
  opRightContent: "opRightContent", // 右侧资源管理器
  opDesktop: "opDesktop",   // 桌面
}

// 仪表板缩略图文件名称
export const fileObjName = "dashboard.png";

// 报表类型
export const eReportType = {
  type_0_team: 0, // 0:团队
  type_1_personal: 1, // 1:个人
  type_2_all: 2, // 2:全部
}

// 搜索是否是英文
export const eEnTag = {
  isEn: 1,
  isNotEn: 0,
}

// 搜索是否是kpi
export const eKpiTag = {
  isKpi: 1,   // Kpi
  isNotKpi: 0,  // 高级搜索
}

// Row的gutter
export const eGutter = [16, 15];

// 工作报告自定义表单数据
export const eMailRptProps ={
  eRptType: 11514, // 报告类型
  eReviewResultType: 11517 // 报告评审
}

export const eCtxOptionType = {
  eGroup: "group", // 分组
  eSingle: "single", // 单个
}

export const eApiMimeType = {
   mime_1_form_data: 1,
   mime_2_x_www_form_urlencoded : 2,
   mime_3_json: 3,
   mime_4_xml: 4
}

//以1，2，3作为特殊的3个参数虚节点的key值
export const eApiVarType = {
  varType_1_header: 1,
  varType_2_query_param: 2,
  varType_3_body: 3,
  //
  varType_11_responseHeader: 11,
  varType_12_responseCookie: 12,
  varType_13_responseBody: 13
}

export const eApiCharsetType = {
  charset_1_ascii: 1,
  charset_2_unicode: 2,
  charset_3_uft8: 3,
  charset_4_uft16: 4,
  charset_5_gb2312: 5,
  charset_6_gbk: 6
}

export const eApiRequestMatchingType = {
  matchingType_1_self: 1,   //正好匹配自己，无需提示
  matchingType_2_another: 2, //匹配别的一个Api，需提示是否切换接口
  matchingType_3_none: 3     //没有匹配任何Api，需提示是否创建新接口
}

export const eApiCallType = {
  callType_1_Http: 1,
  callType_2_cURL: 2,
  callType_3_Mock: 3,
  callType_4_Proxy: 4
}

// export const eApiVarTypeString = {
//   HEADER: 'requestHeader',
//   QUERY: 'queryParameter',
//   BODY: 'requestBody',
// }

// 报告操作
export const eRptOpType = {
  OpType_0_CreateRpt: 0, // 新建报告
  OpType_1_UpdateRpt: 1, // 修改报告
  OpType_2_Review: 2,    // 评审报告
};

// team_016_update_node_op 操作类型
export const eUpdateNodeOp ={
  textStrike :"textStrike",
  textColorType :"textColorType",
  flgIconType :"flgIconType",
}

// 空间树opType
export const eSpaceTreeOpType = {
  menu : "menu", // 主空间，不包含子空间和团队
  modify : "modify", 
  task : "task", // 团队，主空间以及子空间
}

// 分类设置上移下移
export const eMoveType = {
  eMoveUp : "1",   // 上移
  eMoveDown : "2",   // 下移
}

export const PLACEHOLDER_BLANK = "\u00A0";
export const eNavId = "navId"; // tms系统提交工单Id
export const eFileObjId = "1"; // 默认上传文件objId

// 工单用的统计类型 
export const eCountType = {
  eTotal: -1,       // -1：全部 (调用getIssueList接口,不传条件，默认查询全部)
  eTotalCreate: 1,  // 1：我创建的
  ePrincipal: 2,    // 2：我负责的
  eProcessing: 3,   // 3: 进行中的
  eWatch:4,         // 4：我关注的
}

// 日期格式
export const eDateFormatType = {
   eDateFormatDate: "YYYY-MM-DD",
   eDateFormatNoSecond: "YYYY-MM-DD HH:mm",
   eDateFormatMicro: "YYYY-MM-DD HH:mm:ss",
}

export const partTypeEnum = {
  part_type_1_home_banner: 1,
  part_type_2_home_intro: 2,
  part_type_3_home_how_to: 3,
  part_type_4_home_why: 4,
  part_type_5_clients: 5,
  part_type_6_feedback: 6,
  part_type_7_more_links: 7,
  part_type_8_home_claim: 8,
  part_type_9_prod_more: 9,
  part_type_10_why_choose_us: 10,

  part_type_21_prod_banner: 21,
  part_type_22_prod_demo: 22,
  part_type_23_prod_scenario: 23,
  part_type_24_prod_sample: 24,
  part_type_25_prod_relation: 25,
  part_type_26_prod_why_choose: 26,
  part_type_27_prod_accordion: 27,
  part_type_28_prod_slide: 28,
  part_type_29_prods_mgmt: 29,

  part_type_31_prod_more_banner: 31,
  part_type_32_prod_more_intro: 32,
  part_type_33_prod_more_single_intro: 33,

  part_type_41_price_solution: 41,
  part_type_42_price_matrix: 42,
  part_type_43_price_calculator: 43,
  part_type_44_price_question: 44,

  part_type_51_coop_partner_banner: 51,
  part_type_52_coop_partner_winwin: 52,
  part_type_53_coop_partner_devotion: 53,
  part_type_54_coop_partner_cooperation: 54,
  part_type_55_coop_partner_footer: 55,

  part_type_61_coop_customize_banner: 61,
  part_type_62_coop_customize_scenario: 62,
  part_type_63_coop_customize_solution: 63,
  part_type_64_coop_customize_digitalize: 64,
  part_type_65_coop_customize_deploy: 65,
  part_type_66_coop_customize_service: 66,
  part_type_67_coop_customize_deliver: 67,
  part_type_68_coop_customize_footer: 68,
  part_type_69_coop_contact_us: 69,

  part_type_71_help_doc_tag: 71
}