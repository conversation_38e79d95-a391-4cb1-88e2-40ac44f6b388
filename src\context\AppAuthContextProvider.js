import React, {createContext, useContext, useEffect, useMemo} from "react";
import { useQuery } from "@tanstack/react-query";
import { team_619_user_info_query } from "@/api/query/query";
import { team_630_user_log_out } from "@/api/http";


const AuthContext = createContext();

export const useAuthContext = () => useContext(AuthContext);

export default function AppAuthContextProvider({children}){

  const team619QueryResult = useQuery({
    ...team_619_user_info_query(),
    enabled: false
  });


  /**
   * 
   * @returns true 退出成功 false 退出失败
   */
  const logout = async() => {
    let result = await team_630_user_log_out()
    if(result.resultCode == 200){
      team619QueryResult.refetch();
    }
    return result
  }

  const contextValue = useMemo(()=>({
    loadUserInfo: team619QueryResult.refetch,
    removeUserInfo: team619QueryResult.remove,
    isAuthenticated: team619QueryResult.data?.resultCode === 200,
    loginInfo: team619QueryResult.data,
    logout
  }),[team619QueryResult.dataUpdatedAt])
  
  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}