import React, { useEffect, useState } from "react";
import { Image } from "antd";
import {
  Box,
  Typography
} from "@mui/material";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../../utils/commonUtils";

export default function DeliveryProcess({data}) {
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  }, [data?.widgetList]);
  return (
    <>
      {/* 交付流程-start */}
      {moduleData.length > 0 && <Box className="delivery-process" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        <Typography variant="h5" sx={{ color: 'white' }}>{filterWidgetList(moduleData, 1)[0]?.value}</Typography>
        <Typography sx={{ mt: "20px" }} variant="body2">
          {filterWidgetList(moduleData, 2)[0]?.value}
        </Typography>
        <div className="delivery-process-image">
          <Image preview={false} src={filterWidgetList(moduleData, 3)[0]?.value}/>
        </div>
      </Box>}
      {/* 交付流程-end */}
    </>
  );
}
