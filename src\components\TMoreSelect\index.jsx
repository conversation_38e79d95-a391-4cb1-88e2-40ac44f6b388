import React, { useEffect } from "react";
import { Checkbox,Col,Form,Row,Input,Dropdown,Button,Space } from "antd";
import "./index.scss"
import { useState } from "react";
// import * as qs from "qs"


/**
 * @param {Array<string>} values 被选中值
 * @param {Array} items 选项 {label:"",value:"",icon:""}
 * @returns 
 */
function SelectPane({values=[],items=[],onValuesChange}){
  const [form] = Form.useForm()
  const [search,setSearch] = useState("");
  const [defaultValues, ] = useState(values);

  const checkboxGroup = Form.useWatch("checkboxGroup", form)

  useEffect(()=>{
    if(checkboxGroup?.length && items.every(item => checkboxGroup.indexOf(item.value) > -1)) {
      form.setFieldValue("checkboxGroupAll", ["1"])
    }else{
      form.setFieldValue("checkboxGroupAll", [])
    }
  },[checkboxGroup])

  const onInputChange = (e) => {
    setSearch(e.target.value)
  }

  const onAllCheckChange = (e) => {
    if(e?.length && e.indexOf("1") > -1){
      let allValues = items.map(item => item.value) // .filter(item => isShow(item.label,search))
      form.setFieldValue("checkboxGroup", allValues)
    }else{
      form.setFieldValue("checkboxGroup", [])
    }
    onValuesChange?.(form.getFieldsValue())
  }

  const sortItems = (items) => {
    return [...items].sort((a,b)=>{
      if((defaultValues.includes(a.value) && defaultValues.includes(b.value)) || (!defaultValues.includes(a.value) && !defaultValues.includes(b.value))){
        return 0
      }

      if(defaultValues.includes(a.value) && !defaultValues.includes(b.value)){
        return -1
      }

      if(!defaultValues.includes(a.value) && defaultValues.includes(b.value)){
        return 1
      }
    })
  }

  const isShow = (label,search) => {
    if(search == ""){
      return true;
    }
    return label.toString().toLowerCase().includes(search.toString().toLowerCase())
  }

  const isShowSearch = (items) => {
    return items?.length > 4
  }

  const _onValuesChange = (values) => {
    onValuesChange?.({
      checkboxGroup:[],
      ...values
    })
  }
  
  return <div className="t-more-select">
    <div className={"t-more-select-search"} >
      <Input placeholder="" onChange={onInputChange} style={isShowSearch(items)?{}:{display:"none"}}/>
    </div>
    <div className="t-more-select-container">
      <Form form={form} name="dynamic_rule" onValuesChange={_onValuesChange} initialValues={{checkboxGroup:values}}>
        <Form.Item name="checkboxGroupAll" style={{marginBottom:"0px"}} className={search?"hide":""}>
          <Checkbox.Group onChange={onAllCheckChange}>
            <Row>
              <Col span={24} className={["t-more-select-item"].join(" ")}>
                <Checkbox value={"1"} className={`t-more-select-item-check tms-checked`} >
                  <span className="flexLeft">{""}<span>全部</span></span>
                </Checkbox>
              </Col>
            </Row>
          </Checkbox.Group>
        </Form.Item>
        <Form.Item name="checkboxGroup">
          <Checkbox.Group>
            <Row>
              {sortItems(items).map((item,index) => {
                return <Col span={24} key={index} className={["t-more-select-item",isShow(item.label,search)?"":"hide"].join(" ")}>
                  <Checkbox key={index} value={item.value} className={`t-more-select-item-check tms-checked`} >
                    <span className="flexLeft" style={{ display: "flex", alignItems: "center" }}>{item.icon || ""}{item.icon?<span style={{marginRight:5}} /> : ""}<span>{item.label}</span></span>
                  </Checkbox>
                </Col>
              })}
            </Row>
          </Checkbox.Group>
        </Form.Item>
      </Form>
    </div>

  </div>
}

/**
 * @param {String} defaultName 默认显示的值
 * @param {Array} value 默认显示的值
 * 
 * @returns 
 */
export default function TMoreSelect({defaultVisibleName="全部",visibleName,defaultValues=[],onValuesChange,items}){

  const [name,setName]= useState(defaultVisibleName);

  const getNames = (changedValues) => {
    let _name = changedValues.map(item => {
      return items.find(itemx => itemx.value == item).label
    }).join(",")
    return _name||"全部";
  }

  const _onValuesChange = (changedValues={}, allValues) => {
    // 如果是所有值都被选中则显示全部
    let _values = items.every(item => changedValues.checkboxGroup.indexOf(item.value) > -1)?[]:changedValues.checkboxGroup;
    if(visibleName == undefined){
      setName(getNames(_values))
    }
    onValuesChange?.(_values)
  }

  useEffect(()=>{
    if(visibleName != undefined){
      setName(visibleName)
    }else{
      setName(getNames(defaultValues))
    }
  },[visibleName,defaultValues])

  return <Dropdown overlay={<SelectPane items={items} values={defaultValues} onValuesChange={_onValuesChange}/>} trigger={["click"]} destroyPopupOnHide>
    <Button>
      <Space>
        <span className="t-more-select-visibleName">{name}</span>
        <span className="iconfont youjiantou_huaban fontsize-12" style={{ color: "#999999",transform:"rotate(90deg)" }}></span>
      </Space>
    </Button>
  </Dropdown>
}