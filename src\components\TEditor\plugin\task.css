@font-face {
  font-family: "taskiconfont"; /* Project id  */
  src: url('../font/taskiconfont.ttf?t=1667550588912') format('truetype');
}

/* task */
ul>li[data-list-style="checked"],ul>li[data-list-style="unchecked"] {
  list-style: none;
}

/* 任务已完成 */
ul>li[data-list-style="checked"]::before {
  font-family: "taskiconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1em;

  content: "\e64d";
  margin-right: 8px;
  margin-left: -26px;
  cursor: pointer;
}

/* 任务未完成 */
ul>li[data-list-style="unchecked"]::before {
  font-family: "taskiconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1em;

  content: "\e68e";
  margin-right: 8px;
  margin-left: -26px;
  cursor: pointer;
}