import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tabs } from "antd";
import { fabric } from "fabric";
import { useEffect, useState } from "react";
import './ImgCanvasModal.scss';

//涂鸦
let canvas;
export default function CanvasImageModal({visible, urlObj, onCancel, getCanvasProps}) {
  const [canvasObj, setCanvasObj] = useState({});
  const [canvasWidth, setCanvasWidth] = useState();
  const [canvasHeight, setCanvasHeight] = useState();
  const [canvasBorderColor, setCanvasBorderColor] = useState('#ff161a');
  const [canvasBgColor, setCanvasBgColor] = useState('transparent');
  const [startCanvasFlag, setStartCanvasFlag] = useState(false);

  const colorData = [
    {key: 1, value: 'rgb(101,169,230)',},
    {key: 2, value: '#ff161a',},
    {key: 3, value: '#ffba08',},
    {key: 4, value: '#1aad14',},
    {key: 5, value: 'transparent',},
  ]
  const bgColorData = [
    {key: 1, value: 'rgba(101,169,230,0.2)',},
    {key: 2, value: 'rgba(51,51,51,0.1)',},
    {key: 3, value: 'rgba(255,0,0,0.1)',},
    {key: 4, value: 'rgba(0,255,18,0.1)',},
    {key: 5, value: 'rgba(255,173,0,0.1)',},
    {key: 6, value: 'rgba(0,255,249,0.1)'},
    {key: 7, value: 'transparent'}
  ]
  const borderColorList = (
    <div>
      {
        colorData.map((color, index) => {
          return (
            <Button key={index}  size="small" type="link" onClick={() => getCanvasColor(color.value)}>
              <span 
              className={color.value == 'transparent' ? "transparentBorder" : "canvas-borderColor" }
              style={{ 
                backgroundColor: color.value, 
                border: canvasBorderColor == color.value ? '2px solid #666' : '' 
              }}></span>
            </Button>
          )
        })
      }
    </div>
  )
  const bgColorList = (
    <div>
      {
        bgColorData.map((color, index) => {
          return (
            <Button key={index}  size="small" type="link" onClick={() => getCanvasBgColor(color.value)}>
              <span 
              className={color.value == 'transparent' ? "transparentBorder" : "canvas-borderColor" }
              style={{ 
                backgroundColor: color.value, 
                border: canvasBgColor == color.value ? '2px solid #3279fe' : '' 
              }}></span>
            </Button>
          )
        })
      }
    </div>
  )
  const tabsItems = [
    { label: '边框', key: 'borderColor', children: borderColorList }, // 务必填写 key
    { label: '背景', key: 'bGColor', children: bgColorList },
  ];
  const borderBgTabs = (
    <Tabs className="borderBgColorTabs" size="small" items={tabsItems} />
  )
  useEffect(() => {
    if(visible && urlObj) {
      getBase64(urlObj)
    }
    return () => {
      unBindEvent();
    }
  },[visible,urlObj]) 

  const getCanvasColor = (color) => {
    setCanvasBorderColor(color)
  }

  const getCanvasBgColor = (color) => {
    setCanvasBgColor(color)
  }
  
  function getBase64(urlObj) {
    let imgUrl_ = urlObj;
    window.URL = window.URL || window.webkitURL;
    var xhr = new XMLHttpRequest();
    xhr.open("get", urlObj.url, true);
    xhr.responseType = "blob";
    xhr.onload = function(){
        if(this.status == 200){
            //得到一个blob对象
            var blob = this.response;
            // 至关重要
            let oFileReader = new FileReader();
            oFileReader.onloadend = function(e){
                // 此处拿到的已经是base64的图片了,可以赋值做相应的处理
                imgUrl_.url = e.target.result
                init(imgUrl_);
            }
            oFileReader.readAsDataURL(blob);
        }
    }
    xhr.send();
  }
  
  /**
  * @name: 初始化
  * */
  function init(urlObj) {
    canvas = new fabric.Canvas("canvas", {
      // backgroundColor: "rgb(100,100,200)", // 画布背景色
      // selectionColor: "rgba(255,255,255,0.3)", // 画布中鼠标框选背景色
      backgroundColor: "rgb(100,100,200)", // 画布背景色
      selectionColor: "rgba(101,169,230,0.2)", // 画布中鼠标框选背景色
      selectionLineWidth: 0, // 画布中鼠标框选边框1
      // selection: false, // 在画布中鼠标是否可以框选 默认为true
    });
    // createRect();
    insertImg(urlObj);
    bindEvent();
  }

  // 初始化绑定事件
  function bindEvent () {
    document.onkeydown = function () {
      let key = window.event.keyCode;
      console.log('按的键', key);
      if (key === 49) { //点击键盘 数字"1"后可以开始绘制标注框
        setStartCanvasFlag(true)
        createRect();
        canvas.skipTargetFind = true;
      } else if (key === 8 || key === 46) { //选中标注框对象 再点击键盘"delete"键可以删除标注框
        if (canvas.getActiveObject() && canvas.getActiveObject()?._objects?.length > 1) {
          canvas.getActiveObject()._objects.forEach((element) => {
            canvas.remove(element);
          });
          canvas.discardActiveObject();
        } else if (canvas.getActiveObject()) {
          canvas.remove(canvas.getActiveObject());
        }
      }
    };
  }

  function unBindEvent () {
    document.onkeydown = null;
  }

  /**
  * @name: 绘制矩形
  */
  let dtop = 0;
  let dleft = 0;
  let dw = 0;
  let dh = 0;
  let rect;

  function createRect(row) {
    canvas.on("mouse:down", (options) => {
      dleft = options.e.offsetX;
      dtop = options.e.offsetY;
    });
    canvas.on("mouse:up", (options) => {
      let offsetX =
        options.e.offsetX > canvas.width ? canvas.width : options.e.offsetX;
      let offsetY =
        options.e.offsetY > canvas.height ? canvas.height : options.e.offsetY;
  
      dw = Math.abs(offsetX - dleft);
      dh = Math.abs(offsetY - dtop);
      // 拦截点击
      if (dw === 100 || dh === 100) {
        return;
      }
  
      rect = new fabric.Rect({
        top: dtop > offsetY ? offsetY : dtop,
        left: dleft > offsetX ? offsetX : dleft,
        width: dw,
        height: dh,
        // fill: "rgba(101,169,230,0.2)",
        fill: canvasBgColor,
        // stroke: "rgb(101,169,230)", // 边框原色
        stroke: canvasBorderColor,
        strokeWidth: 2, // 边框大小1
        // angle: 15,
        // selectable: false, // 是否允许当前对象被选中
        lockRotation: true, // 不允许旋转
      });
      rect.set("strokeUniform", true); // 该属性在启用时可以防止笔划宽度受对象的比例值影响
      canvas.add(rect);
      stopDraw();
  
      canvas.skipTargetFind = false; //设置对象能否选中
    });
      
    canvas.on("mouse:move", (options) => {
      if (options.target) {
        objectMoving(options);
      }
    });
  }

  const insertImg = (urlObj) => {
    // 插入背景
    let ctx = canvas.getContext('2d')
    let image = new Image();
    image.crossOrigin='anonymous'
    image.src = urlObj.url
    // image.setAttribute('crossOrigin', 'anonymous') //关键
    // image.src = urlObj.url;
    // let canvasWidth_;
    // let canvasHeight_;
    let scale = parseFloat(image.width/image.height); // 计算图片比例
    if(image.width == image.height) {
        // canvasWidth_ = 1200
        // canvasHeight_ = 1200
        setCanvasWidth(1200)
        setCanvasHeight(1200)
    } else if(image.width > image.height) {
        // canvasWidth_ = 1200
        // canvasHeight_ = 1200 / scale
        setCanvasWidth(1200)
        setCanvasHeight(1200 / scale)
    } else {
        // canvasWidth_ = 1200
        // canvasHeight_ = 1200 * scale
        setCanvasWidth(1200)
        setCanvasHeight(1200 * scale)
    }
    // image.width = canvasWidth_
    // image.height = canvasHeight_
    setCanvasWidth(image.width)
    setCanvasHeight(image.height)
    image.onload = () => {
      ctx.drawImage(image, 0, 0)
      // 绘制图片
      // 设置canvas宽高
      canvas.setWidth(image.width);
      canvas.setHeight(image.height);
  
      fabric.Image.fromURL(urlObj.url, (img) => {
        img.set({
          scaleX: 1,
          scaleY: 1,
          left: 0,
          top: 0,
        });
        canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas));
      });
    };
  };

  // 释放canvas监听
  const stopDraw = () => {
      canvas.off("mouse:down");
      canvas.off("mouse:up");
      setStartCanvasFlag(false)
  };
  // 限制对象的 不超出画布
  function objectMoving(e) {
    var obj = e.target;
    if (!obj) return;
    if (
      obj.currentHeight > obj.canvas.height ||
      obj.currentWidth > obj.canvas.width
    ) {
      return;
    }
    obj.setCoords();
    // top-left corner
    if (obj.getBoundingRect().top < 0 || obj.getBoundingRect().left < 0) {
      obj.top = Math.max(obj.top, obj.top - obj.getBoundingRect().top);
      obj.left = Math.max(obj.left, obj.left - obj.getBoundingRect().left);
    }
    // bot-right corner
    if (
      obj.getBoundingRect().top + obj.getBoundingRect().height >
        obj.canvas.height ||
      obj.getBoundingRect().left + obj.getBoundingRect().width > obj.canvas.width
    ) {
      obj.top = Math.min(
        obj.top,
        obj.canvas.height -
          obj.getBoundingRect().height +
          obj.top -
          obj.getBoundingRect().top
      );
      obj.left = Math.min(
        obj.left,
        obj.canvas.width -
          obj.getBoundingRect().width +
          obj.left -
          obj.getBoundingRect().left
      );
    }
    setCanvasObj({...obj})
  }

  // 开始标注
  const startCanvas = () => {
    setStartCanvasFlag(!startCanvasFlag)
    createRect();
    canvas.skipTargetFind = true;
  }

  // 获取所有标注对象
  function getCanvasObj() {
    let urlObj_ = urlObj
    urlObj_.url = canvas.toDataURL('image/png')
    getCanvasProps(urlObj_)
    cancelCanvasImage()
  }

  // 关闭弹窗
  const cancelCanvasImage = () => {
    setStartCanvasFlag(false)
    canvas.clear();
    canvas.dispose()
    onCancel()
  }

  return (
    <Modal
    className="CanvasImageModal"
    width={1300}
    centered
    maskClosable={false}
    footer={null}
    keyboard={true}
    open={visible}
    onOk={cancelCanvasImage}
    onCancel={cancelCanvasImage}
    cancelText="取消"
    okText="确认">
      <div className={startCanvasFlag ? "startCanvas" : ''} style={{ width: canvasWidth, height: canvasHeight }}>
        <div className="canvasToolbar">
          <span style={{ fontSize: 16 }}>图片标注</span>
          <div style={{ display: 'flex', justifyContent: 'end', alignItems: 'center' }}>
            <Button 
            style={{ display: 'flex', alignItems: 'center', marginLeft: 10, color: '#999', backgroundColor: startCanvasFlag ? '#ededed' : '' }}
            title="开始标注" 
            className="canvasToolbar-btn" 
            size="small" 
            type="link" 
            onClick={startCanvas}>
              <span className="canvas-borderColor" style={{ backgroundColor: canvasBgColor, border: '1px solid', borderColor: canvasBorderColor }}></span>
            </Button>
            <Popover
            // content={borderColorList}
            content={borderBgTabs}
            trigger="click"
            >
                <Button 
                title="颜色" 
                style={{ display: 'flex', alignItems: 'center', marginLeft: 10, color: '#999' }} 
                size="small" 
                type="link"
                icon={<span className="iconfont yanse1 fontsize-18"/>}>
                  {/* <span className="canvas-borderColor" style={{ backgroundColor: canvasBgColor, border: '2px solid', borderColor: canvasBorderColor }}></span> */}
                </Button>
            </Popover>
          </div>
          <div>
            <Button title="保存" className="canvasToolbar-btn" size="small" type="link" icon={<CheckOutlined />} onClick={getCanvasObj}/>
            <Button title="取消" className="canvasToolbar-btn" style={{ color: '#666' }} size="small" type="link" icon={<CloseOutlined />} onClick={cancelCanvasImage}/>
          </div>
        </div>
        <canvas id="canvas"></canvas>
      </div>
    </Modal>
  )
}