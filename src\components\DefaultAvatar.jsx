import React from "react";
import avatarImg from "../assets/images/avatar.png"
import noAvatarImg from "../assets/images/noAvatar.png"
import { Avatar } from "antd";

/**
 * 默认头像匹配
 * @param avatarSrc  头像地址
 * @param avatarFlg  无头像 条件区别：有avatarFlg条件 显示蓝色底头像；无avatarFlg条件 显示灰色底头像
 * @param size       头像尺寸,默认20px
 */
export default function DefaultAvatar(props) {
  const { avatarSrc, avatarFlg, size } = props
  return <>
    <Avatar src={avatarSrc ? avatarSrc : (avatarFlg ? avatarImg : noAvatarImg)} size={size ? size : 20} />
  </>
}