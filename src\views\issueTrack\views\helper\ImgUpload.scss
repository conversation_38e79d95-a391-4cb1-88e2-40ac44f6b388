.previewBox {
  position: relative;
  margin: 0 10px 10px 0;
  width: 100px;
  height: 100px;
  line-height: 100px; 
  text-align: center;
  border: 1px solid #999;
  &-open {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translate(-50%, -50%);
    transition: all .3s ease 0s;
    .ant-btn {
      color: white;
    }
  }
}
.previewBox:hover {
  .previewBox-open {
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.5);
    transition: all .2s ease 0s;
  }
}

.workOrderUpload {
  width: 100px;
  height: 100px;
  .ant-upload {
    width: 100px;
    height: 100px;
  }
  .ant-upload {
    margin-top: -5px;
  }
}

// 新建工单弹窗，预览图片样式
.previewImage-padding {
  .ant-image-preview-wrap {
    .ant-image-preview {
      .ant-image-preview-content {
        .ant-image-preview-body {
          .ant-image-preview-img-wrapper {
            padding: 40px;
          }
        }
      }
    }
  }
}