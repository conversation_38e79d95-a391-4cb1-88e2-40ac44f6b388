import React, { useEffect, useState, useRef } from "react";
import { Tab, Tabs, Typography } from "@mui/material";
import { Collapse, Button, Image } from "antd";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { DownOutlined } from "@ant-design/icons";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../utils/commonUtils";

const { Panel } = Collapse;

// 功能简介-右侧滚动切换模板
export default function FunctionIntroductionScrollSwitch({data}) {
  const navigate = useNavigate();
  const containerRef = useRef(null);
  const timer = useRef(null)
  const [snapshotValue, setSnapshotValue] = useState();  // 默认选中
  const [moduleData, setModuleData] = useState([]);
  const [currentModuleItem, setCurrentModuleItem] = useState([]);

  useEffect(() => {
    containerRef.current.addEventListener("wheel", (e) => {
      let position_ = filterWidgetList(moduleData, 11)[0]?.value || ""
      if(position_ !== "top" && position_ !== "bottom") return // 除了top和bottom外其他布局不执行以下逻辑不阻止默认行为
      e.preventDefault(); // 阻止原生滚动事件
      // 获取滚动位置
      let scrollLeft = containerRef.current.scrollLeft
      const scrollTotalWidth = containerRef.current.scrollWidth
      const scrollItemWidth = containerRef.current.offsetWidth
  
      const scrollBehavior = 'smooth'
      let offset = scrollLeft + e.deltaY * 4 // 放大偏移倍数
      containerRef.current.scrollTo({
        top: 0,
        left: offset,
        behavior: scrollBehavior,
      })
  
      // 防抖
      if (timer.current) {
        clearTimeout(timer.current)
      }
  
      timer.current = setTimeout(() => {
        // 计算是否滚动
        scrollLeft = computeScroll(offset, scrollItemWidth)
  
        containerRef.current.scrollTo({
          top: 0,
          left: scrollLeft,
          behavior: 'smooth',
        })
      }, 700)
    })
  },[])

  useEffect(() => {
    if(Array.isArray(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
      setSnapshotValue(filterWidgetList(formatData, 6)[0]?.widgetId)
      setCurrentModuleItem([...filterWidgetList(formatData, 6)[0]?.children || []])
    }
  },[data?.widgetList])

  useEffect(() => {
    if(snapshotValue) {
      let arr_ = filterWidgetList(moduleData, 6).filter(el => el.widgetId === snapshotValue)[0]?.children || []
      setCurrentModuleItem([...arr_])
    }
  },[snapshotValue])

  const computeScroll = (currentScrollLeft, scrollElWith) => {
    // 判断滚动偏移是否满足滚动要求
    const index = Math.round(currentScrollLeft / scrollElWith)
    return scrollElWith * index
  }

  // 选择
  const selectContentLi = (item) => {
    setSnapshotValue(item.widgetId)
  }

  // 获取模板排列位置
  const getDemoPosition = (type) => {
    let className_ = "";
    switch (type) {
      case "left":
      case "right":
      case "top":
      case "bottom":
        className_ = "FunctionIntroductionScrollSwitch-" + type
        break;    
      default:
        break;
    }
    return className_
  }
  return (
    <div 
        className={[
          "FunctionIntroductionScrollSwitch", 
          getDemoPosition(filterWidgetList(moduleData, 11)[0]?.value)
        ].join(" ")}
        style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}
      >
      {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography className="FunctionIntroductionScrollSwitch-title" variant="h5">
        {filterWidgetList(moduleData, 1)[0]?.value}
      </Typography>}
      <div className="FunctionIntroductionScrollSwitch-content">
        {currentModuleItem.length > 0 && <div className="FunctionIntroductionScrollSwitch-content-left">
          <Image 
          className="FunctionIntroductionScrollSwitch-content-left-image" 
          width="100%" 
          preview={false} 
          src={filterWidgetList(currentModuleItem, 3)[0]?.value}/>
          <div className="FunctionIntroductionScrollSwitch-content-left-text">
            <div className="FunctionIntroductionScrollSwitch-content-left-text-title">
              {filterWidgetList(currentModuleItem, 1)[0]?.value}</div>
            <div className="FunctionIntroductionScrollSwitch-content-left-text-detail">
              {filterWidgetList(currentModuleItem, 2)[0]?.value}
            </div>
            {filterWidgetList(currentModuleItem, 4).length > 0 && <Button 
            style={{ marginTop: 10, padding: 0, color: '#0077f2' }} 
            size="small" type="link"
            onClick={() => navigate(filterWidgetList(currentModuleItem, 4)[0]?.linkUrl || '')}>
              {filterWidgetList(currentModuleItem, 4)[0]?.value}
            </Button>}
          </div>
        </div>}
        {filterWidgetList(moduleData, 6).length > 0 ? <div className="FunctionIntroductionScrollSwitch-content-right" ref={containerRef}>
          {(filterWidgetList(moduleData, 6) || []).map((item, index) => (
            <div 
              className={["FunctionIntroductionScrollSwitch-content-right-li", item.key === snapshotValue && "select_li"].join(" ")}
              key={item.key}
              onClick={() => selectContentLi(item)}
            >
              <div className="scrollSwitch-image">
                <Image width="100%" preview={false} src={filterWidgetList((item?.children || []), 3)[0]?.value}/>
              </div>
              <div style={{ marginTop: 6 }}>
                <div style={{ fontWeight: 'bold' }}>{filterWidgetList((item?.children || []), 1)[0]?.value}</div>
                <div className="scrollSwitch-detail">{filterWidgetList((item?.children || []), 2)[0]?.value}</div>
              </div>
            </div>
          ))}
        </div> : <div ref={containerRef}/>}
      </div>
    </div>
  );
}