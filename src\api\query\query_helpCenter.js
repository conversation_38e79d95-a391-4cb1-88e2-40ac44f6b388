import { 
  doc_004_get_tutorial_info,
} from '../http';
import { doc004 } from "@/utils/ApiPath";
import { getNodeConfigByNodeType, getColorByType } from "@/utils/TsbConfig";
import { transformSearhQueryParams } from "@/utils/ArrayUtils";

// 获取帮助文档信息
export const doc_004_get_tutorial_info_query = (teamId, objNodeId, searchQuery, enabled = true) => ({
  queryKey: [doc004, teamId, objNodeId, searchQuery],
  queryFn: async () => {
    return doc_004_get_tutorial_info({ teamId, objNodeId, ...transformSearhQueryParams(searchQuery) })
        .then((res) => {
          if (res.resultCode == 200) {
            const rootNode = { nodeId: objNodeId, children: res.docList };
            const [rootNodeUi, treeKeyList] = transformToObjTreeUi(rootNode);
            const spaceId = res.spaceId ? res.spaceId : -999
            return { rootNodeUi, treeKeyList, spaceId };
          }
          return {}
        })
        .catch((err) => {
          console.log(err);
        });
  },
  enabled: enabled,
  cacheTime: Infinity
})

// doc返回的就是tree结构，只是更改对象而已
function transformToObjTreeUi(rootNodeUi, treeKeyList = []) {
  // TODO:不能使用对象赋值的方式，不会生效
  rootNodeUi.key = rootNodeUi.nodeId?.toString();
  rootNodeUi.label = rootNodeUi.title;
  rootNodeUi.labelColor =  getColorByType(rootNodeUi.nameTextColorType);
  rootNodeUi.showMoreIcon = false; // 更多菜单
  // rootNodeUi.nodeParentId = rootNodeUi.parentId; // 和左侧树以及obj_node_info接口统一为nodeParentId,拖拽时使用 0505后端更改为nodeParentId
  let nodeItemConfig = getNodeConfigByNodeType(rootNodeUi.nodeType);
  let icon = <span className={"iconfont "+ nodeItemConfig?.icon} style={{color: getColorByType(rootNodeUi.rightFlgIconType)}}/> 
  rootNodeUi.icon = icon;
  // 更改items节点为children，便于使用treeOp的方法，可以后端调整生成
  treeKeyList.push({ key: rootNodeUi.nodeId, label: rootNodeUi.label || "", parentKey: rootNodeUi?.nodeParentId?.toString() }); //记录nodeId与parentNodeId的映射关系
  (rootNodeUi.children || []).map((item) => transformToObjTreeUi(item, treeKeyList));
  return [rootNodeUi, treeKeyList]; //返回 objNode所在的树 转换后的 Ui所需要的树状结构(带有children节点)
}
