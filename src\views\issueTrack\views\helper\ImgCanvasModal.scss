.CanvasImageModal {
    .ant-modal-content {
        // background-color: transparent;
        box-shadow: none;
        .ant-modal-close {
            display: none;
        }
        .ant-modal-body {
            // display: flex;
            // justify-content: center;
            padding: 30px 0;
            height: calc(100vh - 40px);
            overflow: auto;
            background-color: transparent;
            .canvas-container {
                // width: 100% !important;
                // height: 100% !important;
                .lower-canvas {
                    // width: 100% !important;
                    // height: 100% !important;
                }
                .upper-canvas {
                    // width: 100% !important;
                    // height: 100% !important;
                }
            }
        }
        .ant-modal-body::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
            height: 10px;
        }
        .ant-modal-body::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: #ededed;
        }
        .ant-modal-body::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            // background   : #ededed;
        }
    }
}

.canvasToolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    padding: 6px 10px;
    width: 100%;
    background-color: white;
    border-bottom: 1px solid #f2f2f2;
    z-index: 99;
    &-btn {
        margin-left: 10px;
    }
    &-btn:hover {
        background-color: #f2f2f2;
    }
}

.canvas-borderColor {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 1px solid #f2f2f2;
}

.startCanvas {
    .canvas-container {
        .upper-canvas {
            cursor: crosshair !important;
        }
    }
}

.transparentBorder {
    position: relative;
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 1px solid #f2f2f2;
}
.transparentBorder::before {
    position: absolute;
    top: 50%;
    left: 50%;
    content: '';
    width: 16px;
    height: 1px;
    background-color: #dedede;
    transform: translate(-50%, -50%) rotate(-45deg);
}

.borderBgColorTabs {
    .tablist {
        .ant-tabs-nav-wrap {
            .ant-tabs-nav-list {
                .ant-tabs-tab {
                    padding: 4px 0;
                }
            }
        }
    }
}