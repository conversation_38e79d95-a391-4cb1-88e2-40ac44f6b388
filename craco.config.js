
const path = require('path')
const ProgressPlugin = require('progress-bar-webpack-plugin')
const WebpackBar = require('webpackbar')
const webpack = require("webpack")
const { RetryChunkLoadPlugin } = require('webpack-retry-chunk-load-plugin');

const resolve = pathUrl => path.join(__dirname, pathUrl)

module.exports = {
  webpack: {
    alias: {
      '@': resolve('src'),
    },
    plugins:[
      new RetryChunkLoadPlugin({
        cacheBust: `function() {
          return Date.now();
        }`,
        maxRetries: 3,
      }),
    ],
    configure: (webpackConfig, { env, paths }) => { 
      // moment时间插件库过大，打包指定语言
      webpackConfig.plugins.push(new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn/));

      const fallback = webpackConfig.resolve.fallback || {}; 
      Object.assign(fallback, { 
        url: require.resolve('url'),
        assert:require.resolve('assert'),
        crypto: require.resolve('crypto-browserify'),
        http: require.resolve('stream-http'),
        https: require.resolve('https-browserify'),
        os: require.resolve('os-browserify/browser'),
        buffer: require.resolve('buffer'),
        stream: require.resolve('stream-browserify'),
      }) 
      webpackConfig.resolve.fallback = fallback;  
      
      webpackConfig.module.rules.push({
        test: /\.m?js/,
        resolve: {
          fullySpecified: false
        }
      })
    
      if(process.env.NODE_ENV === "production"){
        webpackConfig.output = {
          ...webpackConfig.output,
          library: {
            type: "umd",
            name: "default"
          },
          scriptType: 'text/javascript'
        }

        const terser = webpackConfig?.optimization?.minimizer?.find(
          x => x.options.extractComments // Find Terser: lookup "extractComments"
        )

        if(terser){
          terser.options.minimizer.options.compress.drop_console = true;
        }

        webpackConfig.optimization.splitChunks = {
          chunks: 'all',
          cacheGroups: {
            vendors: { // 提取node_modules代码
              test: /node_modules/, // 只匹配node_modules里面的模块
              name: 'vendors', // 提取文件命名为vendors,js后缀和chunkhash会自动加
              minChunks: 1, // 只要使用一次就提取出来
              chunks: 'initial', // 只提取初始化就能获取到的模块,不管异步的
              minSize: 0, // 提取代码体积大于0就提取出来
              priority: 1, // 提取优先级为1
            }
          }
        }

        webpackConfig.plugins.push(new WebpackBar({
          basic: false,
          profile: false,
        }));

        // webpackConfig.plugins.push(new BundleAnalyzerPlugin()); // 分析打包后的包体积
      }else{
        webpackConfig.plugins.push(new ProgressPlugin());
      }

      // webpackConfig.entry['editor.worker'] = './node_modules/monaco-editor/esm/vs/editor/editor.worker.js'
      // webpackConfig.entry['json.worker'] = './node_modules/monaco-editor/esm/vs/language/json/json.worker'
      // webpackConfig.entry['css.worker'] = './node_modules/monaco-editor/esm/vs/language/css/css.worker'
      // webpackConfig.entry['html.worker'] = './node_modules/monaco-editor/esm/vs/language/html/html.worker'
      // webpackConfig.entry['ts.worker'] = './node_modules/monaco-editor/esm/vs/language/typescript/ts.worker'
      // console.log(webpackConfig.module.rules)
      return webpackConfig; 
    },
  },
}