import React,{useRef} from "react";

// export default function () {
//   const click = useDebounce((e: Event) => {
//     console.log(e);
//   }, 1000)
  
//   return (
//     <button onClick={click}>按钮</button>
//   );
// }

export function useDebounce(fn,delay) {
  const refTimer = useRef();

  return function f(...args) {
    if (refTimer.current) {
      clearTimeout(refTimer.current);
    }
    refTimer.current = setTimeout(() => {
      fn(...args);
    }, delay);
  }
}
