/* eslint-disable react-hooks/exhaustive-deps */
import { EllipsisOutlined } from "@ant-design/icons";
import { eInputMaxLength } from "@/utils/enum";
import { getSearchValueLabel } from "@/utils/logicUtils";
import { eCtxTypeId, eNodeTypeId } from "@/utils/TsbConfig";
import { Button, Input, Space } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { formatSvg } from "@/utils/ViewUtils";
import "./index.scss";

export default function TreeItem({ nodeData, teamId, onMoreBtnClick, onShowMoreButtonClick, selectedKeys, searchValue, sysIconList = [], ...props }) {
  
  const inputNameRef = useRef(null)

  useEffect(() => {
    if (nodeData.isRenaming) {
      inputNameRef.current?.focus() // 聚焦
      inputNameRef.current?.select() // 内容全选
      document.addEventListener("keydown", escFunction, false);
      return () => {
        document.removeEventListener("keydown", escFunction, false);
      };
    }
  }, [nodeData.isRenaming])

  // 确认重命名
  const onNameChanged = (e) => {
    // 不写或者名称与原先相同都不会重命名
    onMoreBtnClick && onMoreBtnClick({ ...nodeData, nodeItem: nodeData, ctxType: eCtxTypeId.ctx_100_rename_confirmed, name: e.target.value, })
  }

  // 取消重命名
  const onNameCancle = () => {
    onMoreBtnClick && onMoreBtnClick({ ...nodeData, nodeItem: nodeData, ctxType: eCtxTypeId.ctx_101_rename_cancle })
  }

  // 搜索结果高亮显示:为什么这么改，用于解决搜索状态重命名时，需要获取label字段，所以在显示时对label处理，而不是直接修改数据源的label字段
  const getLabelView = useMemo(() => {
   return getSearchValueLabel(searchValue, nodeData);
  }, [searchValue, nodeData]);

  // TODO:onBlur先于keydown事件触发，导致esc点击后会先触发onBlur，onNameChanged，keydown的onNameCancle不会触发 @garry
  //点击Esc取消重命名
  const escFunction = useCallback((event) => {
    if(event.keyCode == 27) {
      //TODO，需要walt协助，在objNodeMoreOps里面同步取消重命名
      onNameCancle();
    }
  }, []);

  // TODO: treeNode高度存在问题，待跟踪
  return <>
    {
      nodeData.isRenaming && <div data-key={nodeData.key}>
        <Input ref={inputNameRef}
          placeholder=""
          defaultValue={nodeData.label}
          onBlur={onNameChanged}
          onPressEnter={onNameChanged}
          onClick={(e) => { e.preventDefault(); e.stopPropagation(); }}
          maxLength={eInputMaxLength.fifty}
        />
      </div>
    }
    {!nodeData.isRenaming &&
      <div className="flexaotu tree-dir-between" data-key={nodeData.key} title={nodeData.label}>
        <span className={`tree-dir-label ${nodeData.nameTextStrikeFlg == '1' ? "tree-dir-title-delete" : ""}`}
          style={{ color: nodeData.labelColor }}>
          {getLabelView}
        </span>
        <Space size={2}>
          {
            /* 目前文档库的下的文档特有 */
            /* nodeData.nodeType == eNodeTypeId.nt_31201_objtype_tutorial_doc && */ formatSvg(sysIconList.find(sys => sys.propType == nodeData.nodeIconType)?.propValue) 
          }
           <span className="tree-dir-more-action-white" >
            {nodeData.showMoreIcon &&
              <Button type="link"
                icon={<EllipsisOutlined />}
                // 选中时固定显示更多按钮
                className={`tree-dir-color ${selectedKeys && selectedKeys[0] === nodeData.key ? "tree-dir-more-btn-display" : "tree-dir-more-btn"}`}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onShowMoreButtonClick && onShowMoreButtonClick({
                    event: e,
                    node: nodeData
                  })
                }} />
            }
         </span>
        </Space>
      </div>
    }
  </>
}