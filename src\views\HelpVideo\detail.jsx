import React from "react";
import { Pagination, Layout, Row, Col, Space, List, Tag } from 'antd';
import { EyeOutlined, LikeOutlined } from "@ant-design/icons";
import { Box } from "@mui/material";
import TopMenuBar from "../../components/TopMenuBar";
import FooterBar from "../../components/FooterBar";
import "./detail.scss";
import { TEditorPreview } from "@/components/TEditor/TEditor";
import { VideoCard } from "./index"

function ListItem () {
  return <div style={{padding: "10px 20px", borderBottom: "1px solid #0000000f", boxSizing: "border-box"}}>
    <div style={{color:"#333"}}>标题标题标题</div>
    <div style={{display:"flex", justifyContent:"space-between", color: "#999", marginTop: 5, fontSize: 12}}>
      <span>2023/11/10 18:57:50</span>
      <Space size={10}>
        <span><EyeOutlined/> 851</span>
        <span><LikeOutlined />127</span>
      </Space>
    </div>
  </div>
}

export default function HelpVideoDetail(){
  return <Layout className="helpVideoDetail">
    <Box sx={{ height: 90 }} />
    <Box sx={{ width: "100%", padding: "0 100px" }}>
      <Box className="helpVideoDetail-TopMenuBar-box">
        <TopMenuBar />
      </Box>
    </Box>
    <Layout style={{display:"flex", alignItems:"center", flex: 1}}>
      <Layout style={{maxWidth: "1200px"}}>
        <div style={{padding: "20px", background:"#fff"}}>
          <div style={{display:"flex"}}>
            <div>分类：</div>
            <Space wrap style={{flex: 1, marginLeft: 20}}>
              <a>全部</a>
              <a>操作系统</a>
              <a>在线运行</a>
              <a>接口测试</a>
              <a>文档/文档库</a>
              <a>Excel</a>
              <a>甘特图/工时登记</a>
              <a>提醒事项/日程</a>
              <a>工作报告</a>
              <a>搜索/报表/仪表板/订阅</a>
              <a>考试/培训管理</a>
              <a>编程设计工具</a>
              <a>问题跟踪</a>
            </Space>
          </div>
          <div style={{display:"flex", marginTop: 20}}>
            <div>排序：</div>
            <Space wrap style={{flex: 1, marginLeft: 20}}>
              <a>最新上传</a>
              <a>浏览量</a>
            </Space>
          </div>
        </div>
        <Layout style={{background: "#fff", borderTop: "1px solid #0000000f"}}>
          <Layout.Sider width={260} theme="light">
            <List 
              dataSource={Array(20).fill(1)} 
              renderItem={() => <ListItem />}>
            </List>
          </Layout.Sider>
          <Layout.Content style={{borderLeft: "1px solid #0000000f", padding: "0 15px 15px 15px"}}>
            <div style={{color:"#333", fontSize: 20, fontWeight: "bold", padding: "10px 0"}}>快速入门</div>
            <div style={{padding: "5px"}}>
              <Tag>在线运行</Tag>
              <span style={{marginLeft: 40, color:"#999"}}>发布时间：2022/11/10 18:57:50</span>
              <Space size={10} style={{marginLeft: 30, color:"#999"}}>
                <span><EyeOutlined/> 851</span>
                <span><LikeOutlined />127</span>
              </Space>
            </div>
            <div style={{minHeight: 300, marginTop: 10}}>
              <TEditorPreview content={"这里是视频图文区域"} />
            </div>
            <div>
              <Space>
                <a>上一篇</a>
                <a>下一篇</a>
                <span>订阅报表</span>
              </Space>
            </div>
            <div>
              <div>相关视频</div>
              <Space>
                <VideoCard />
                <VideoCard />
                <VideoCard />
              </Space>
            </div>
          </Layout.Content>
      </Layout>
    </Layout>
    </Layout>
  </Layout>
}