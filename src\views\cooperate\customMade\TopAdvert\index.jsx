import React, { useEffect, useState } from "react";
import { Box, Typography, Stack } from "@mui/material";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../../utils/commonUtils";

export default function TopAdvert(props) {
  const { data } = props;
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  }, [data?.widgetList]);

  return (
    <>
      {/* 定制主体区-start */}
      <Box className="customMade-main" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography variant="h3" sx={{ fontSize: "40px", color: 'white'}}>{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
        {!!filterWidgetList(moduleData, 2)[0]?.value && <Typography sx={{ mt: "10px", color: '#cecfd0' }} variant="subtitle1">
          {filterWidgetList(moduleData, 2)[0]?.value}
        </Typography>}
        {filterWidgetList(moduleData, 4).length > 0 && <Box className="customMade-main-stack">
          <Stack
            sx={{ flexWrap: "wrap", justifyContent: "center", mt: "15px" }}
            direction={{ xs: "column", sm: "row" }}
            spacing={7}
          >
            {filterWidgetList(moduleData, 4).map((item, index) => (
              <Box key={item.widgetId} className="customMade-main-stack-li">
                <Box className="customMade-main-stack-li-text">
                  <Typography variant="subtitle1" sx={{ color: '#02fafb' }}>{item.value}</Typography>
                  {/* <Typography variant="subtitle1">10</Typography>
                  <Typography className="fontsize-12" variant="body1">
                    年+
                  </Typography> */}
                </Box>
                {/* <Typography className="fontsize-12" variant="body1">
                  技术沉淀
                </Typography> */}
              </Box>
            ))}
          </Stack>
        </Box>}
      </Box>
      {/* 定制主体区-end */}
    </>
  );
}
