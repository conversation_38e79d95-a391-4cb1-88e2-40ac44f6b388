/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2022-11-11 14:25:47
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2023-04-10 13:53:52
 * @Description: 错误报告（api 返回状态码 -1场景）
 * 
 * @params {serialNo} 错误号
 * @params {resultMessage} 错误码
 * @params {resultMoreMessage} 错误信息
 */

import { Button, Input, Modal } from 'antd';
import { useEffect, useState } from 'react';

import { globalEventBus } from "@/utils/eventBus";

import "./ErrorModal.scss";

const { TextArea } = Input;
/**
 * 
 * @param {*} param0 
 * @returns 
 */

export default function ErrorModal() {
 
  const [selectDataModalOpen, setSelectDataModalOpen] = useState(false);
  const [nodeItem, setNodeItem] = useState({});

  const { response: {data : { serialNo, resultMessage, resultMoreMessage } =  {} } = {} } = nodeItem; // 注意response.data中才是真实数据

  useEffect(() => {
    globalEventBus.on("openErrorModalEvent", openErrorModalEvent);
    return () => globalEventBus.off("openErrorModalEvent", openErrorModalEvent);
  }, []);

  function openErrorModalEvent(target, args) {
    setNodeItem(args);
    setSelectDataModalOpen(true);
  }

  // 确定
  const handleModalOk = () => {
    setSelectDataModalOpen(false);
  }

  // 关闭
  const handleModalCancle = () => {
    setSelectDataModalOpen(false);
  }

  return (
    <>
      <Modal
        className="tms-modal error-modal"
        title={
          <div className="error-modal-title"><span className="iconfont guanbi2fill marginRight-5 error-modal-title-icon" ></span><span className="error-modal-title-text">错误报告</span></div>
        }
        centered={true}
        open={selectDataModalOpen}
        onCancel={() => handleModalCancle()}
        onClose={() => handleModalCancle()}
        width={600} // 宽度固定
        // maskClosable={false}
        destroyOnClose={true}
        footer={null}
      >
        <div className="error-modal-body">
          <div><span className="iconfont tishi1 error-modal-warn"></span></div>
          <div>
            <p className='fontsize-18 fontcolor-bold'>
              iTeam系统捕获到未知错误，非常抱歉，给您带来不便。系统已经生成错误报告，点击“确定”按钮后，您可继续使用，感谢您的支持。
            </p>
            <p className='fontsize-16'>
              <span>编号：</span> <span>{serialNo}</span>
            </p>
            <p className='fontsize-16'>
              <span>分类：</span> <span>{resultMessage}</span>
            </p>
            {/* tmsbug-4324：错误信息为空的时候，整行可以不显示 */}
            {
              !!resultMoreMessage && <p className='fontsize-16 error-modal-message'>
              <span className='error-modal-message-label'>详细信息：</span>
              <p className={`error-modal-message-text ${ !!resultMoreMessage ? "error-modal-message-text-border" : ""}`}>{resultMoreMessage}</p>
            </p>
            }
          </div>
        </div>
        <div className="error-modal-confirm">
          <Button type="primary" onClick={() => handleModalOk()}>确定</Button>
        </div>
      </Modal>
    </>
  )
}
