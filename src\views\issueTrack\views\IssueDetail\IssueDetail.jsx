/* eslint-disable jsx-a11y/anchor-is-valid */
import * as http from "@/api/http";
import { TEditorPreview } from "@/components/TEditor/TEditor";
import TLoading from "@/components/TLoading";
import { getRootUrl } from "@/router/RouterRegister";
import {
  eConsoleNodeId,
  eConsoleUiControl,
  eObjSocialOpType,
  PLACEHOLDER_BLANK,
  eDateFormatType,
  eFileObjId,
} from "@/utils/enum";
import { globalUtil } from "@/utils/globalUtil";
import * as toolUtil from "@/utils/toolUtil";
import { eNodeTypeId } from "@/utils/TsbConfig";
import {
  useMutationIssue011OpNode, useMutationTeam512delAttachment,
  useQueryGetFavoriteStatus,
  useQueryIssue016IssueInfo,
  useQueryTeam503GetAttachmentList
} from "@/views/issueTrack/service/issueHooks";
import { useIssueSearchParams } from "@/views/issueTrack/service/issueSearchHooks";
import { DislikeFilled, DislikeOutlined, LikeFilled, LikeOutlined } from '@ant-design/icons';
import { Button, Col, Image, Row, Space, Upload } from "antd";
import copy from "copy-to-clipboard";
import { useEffect, useState } from "react";
import { getPropValueByIdType, twoTimeInterval } from "../../utils/ArrayUtils";
import EditText from "./EditText";
import "./IssueDetail.scss";
import IssueDetailOpTabs from "./IssueDetailOpTabs";
import DateUtil from "@/utils/dateUtil";
import { checkDate } from "@/utils/ArrayUtils";
import { issue_512_get_issue_total_query, team_553_get_front_query_query } from "@/api/query/query";
import { useQueryClient } from "@tanstack/react-query";
import { validateAuthActions } from "@/utils/commonUtils";
import { useAuthContext } from "@/context/AppAuthContextProvider";
/**
 * issue详情页
 */
export default function IssueDetail({ viewMode, issueNodeId, selectionList, /* userList, */ gotoPreviousIssue, gotoNextIssue, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg }) {

  const authActions = useAuthContext();
  
  const queryClient = useQueryClient();

  // const { teamId, nodeId: issueListNodeId, } = useParams();
  const teamId = process.env.REACT_APP_WORKORDER_TEAMID;
  const issueListNodeId = process.env.REACT_APP_WORKORDER_NODEID;
  const { issueQueryId } = useIssueSearchParams(); //queryId

  // 获取issue详情
  const { issueInfo , issueHistoryList, issueObjId, _issueFormColumn, isLoading, refetch: issue016Refetch } = useQueryIssue016IssueInfo(teamId, issueListNodeId, issueNodeId, !!issueNodeId); //获取issue详情
  const { onIssue011OperateNode, isMutating, isMutateError, mutateError } = useMutationIssue011OpNode(); //编辑issue

  // issue字段信息
  let [issueTitleAttr, setIssueTitleAttr] = useState({}); //issue标题字段
  let [issueAttrs, setIssueAttrs] = useState([]); //表单元素及其值

  // 社交点赞等，评论列表
  const [likeFlg, setLikeFlg] = useState(false); //点赞
  const [likeCnt, setLikeCnt] = useState(false); //点赞数

  const [dislikeFlg, setDislikeFlg] = useState(0); //是否倒赞
  const [dislikeCnt, setDislikeCnt] = useState(0); //倒赞数

  const [watchFlg, setWatchFlg] = useState(false); //关注
  const [watchCnt, setWatchCnt] = useState(0); //关注数

  const { favoriteStatus} = useQueryGetFavoriteStatus(teamId, issueInfo?.objNodeId, !!issueInfo?.objNodeId); // 社交状态

  // 附件
  const [attachmentImgPreviewFlg, setAttachmentImgPreviewFlg] = useState(false); //预览
  const [attachmentImgPreviewIdx, setAttachmentImgPreviewIdx] = useState(0);   //预览index位置
  const { attachmentList, isLoading: attachmentListLoading, refetch: refetchAttachmentList } = useQueryTeam503GetAttachmentList(teamId, issueNodeId, eNodeTypeId.nt_31704_objtype_issue_item, !!issueObjId); //附件
  const { delAttachmentMutation } = useMutationTeam512delAttachment(); // 删除附件

  // 获取社交状态，存储配置相应的值
  useEffect(() => {
    if (favoriteStatus?.resultCode === 200) {
      const { objFavoriteAndWatch
        : { likeFlg, dislikeFlg, likeCnt, dislikeCnt, favoriteCnt, favoriteFlg, watchFlg, watchCnt, } } = favoriteStatus
      setLikeFlg(likeFlg);
      setLikeCnt(likeCnt);
      setWatchFlg(watchFlg);
      setDislikeFlg(dislikeFlg);
      setDislikeCnt(dislikeCnt);
      setWatchCnt(watchCnt);
    }
  }, [favoriteStatus]);

  useEffect(() => {
    if (issueObjId) {
      processData(_issueFormColumn)
    }
  }, [issueObjId, _issueFormColumn]);

  if (isLoading || attachmentListLoading) {
    return <TLoading />;
  }

  const props = {
    name: 'file',
    multiple: true,
    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`,
    data: { teamId: teamId, moduleName: "issues", nodeId: eFileObjId, objType: eNodeTypeId.nt_31704_objtype_issue_item },
    //使组件列表不显示
    showUploadList: false,
    onChange: uploadFile
  };

  const downUrl = (fileName, url) => {
    //不同于之前的点击下载只甩出一张图片，会放在右上角下载盒子里
    let x = new XMLHttpRequest();
    x.open("GET", url, true);
    x.responseType = "blob";
    x.onload = function (e) {
      let blob = x.response;
      if ("msSaveOrOpenBlob" in navigator) {
        //IE导出
        window.navigator.msSaveOrOpenBlob(blob, fileName);
      } else {
        let a = document.createElement("a");
        a.download = fileName;
        a.href = URL.createObjectURL(blob);
        document.querySelector("body").appendChild(a);
        a.click();
        document.querySelector("body").removeChild(a);
      }
    };
    x.send();
  };

  //点赞/取消点赞
  function likeClick() {
    if(!validateAuthActions(authActions)) return;
    setLikeFlg(prevFlg => !prevFlg);
    setLikeCnt(likeFlg ? Math.max(likeCnt - 1, 0) : likeCnt + 1);
    _load_team029_obj_social_op(likeFlg ? eObjSocialOpType.OpType_5_Cancel_Like : eObjSocialOpType.OpType_4_Like)
  }

  //倒赞/取消倒赞
  function disLikeClick() {
    if(!validateAuthActions(authActions)) return;
    setDislikeFlg(prevFlg => !prevFlg);
    setDislikeCnt(dislikeFlg ? Math.max(dislikeCnt - 1, 0) : dislikeCnt + 1);
    _load_team029_obj_social_op(dislikeFlg ? eObjSocialOpType.OpType_7_Cancel_Dislike : eObjSocialOpType.OpType_6_Dislike);
  }

  // 关注/取消关注
  function followClick() {
    if(!validateAuthActions(authActions)) return;
    setWatchFlg(prevFlg => !prevFlg);
    setWatchCnt(watchFlg ? Math.max(watchCnt - 1, 0) : watchCnt + 1);
    _load_team029_obj_social_op(watchFlg ? eObjSocialOpType.OpType_9_Cancel_Watch : eObjSocialOpType.OpType_8_Watch);
  }

  /**
   * 点赞及关注
   * @param 4点赞  5取消点赞  6 倒赞  7:取消倒赞  8关注，9取消关注
   */
  function _load_team029_obj_social_op(type) {
    var params = { teamId: teamId, objNodeId: issueInfo.objNodeId, opType: type, }
    http.team_029_obj_social_op(params).then((res) => {
      if (res.resultCode === 200) {
        globalUtil.success(res.resultMessage);
        if(type == eObjSocialOpType.OpType_9_Cancel_Watch || type == eObjSocialOpType.OpType_8_Watch){ // 关注后需要更新关注总数
         queryClient.invalidateQueries(issue_512_get_issue_total_query(teamId, issueListNodeId).queryKey) // 创建工单后，刷新统计列表数据
        }
      }
    });
  }

  // 根据id查找预览的位置
  function findPreviewIndex(id) {
    return attachmentList?.filter(el => el.fileType == 1)?.findIndex(item => item.id == id)
  }

  //点击预览功能
  function onAttachmentPreviewClick(item) {
    let index = findPreviewIndex(item.id)
    setAttachmentImgPreviewIdx(index);
    setAttachmentImgPreviewFlg(true);
  }

  //删除附件功能
  function deleteFile(item) {
    let params = { teamId: teamId, id: item.id, }
    delAttachmentMutation(params, {
      onSuccess: (data, vars) => {
        if (data.resultCode == 200) {
          console.log("data", data)
          let fileObj = { objId: item.id, objType: 23, objName: item.fileName, seqNo: 1 }
          let _params = { teamId: teamId, nodeId: issueNodeId, objDelList: [fileObj] }
          onIssue011OperateNode(_params, {
            onSuccess: (_data, _vars) => {
              if (_data.resultCode == 200) {
                refetchAttachmentList(); //刷新附件列表
              }
            }
          })
        }
      }
    })
  }

  //附件上传成功后，再将附件业务数据存储一份至issue
  function uploadFile(info) {
    if (info.file.status == 'uploading') {
      console.log('uploading', info.file, info.fileList);
    }
    if (info.file.status === 'done') {
      if (info.file.response.resultCode == 200) {
        globalUtil.success(`${info.file.name} 文件上传成功！`);
        let fileObj = { objId: info.file.response.id, objType: 23, objName: info.file.name, seqNo: 1 }
        var params = { nodeId: issueNodeId, teamId: teamId, }
        params.objRelList = [fileObj];
        onIssue011OperateNode(params, {
          onSuccess: (_data, _vars) => {
            if (_data.resultCode == 200) {
              refetchAttachmentList(); //刷新附件列表
            }
          }
        })
      }
    } else if (info.file.status === 'error') {
      globalUtil.error(`${info.file.name} 文件上传失败！`);
    }
  }

  // 格式化级联菜单(系统模块)数据
  function formatCascader(value, optionList = []) {
    let name = ''
    if (value) {
      let options = value.split('/')
      let optionObj = {}
      for (let i = 0; i < options.length; i++) {
        if (i == 0) {
          optionObj = optionList.filter(el => el.propType == options[i])[0] || {}
          name += (optionObj.propValue || '')
        } else {
          optionObj = optionList.filter(el => el.selectionId == optionObj.propType && el.propType == options[i])[0] || {}
          name += (optionObj.propValue ? '/' + optionObj.propValue : '')
        }
      }
    }
    return name
  }
  // 处理数据
  function processData(_attrs) {
    _attrs.forEach((_attr, index) => {
      // 有拉下selectionLid
      if (_attr.selectionLid) {
        // -701 人员匹配
       /*  if (_attr.selectionLid == "-701") {
          _attr.itemValue = getUserNameById(userList, _attr.columnValue)
        } else  */ if (_attr.selectionLid == "1930") {
          // 1930 系统模块匹配
          _attr.itemValue = formatCascader(_attr.columnValue, selectionList)
        } else {
          _attr.itemValue = getPropValueByIdType(selectionList, _attr.selectionLid, _attr.columnValue); //从code找到value
        }
      } else {
        _attr.itemValue = _attr.columnValue
      }

      if (_attr.attrNid == eConsoleNodeId.Nid_11110_Issue_Status) {
        let _issueStatus = {};
        _issueStatus.name = _attr.attrNid
        if (_attr.columnValue) {
          _issueStatus.columnValue = _attr.columnValue.toString()
        }
        _issueStatus.itemValue = _attr.itemValue  //显示的值, columnValue可能是code, 而itemValue转换为value
        _issueStatus.id = _attr.selectionLid;
      } else if (_attr.attrNid == eConsoleNodeId.Nid_11102_Issue_Title) {
        setIssueTitleAttr(_attr);
      }
    });

    _attrs = _attrs.filter(_attr => //排除掉如下几个attrId
      !(
        // _attr.uiControl == eConsoleUiControl.RichTextBox ||
        _attr.attrNid == eConsoleNodeId.Nid_11102_Issue_Title ||
        _attr.attrNid == eConsoleNodeId.Nid_11101_Issue_IssueNo ||
        // _attr.attrNid == eConsoleNodeId.Nid_11118_Issue_CreateDate ||
        _attr.attrNid == eConsoleNodeId.Nid_11105_Issue_Creator)
    );
    setIssueAttrs(_attrs);
  }

  // 编辑
  function onEditTextChanged(value, item) {
    var params = { nodeId: issueNodeId, teamId: teamId, }
    //对用户传递进来的值，进行判断（因为用户如只点击不选择项目，则传递进来空值，空值则不传递value）
    if (value == "" && item.attrNid != eConsoleNodeId.Nid_11103_Issue_Description) {
      console.log(params, "param的值")
    } else {
      params[item.attrNid] = value
      onIssue011OperateNode(params);
    }
  }

  //复制链接地址
  function copyLink() {
    try {
      const copyUrl = getLinkUrl()
      console.log("copyUrl", copyUrl)
      copyUrl && copy(copyUrl);
      copyUrl ? globalUtil.success("链接已复制至剪贴板") : globalUtil.error("无链接");
    } catch (e) {
      globalUtil.success("链接复制失败");
    }
  }

  // 链接地址
  function getLinkUrl() {
    const pathname = `/team/${teamId}/issues/${issueListNodeId}/issueId/${issueNodeId}${issueQueryId ? `?queryId=${issueQueryId}` : ``}`
    const url = getRootUrl() + pathname;
    return url
  }


  return (<>
    {/*1. 头部信息区块*/}
    <div className="issue-detail" >
      <Row className="issue-detail-border">
        <Space direction="vertical" className="issue-detail-header" size={0}>
          <div className="issue-detail-title">
            <EditText _onContentChanged={(value) => onEditTextChanged(value, issueTitleAttr)}
              _contentValue={issueTitleAttr.itemValue}
              attrNid={eConsoleNodeId.Nid_11102_Issue_Title}
              editType={issueTitleAttr.uiControl}
              selectionId={issueTitleAttr.selectionLid}
              // userList={userList}
              _width={600}
              editable={false}
              >
              {issueInfo.title}
            </EditText>
          </div>
          <div className="issue-detail-des">
            <Space size={15}>
              <a onClick={() => copyLink()}>{issueInfo.prefix}</a>
              {
                // 创建时间和结束时间不相等则视为有修改操作，显示修改时间
                issueInfo.createDt != issueInfo.updateDt ?
                  <>
                    <span className="fontcolor-light">由{issueInfo.creatorName}创建</span>
                     {/* 修改人从后端直接获取 04.06 by walt  */}
                    <span className="fontcolor-light">{issueInfo.updatorName}修改于
                      <span>
                        { checkDate(issueInfo.updateDt, new DateUtil().getNow(eDateFormatType.eDateFormatMicro), 1) ? 
                        <span>{twoTimeInterval(issueInfo.updateDt, new DateUtil().getNow(eDateFormatType.eDateFormatMicro))}前</span>
                        : 
                        issueInfo.updateDt}
                      </span>
                    </span>
                  </>
                  :
                  <span className="fontcolor-light">由{issueInfo.creatorName}创建于{issueInfo.createDt}</span>
              }
            </Space>
            <Space size={20} style={{ display: "flex", alignItems: "baseline" }}>
              <a className="fontsize-12 issueColor" onClick={gotoPreviousIssue} disabled={previousIssueLinkDisabledFlg}>&lt;上一条</a>
              <a className="fontsize-12 issueColor" onClick={gotoNextIssue} disabled={nextIssueLinkDisabledFlg}>下一条&gt;</a>
            </Space>
          </div>
        </Space>
      </Row>
    </div>
    {/*2. 基本信息区块*/}
    <div className="issues" style={{ borderTop: "1px solid #f2f2f2" }}>
      <>
        <div className="issueDetail-title">基本信息</div>
        <div className="issue-detail">
          <section id="issue-info">
            <Row className="issue-detail-base">
                {issueAttrs.map((attr, _index) => {
                  return (
                    <Col key={_index} span={(attr.uiControl == eConsoleUiControl.RichTextBox || attr.uiControl == eConsoleUiControl.Image) ? 24 : 12} >
                      <div key={_index} className= { attr.uiControl == eConsoleUiControl.RichTextBox ? "issue-detail-col-block" : "issue-detail-col"} >
                        <div className="issue-detail-col-label">{attr.name}</div>
                        <div className="issue-detail-col-value">
                          {
                            <EditText 
                              teamId ={teamId}
                              issueNodeId={issueNodeId}
                              issueId={issueInfo?.id} 
                              _onContentChanged={(value) => onEditTextChanged(value, attr)}
                              _contentValue={attr.columnValue}
                              attrNid={attr.attrNid}
                              editType={attr.uiControl}
                              format={attr.format}
                              selectionId={attr.selectionLid}
                              selectionList={selectionList}
                              // userList={userList}
                              _width={"100%"}
                              // editable={attr.modifiable != 0}
                              editable={attr.attrNid == eConsoleNodeId.Nid_11234_WorkOrder_WorkOrderStatus || attr.attrNid == eConsoleNodeId.Nid_11236_WorkOrder_OpenType} // 工单状态/开放类型可编辑，其他不可编辑
                              onRefresh={issue016Refetch}
                            >
                              {attr.itemValue}
                            </EditText>
                          }
                        </div>
                      </div>
                    </Col>
                  )
                })}
            </Row>
          </section>
          {/*3 附件列表 区块*/}
          <div>
            <div className="optionhistory-title">
              <span className="fontsize-14" style={{ paddingRight: 5 }}>附件</span>
              <Upload {...props} >
                <Button type="link" ><span style={{ color: "#3279fe", fontWeight: "bold", fontSize: 18 }}>+</span></Button>
              </Upload>
            </div>
          </div>
          <Space direction="vertical" size={5} style={{ display: "flex", padding: "0px 28px" }}>
            {attachmentList.map((item, index) => {
              return <Row justify="space-between" align="middle" key={index}>
                <Col span={15}>
                  <Space direction="horizontal">
                    <span style={{ color: "#F7932A" }} className="issue-list-content-sub-item-type iconfont a-morenwenjianjiabeifen3 fontsize-14" />
                    <span className="fontcolor-light">{item.fileName}</span>
                  </Space>
                </Col>
                {/* userName从后端直接获取 04.06 by walt  */}
                <Col span={2}>
                  <span className="fontsize-12 fontcolor-light" type="link">{item.userName}</span>
                </Col>
                <Col span={4}>
                  <span className="fontsize-12 fontcolor-light">{item.createDt}</span>
                </Col>
                <Col span={1} style={{ textAlign: "center" }}>
                  {item.fileType == 1 ? <a className="fontsize-12" type="link" onClick={() => onAttachmentPreviewClick(item)}>预览</a> : ""}
                </Col>
                <Col span={1} style={{ textAlign: "center" }}>
                  <a className="fontsize-12" type="link" onClick={() => downUrl(item.fileName, item.filePath)}>下载</a>
                </Col>
                <Col span={1} style={{ textAlign: "center" }}>
                  <a className="fontsize-12" type="link" onClick={() => deleteFile(item)}>删除</a>
                </Col>
              </Row>
            })}
          </Space>
          {/*6 点赞，关注，收藏 区块*/}
          <Row justify="space-between" align="middle" style={{ paddingRight: "15px", alignItems: "center", padding: "20px 28px 15px 28px", borderTop: "1px solid #f2f2f2" }}>
            <Col>
              <Space size={20}>
                {/* 点赞状态 */}
                <a onClick={() => likeClick()} className="tms-link-nocolor op-btn">
                  <Space size={2}>
                  {likeFlg ? <LikeFilled className="fontsize-14"/> : <LikeOutlined className="fontsize-14"/>}
                  <span className="fontsize-14">{likeCnt == 0 ? PLACEHOLDER_BLANK : likeCnt}</span>
                  </Space>
                </a>
                {/* 倒赞状态 */}
                <a onClick={() => disLikeClick()} className="tms-link-nocolor op-btn">
                  <Space size={2}>
                    {dislikeFlg ? <DislikeFilled className="fontsize-14"/> : <DislikeOutlined className="fontsize-14"/>}
                    <span className="fontsize-14"> {dislikeCnt == 0 ? PLACEHOLDER_BLANK : dislikeCnt}</span>
                  </Space>
                </a>
                {/* 关注 */}
                <a onClick={() => followClick()} className="tms-link-nocolor op-btn">
                  <Space size={2}>
                    {watchFlg ? <span className="iconfont yanjing_xianshi fontsize-20 " /> : <span className="iconfont yanjing_yincang fontsize-20" />}
                    <span className="fontsize-14">{watchCnt == 0 ? PLACEHOLDER_BLANK : watchCnt}</span>
                  </Space>
                </a>
              </Space>
            </Col>
          </Row>
          {/*7 评论列表及操作历史Tabs */}
          {issueNodeId && 
          <IssueDetailOpTabs
            teamId={teamId}
            issueNodeId={issueNodeId}
          />}
        </div>
      </>
      {/* 多图预览模块 */}
      <Image.PreviewGroup
        preview={{
          visible: attachmentImgPreviewFlg,
          current: attachmentImgPreviewIdx,
          onVisibleChange: (value) => {
            setAttachmentImgPreviewFlg(value);
          }
        }}>
        {attachmentList && attachmentList.length > 0 ?
          attachmentList?.filter(el => el.fileType == 1).map((item, index) => {
            return <Image width={200}
              style={{ display: 'none' }}
              src={item.filePath}
              key={index}
            />
          })
          :
          <></>
        }
      </Image.PreviewGroup>
    </div>
  </>);

}
