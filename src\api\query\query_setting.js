import {
  setting_202_get_team_allusergrp,
  setting_320_get_node_priv,
  setting_407_get_console_selection_list
} from '../http_setting';

//获取"全部成员"组成员列表
export const setting_202_get_team_allusergrp_query = (teamId) => ({
  queryKey: ['setting_202_get_team_allusergrp', teamId],
  queryFn: async () => {
    let result = await setting_202_get_team_allusergrp({ teamId })
    if (result.resultCode == 200) {
      return result.userList
    }
  }
})

export const setting_320_get_node_priv_query = (teamId, nodeId) => ({
  queryKey: ['setting_320_get_node_priv', teamId, nodeId],
  queryFn: async () => setting_320_get_node_priv({ teamId, nodeId }),
  cacheTime: Infinity
})

export const setting_407_get_console_selection_list_query = (teamId) => ({
  queryKey: ['setting_407_get_console_selection_list', teamId],
  queryFn: async () => {
    let result = await setting_407_get_console_selection_list({ teamId })
    return result?.optionList??[];
  }
})