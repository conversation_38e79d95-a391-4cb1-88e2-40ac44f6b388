import React, { useEffect, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import { Image } from "antd";
import {
  Box,
  Typography,
  Button,
  Paper,
  Stack,
  ImageList,
  ImageListItem,
} from "@mui/material";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import "./index.scss";
import {team_1301_get_page_part_widget} from "../../../api/http_common";
import TopMenuBar from "../../../components/TopMenuBar";
import FooterBar from "../../../components/FooterBar";
import UserEvaluation from "../../../components/partTemplate/common/UserEvaluation";
import Advertisement from "../../../components/partTemplate/common/Advertisement";
import FunctionIntroduction from "../../../components/partTemplate/common/FunctionIntroduction";
import QuickRecom from "../../../components/partTemplate/common/QuickRecom";
import { skipOS } from "../../../utils/commonUtils";
import PortalLink from "../../../components/PortalLink";
import { useAuthContext } from "@/context/AppAuthContextProvider";
import {
  isArrayUtils,
  filterWidgetList,
  formatTreeData,
} from "../../../utils/commonUtils";
import PurchaseConsultationBanner from "../../../components/PurchaseConsultationBanner";
import OurUsers from "../../../components/partTemplate/common/OurUsers"; // 我们的用户
import FunctionIntroductionAccordion from "../../../components/FunctionIntroductionAccordion";
import FunctionIntroductionScrollSwitch from "../../../components/FunctionIntroductionScrollSwitch";
import MoreProducts from "../../../components/MoreProducts"; // 更多产品
import TopPoster from "../../../components/partTemplate/products/TopPoster";
import {formatVariantColor} from "../../../utils/muiThemeUtils";
import {partTypeEnum} from 'src/utils/enum';

export default function ProductsDetail() {
  const navigate = useNavigate();
  const location = useLocation();
  const { state } = useLocation();
  const [partList, setPartList] = useState([]);

  useEffect(() => {
    getPagePartWidget(6666, location.pathname);
  }, []);

  // 获取产品详情页面元素
  const getPagePartWidget = (teamId, menuUrl) => {
    team_1301_get_page_part_widget({ teamId, menuUrl }).then((res) => {
      if (res.resultCode == 200) {
        setPartList([...res.partList]);
      }
    });
  };

  const getProductItemContent = (type, data, index) => {
    switch (type) {
      case partTypeEnum.part_type_5_clients : //我们的客户
        return <OurUsers key={index} data={data} />;
      case partTypeEnum.part_type_6_feedback : // 用户评价
        return <UserEvaluation key={index} data={data} />;
      case partTypeEnum.part_type_7_more_links : //快捷推荐
        return <QuickRecom key={index} data={data} />;
      case partTypeEnum.part_type_9_prod_more :  //更多产品
        return <MoreProducts key={index} data={data} />;
      case partTypeEnum.part_type_21_prod_banner : //Banner
        return <Advertisement key={index} data={data} isBgFlag={false} />;
      case partTypeEnum.part_type_22_prod_demo : // 实时演示
        return <Demonstrate key={index} data={data} />;
      case partTypeEnum.part_type_23_prod_scenario : // 使用场景
        return <FunctionIntroduction key={index} data={data} />;
      case partTypeEnum.part_type_24_prod_sample : // 示例
        return <GiveTypicalExamples key={index} data={data} />;
      case partTypeEnum.part_type_25_prod_relation :
        return <ProductInterworking key={index} data={data} />;
      case partTypeEnum.part_type_26_prod_why_choose : // 为什么选择iTeam
        return <ProductWhyChoose key={index} data={data} />;
      case partTypeEnum.part_type_27_prod_accordion : // 示例-手风琴
        return <FunctionIntroductionAccordion key={index} data={data} />;
      case partTypeEnum.part_type_28_prod_slide : // 示例-幻灯片
        return <FunctionIntroductionScrollSwitch key={index} data={data} />;
      case partTypeEnum.part_type_29_prods_mgmt : // 合集
        return <TopPoster key={index} data={data} />;
      case partTypeEnum.part_type_8_home_claim : // 购买咨询
        return <PurchaseConsultationBanner key={index} data={data} />;
    }
  };

  return (
    <div className="productsDetail">
      <Box sx={{ height: 76 }} />
      <Box sx={{ width: "100%", padding: "0 100px" }}>
        <Box className="productDetail-TopMenuBar-box">
          <TopMenuBar />
        </Box>
      </Box>
      {partList.map((item, index) => (
        <React.Fragment key={index}>
          {getProductItemContent(item.partType, item, index)}
        </React.Fragment>
      ))}
      <FooterBar />
    </div>
  );
}

// 演示
function Demonstrate({ data }) {
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData]);
    }
  }, [data]);
  return (
    <>
      {moduleData.length > 0 ? (
        <Box
          className="demonstrate"
          style={{
            background:
              filterWidgetList(moduleData, 12)[0]?.value || "transparent",
          }}
        >
          <Typography sx={{ color: "white", fontSize: 34 }} variant="h5">
            {filterWidgetList(moduleData, 1)[0]?.value}
          </Typography>
          <Paper
            className="demonstrate-page"
            elevation={1}
            sx={{ overflow: "hidden" }}
          >
            <Box className="demonstrate-page-top">
              <Box className="white-bar"></Box>
              <Box className="dot-list">
                <Box className="dot-li dot-red"></Box>
                <Box className="dot-li dot-yellow"></Box>
                <Box className="dot-li dot-green"></Box>
              </Box>
            </Box>
            {filterWidgetList(moduleData, 10).length > 0 && (
              <video
                muted
                autoPlay
                controls
                width="1200"
                src={filterWidgetList(moduleData, 10)[0]?.value}
              ></video>
            )}
            {filterWidgetList(moduleData, 3).length > 0 && (
              <Image
                width={"100%"}
                preview={false}
                src={filterWidgetList(moduleData, 3)[0]?.value}
              />
            )}
          </Paper>
        </Box>
      ) : (
        <></>
      )}
    </>
  );
}

// 产品示例
function GiveTypicalExamples({ data }) {
  const navigate = useNavigate();
  const authActions = useAuthContext();
  const [moduleData, setModuleData] = useState([]);
  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData]);
    }
  }, [data?.widgetList]);

  // 过滤数据
  const filterWidgetList = (data, type) => {
    return data.filter((el) => el.widgetType === type);
  };

  // 按钮点击
  const btnClick = (item) => {
    if (item.linkUrl === "os") {
      skipOS(authActions.isAuthenticated);
    } else {
      navigate(item.linkUrl);
    }
  };
  return (
    <>
      {moduleData.length > 0 && (
        <Box
          className="give-typical-examples"
          style={{
            background:
              filterWidgetList(moduleData, 12)[0]?.value || "transparent",
          }}
        >
          {!!filterWidgetList(moduleData, 1)[0]?.value && (
            <Typography variant="h5" sx={{ fontSize: 34 }}>
              {filterWidgetList(moduleData, 1)[0]?.value}
            </Typography>
          )}
          {filterWidgetList(moduleData, 3).length > 0 && (
            <ImageList
              sx={{
                margin: "10px auto 20px",
                width: 1000,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                flexWrap: "wrap",
              }}
              cols={2}
            >
              {filterWidgetList(moduleData, 3).map((item) => (
                <ImageListItem
                  key={item.widgetId}
                  sx={{
                    width: 490,
                    boxShadow: "0px 0px 4px 1px rgba(0,0,0,.06)",
                  }}
                >
                  <img src={item.value} />
                </ImageListItem>
              ))}
            </ImageList>
          )}
          {filterWidgetList(moduleData, 4).length > 0 && (
            <Box>
              {filterWidgetList(moduleData, 4).map((item, index) => (
                <Button
                  key={item.widgetId}
                  sx={{ ml: "20px", width: 150, borderRadius: 20 }}
                  variant={formatVariantColor(item.style)?.variant}
                  color={formatVariantColor(item.style)?.color}
                  onClick={() => btnClick(item)}
                >
                  {item.value}
                </Button>
              ))}
            </Box>
          )}
        </Box>
      )}
    </>
  );
}

// 为什么选择产品
function ProductWhyChoose({ data }) {
  const authActions = useAuthContext();
  const navigate = useNavigate();
  const [moduleData, setModuleData] = useState([]);
  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData]);
    }
  }, [data?.widgetList]);

  return filterWidgetList(moduleData, 6).length > 0 ? (
    <Box
      className="why-choose"
      style={{
        background: filterWidgetList(moduleData, 12)[0]?.value || "transparent",
      }}
    >
      <Box className="why-choose-title">
        {!!filterWidgetList(moduleData, 1)[0]?.value && (
          <Typography variant="h5" sx={{ fontSize: 34 }}>
            {filterWidgetList(moduleData, 1)[0]?.value}
          </Typography>
        )}
      </Box>
      <Box className="why-choose-tabbar__body">
        <Box className="why-choose-tabbar__item">
          <Box className="why-choose-advantages">
            {filterWidgetList(moduleData, 6).map((item, index) => (
              <Box className="why-choose-advantages-elem" key={index}>
                <Box className="why-choose-advantage">
                  <div>
                    <CheckCircleOutlineIcon
                      sx={{ fontSize: 60, color: "#2095f3" }}
                    />
                    <Box className="why-choose-advantage__title">
                      {filterWidgetList(item?.children || [], 1)[0]?.value}
                    </Box>
                    <Box className="why-choose-advantage__text">
                      {filterWidgetList(item?.children || [], 2)[0]?.value}
                    </Box>
                  </div>

                  {filterWidgetList(item?.children || [], 4).length > 0 && (
                    <div style={{ textAlign: "center" }}>
                      <Button
                        variant={
                          formatVariantColor(
                            filterWidgetList(item?.children || [], 4)[0]?.style
                          )?.variant
                        }
                        color={
                          formatVariantColor(
                            filterWidgetList(item?.children || [], 4)[0]?.style
                          )?.color
                        }
                        onClick={() => {
                          filterWidgetList(item?.children || [], 4)[0]
                            ?.linkUrl === "os"
                            ? skipOS(authActions.isAuthenticated)
                            : navigate(
                                filterWidgetList(item?.children || [], 4)[0]
                                  ?.linkUrl
                              );
                        }}
                      >
                        {filterWidgetList(item?.children || [], 4)[0]?.value}
                      </Button>
                    </div>
                  )}
                </Box>
              </Box>
            ))}

            {/* <Box className="why-choose-advantages-elem">
              <Box className="why-choose-advantage-cta">
                <Box className="why-choose-advantage-cta__title">
                  开始使用
                </Box>
                <Box className="why-choose-advantage-cta__text">
                  在我们的官方技术支持的帮助下，提供两个免费团队，
                  <br />
                  企业版产品试用一个月
                </Box>
                <Link className="why-choose-advantage-cta__link">
                  开始使用&nbsp;&gt;
                </Link>
              </Box>
            </Box> */}
          </Box>
        </Box>
      </Box>
    </Box>
  ) : (
    <></>
  );
}

// 产品互通
function ProductInterworking({ data }) {
  const navigate = useNavigate();
  const authActions = useAuthContext();
  const [title, setTitle] = useState("");
  const [moduleData, setModuleData] = useState([]);
  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let title =
        filterWidgetList(formatTreeData([], data.widgetList), 1)[0]?.value ||
        "";
      let formatData = formatTreeData([], data.widgetList);
      setTitle(title);
      setModuleData([...formatData]);
    }
  }, [data?.widgetList]);

  // 按钮点击
  const btnClick = (item) => {
    if (item.linkUrl === "os") {
      skipOS(authActions.isAuthenticated);
    } else {
      navigate(item.linkUrl);
    }
  };

  return (
    <Box
      className="ProductInterworking"
      style={{
        background: filterWidgetList(moduleData, 12)[0]?.value || "transparent",
      }}
    >
      {!!title && <Typography variant="h5" style={{ fontSize: 34 }}>{title}</Typography>}
      <Box className="ProductInterworking-stack">
        <Stack
          sx={{ flexWrap: "wrap", justifyContent: "center", mt: "40px" }}
          direction={{ xs: "column", sm: "row" }}
          spacing={10}
        >
          {filterWidgetList(moduleData, 6).map((item, index) => (
            <Box className="ProductInterworking-stack-li" key={item.key}>
              <Typography
                sx={{ fontSize: 16, fontWeight: "bold" }}
                variant="subtitle1"
              >
                {filterWidgetList(item?.children || [], 1)[0]?.value}
              </Typography>
              <Typography sx={{ fontSize: 12 }} variant="body1">
                {filterWidgetList(item?.children || [], 2)[0]?.value}
              </Typography>
              <PortalLink
                className="fontsize-14"
                style={{ color: "#0077f2" }}
                to={filterWidgetList(item?.children || [], 4)[0]?.linkUrl}
                name={filterWidgetList(item?.children || [], 4)[0]?.value}
              />
            </Box>
          ))}
        </Stack>
      </Box>
      <Box sx={{ mt: "20px" }}>
        {filterWidgetList(moduleData, 4).map((item, index) => (
          <Button
            key={item.widgetId}
            sx={{ ml: "10px", width: 150, borderRadius: 20 }}
            variant={formatVariantColor(item.style)?.variant}
            color={formatVariantColor(item.style)?.color}
            onClick={() => btnClick(item)}
          >
            {item.value}
          </Button>
        ))}
      </Box>
    </Box>
  );
}
