import React, { useEffect, useState } from "react";
import { Image } from "antd";
import { Box, Typography, Stack } from "@mui/material";
import "./Cust_Part_2_Multi_Tech.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../utils/commonUtils";

export default function Cust_Part_2_Multi_Tech(props) {
  const { data } = props;
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  }, [data?.widgetList]);

  return (
    <>
      {/* 多端多场景-start */}
      <Box className="multiEnd-multiScenario" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography 
          variant="h5"
          sx={{
            fontSize: '34px',
            fontWeight: '500'
          }}
        >
          {filterWidgetList(moduleData, 1)[0]?.value}
        </Typography>}
        {!!filterWidgetList(moduleData, 2)[0]?.value && <Typography 
          sx={{ 
            mt: "10px", 
            fontSize: '16px'
          }} 
          variant="body2"
        >
          {filterWidgetList(moduleData, 2)[0]?.value}
        </Typography>}
        {filterWidgetList(moduleData, 6).length > 0 && <Box className="multiEnd-multiScenario-stack">
          <Stack
          sx={{ flexWrap: "wrap", justifyContent: "center", mt: "40px" }}
          direction={{ xs: "column", sm: "row" }}
          spacing={4}
          >
            {filterWidgetList(moduleData, 6).map((item, index) => (
              <Box className="multiEnd-multiScenario-stack-li" key={item.widgetId}>
                <Box>
                  <Image style={{ borderRadius: "50%" }} preview={false} width={80} height={80} src={filterWidgetList((item?.children || []), 3)[0]?.value}/>
                </Box>
                <Typography sx={{ margin: '10px 0' }} variant="h6">{filterWidgetList((item?.children || []), 1)[0]?.value}</Typography>
                <Typography 
                  sx={{ 
                    color: '#666',
                    lineHeight: 1.5
                  }} 
                  variant="body2"
                >
                  {filterWidgetList((item?.children || []), 2)[0]?.value}
                </Typography>
              </Box>
            ))}
          </Stack>
        </Box>}
      </Box>
      {/* 多端多场景-end */}
    </>
  );
}
