import Icon from '@ant-design/icons';

import { ReactComponent as BugSvg } from "../assets/svg/bug.svg";
import { ReactComponent as ChangeSvg } from "../assets/svg/change.svg";
import { ReactComponent as EnhanceSvg } from "../assets/svg/enhance.svg";
import { ReactComponent as NewDemandSvg } from "../assets/svg/newDemand.svg";
import { ReactComponent as StorySvg } from "../assets/svg/story.svg";
import { ReactComponent as ImproveSvg } from "../assets/svg/improve.svg";
import { ReactComponent as SubtaskSvg } from "../assets/svg/subtask.svg";
import { ReactComponent as TaskSvg } from "../assets/svg/task.svg";

import { ReactComponent as KuaijiejiaobiaoSvg } from "../assets/svg/kuaijiejiaobiao.svg";

// 问题类别
export const IssueTypeBugIcon = props => <Icon component={BugSvg} {...props} />;
export const IssueTypeChangeIcon = props => <Icon component={ChangeSvg} {...props} />;
export const IssueTypeEnhanceIcon = props => <Icon component={EnhanceSvg} {...props} />;
export const IssueTypeNewDemandIcon = props => <Icon component={NewDemandSvg} {...props} />;
export const IssueTypeStoryIcon = props => <Icon component={StorySvg} {...props} />;
export const IssueTypeImproveIcon = props => <Icon component={ImproveSvg} {...props} />;
export const IssueTypeSubtaskIcon = props => <Icon component={SubtaskSvg} {...props} />;
export const IssueTypeTaskIcon = props => <Icon component={TaskSvg} {...props} />;


export const Iconkuaijiejiaobiao = props => <Icon component={KuaijiejiaobiaoSvg} {...props}/>
