/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2023-05-10 13:39:19
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2023-05-10 14:37:56
 * @Description: 请填写文件内容描述！
 */
import {eSelectionListId, eMoveType, eCtxOptionType} from "@/utils/enum";
import * as _ from 'lodash';
import { Avatar } from "antd";
import avatar from "@/assets/images/avatar.png"
import noAvatar from "@/assets/images/noAvatar.png"

// 后端返回的默认值
export const DEFAULT_VALUE = -999;

// 判空
export function isEmpty(value) {
  let flg = false;
  if (Object.prototype.toString.call(value) === "[object Array]") {
    flg = value.length === 0 ? true : false;
  } else if (Object.prototype.toString.call(value) === "[object Object]") {
    flg = Object.keys(value).length === 0 ? true : false;
  } else if (Object.prototype.toString.call(value) === "[object String]") {
    flg = value.replace("/s", "").length === 0 ? true : false;
  } else if (Object.prototype.toString.call(value) === "[object Number]") {
    flg = (isNaN(value) || value == DEFAULT_VALUE) ? true : false; // 后端返回的默认值 -999 
  } else if (Object.prototype.toString.call(value) === "[object Null]") {
    flg = true;
  } else if (Object.prototype.toString.call(value) === "[object Undefined]") {
    flg = true;
  } else if (Object.prototype.toString.call(value) === "[object Boolean]") {
    flg = value ? false : true;
  } else if (Object.prototype.toString.call(value) === "[object Map]") {
    // 判断map是否为空
    flg = value === null || value.size === 0 ;
  }
  return flg;
}

// 判断数字是否是初始值
export function isInitNum (value) {
  return value === -999 || isEmpty(value);
}

// 判断是否是数组
export function isArray(obj) {
  return Object.prototype.toString.call(obj) === "[object Array]";
}

// 判断是否是对象
export function isObject(obj) {
  return Object.prototype.toString.call(obj) === "[object Object]";
}

// 俩个数组取交集，mainList是主数据
export const intersection = (mainList, subList, key) => {
  if (isEmpty(mainList) || isEmpty(subList)) {
    return [];
  }
  let arr = subList.map((item) => item[key]);
  let newArr3 = mainList.filter((info) => {
    return new Set(arr).has(info[key]);
  });
  return newArr3;
};

//数组去重，mainList中去除和subList的key相同的数据
export const deduplication = (mainList, subList, key) => {
  let newArr3 = mainList.filter(function (item1) {
    return subList.every(function (item2) {
      return item2[key] !== item1[key];
    });
  });
  return newArr3;
};

/**
 * 匹配407列表数据,获取propValue
 * @param list 数据
 * @param _id  selectionId
 * @param type propType
 */
export function getPropValueByIdType(list, _id, type) {
  return (
    (list || []).find((item) => {
      return item.selectionId == _id && item.propType == type;
    }) || { propValue: "" }
  ).propValue;
}
/**
 * 匹配407列表数据,获取list
 * @param list 数据
 * @param code selectionId
 */
export function getCodeValueListByCode(list, code) {
  return (list || []).filter((item) => {
    return item.selectionId == code;
  });
}
/**
 * 匹配202列表数据,获取userName
 * @param list 数据
 * @param _id  userId
 */
export function getUserNameById(list, _id) {
  return (
    (list || []).find((item) => {
      return item.userId == _id;
    }) || { userName: "" }
  ).userName;
}

/**
 * 获取用户数据数组
 * @param {*} userList
 * @returns
 */
export const getUserList = (userList = []) => {
  return userList.map((item, index) => {
    if (item.userId) {
      item.propType = item.userId.toString();
    }
    item.propValue = item.userName;
    return item;
  });
};

/**
 * 获取用户名称Select类型
 * @param {*} userList
 * @returns
 */
export const getUserListSelect = (userList = []) => {
  return (userList || []).map((item, index) => {
    if (item.userId) {
      item.icon = <Avatar src={item.avatar ? item.avatar : item.userName ? avatar : noAvatar} size={24}/>
      item.value = item.userId.toString();
      item.key = item.userId.toString();
    }
    item.label = item.userName;
    return item;
  });
};

/**
 * 资源列表
 * @param {*} data 407资源列表数据
 * @param {*} needAllType 是否需要全部的资源类型: 默认false
 * @param {*} otherList 额外附加的List: 消息模块
 * @returns
 */
export const getResTypeList = (data = [], needAllType = false, selectionId = eSelectionListId.Selection_1921_Res_ObjType, otherList) => {
  let selectionList = data.filter((item) => (item.selectionId == selectionId && (needAllType || item.propType != "-1" )) );
  if(otherList){
    selectionList = [...otherList, ...selectionList];
  }
  selectionList = (selectionList || []).map((item) => ({
    key: item.propType.toString(),
    value: item.propType.toString(),
    label: item.propValue.toString(),
  }));
  console.log("getResTypeList-selectionList", selectionList);
  return selectionList;
};

/**
 * 空间列表
 * @param {*} data 407资源列表数据
 * @returns
 */
export const getSpacesList = (spaceList = [], teamId) => {
  let _spaceList = spaceList.filter((item) => item.id != teamId);
  return (_spaceList || []).map((item) => ({
    key: item.id?.toString(),
    value: item.id?.toString(),
    label: item.name?.toString(),
  }));
};

//比较两个对象是否相等
export const compareObj = (obj, newObj) => {
  return _.isEqual(obj, newObj);
};

//比较两个数组是否相等
export const compareArr = (arr, newArr) => {
  return _.isEqual(arr, newArr);
};

// 编辑器@UserName
export const getEditorUserList = (data) => {
  return data.map((item) => ({
    key: item.userName,
    value: item.userName,
    id: item.userId,
  }));
};

// 时间格式化 dhx-schduler 日期格式化，已废弃， 更改为 scheduler.templates.format_date
export function dateFormat(fmt) {
  var o = {
    "M+": this.getMonth() + 1, //月份
    "D+": this.getDate(), //日
    "d+": this.getDate(), //日
    "H+": this.getHours(), //小时
    "m+": this.getMinutes(), //分
    "s+": this.getSeconds(), //秒
    "q+": Math.floor((this.getMonth() + 3) / 3), //季度
    S: this.getMilliseconds(), //毫秒
  };
  if (/(y+|Y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (var k in o) if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
  return fmt;
}


/**
 * 复制文本到剪切板中
 *
 * @export
 * @param {*} value 需要复制的文本
 * @param {*} cb 复制成功后的回调
 */
export function copyToClip(value, cb) {
  // 动态创建 textarea 标签
  const textarea = document.createElement("textarea");
  // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
  textarea.readOnly = "readonly";
  textarea.style.position = "absolute";
  textarea.style.left = "-9999px";
  // 将要 copy 的值赋给 textarea 标签的 value 属性
  textarea.value = value;
  // 将 textarea 插入到 body 中
  document.body.appendChild(textarea);
  // 选中值并复制
  textarea.select();
  textarea.setSelectionRange(0, textarea.value.length);
  document.execCommand("Copy");
  document.body.removeChild(textarea);
  if (cb && Object.prototype.toString.call(cb) === "[object Function]") {
    cb();
  }
}

/**
 * @description 根据子节点获取父节点key (Tree形式)
 * @param {String} rootNode Tree数组
 * @param {Array} key 选中的节点, 可能为objId或者nodeId
 * @param {Array} includeOwn 是否包含自身节点，默认false
 * @returns 
 */

 export function getParentsKeysBykeyInTree(rootNode, key, includeOwn = false) {
  for (let i in rootNode) {
    if (rootNode[i].key == key) {
      return includeOwn ? [rootNode[i].key] : [];  // 是否返回自身节点信息
    }

    if (rootNode[i].children) {
      let node = getParentsKeysBykeyInTree(rootNode[i].children, key, includeOwn);
      if (node !== undefined) {
        return node.concat(rootNode[i].key);  //查询到把父节点连起来
      }
    }
  }
}

/**
 * @description 根据子节点获取父节点信息
 * @param {String} key 叶子节点
 * @param {Array} arr 需要展开的节点
 * @param {Array} list 数据源
 * @returns
 */

 export function getParentsNodeTypeById(list, key) {
  for (let i in list) {
    if (list[i].key == key) {
      //查询到就返回该数组对象,顶级节点，父节点为空
      return [];
    }

    if (list[i].children) {
      let node = getParentsNodeTypeById(list[i].children, key);
      if (node !== undefined) {
        //查询到把父节点连起来
        return node.concat({
          key: list[i].key,
          nodeType: list[i].nodeType,
        });
      }
    }
  }
}

// 是否是日期
export function isDate(str){
   return isNaN(str) && !isNaN(Date.parse(str))
}

// 获取选中的值
export const getSelectKeys = (pathname) => {
  try {
    let currentSelectId = pathname.split("/")[2].split("-")[1]
    return currentSelectId;
  } catch (error) {
    return null;
  }
}


/**
 * 时间范围是否在多少天内
 * @param {Object} startTime 开始时间
 * @param {Object} endTime   结束时间
 * @param {Object} compDay   是否在x天内
 * 
 * 使用方法
 * checkDate('2022-10-23 00:00:00', '2022-10-24 00:00:01', 1);
 * checkDate('2022-10-23', '2022-10-24', 1);
 */
 export function checkDate(startTime, endTime, compDay) {
  if (startTime == "" || startTime == null || startTime == undefined) {
      console.log("开始时间为空，请检查！");
      return false;
  }
  if (endTime == "" || endTime == null || endTime == undefined) {
      console.log("结束时间为空，请检查！");
      return false;
  }
  var data1 = Date.parse(startTime.replace(/-/g, "/"));
  var data2 = Date.parse(endTime.replace(/-/g, "/"));
  var datadiff = data2 - data1;
  var time = parseInt(compDay) * (60 * 60 * 24 * 1000);
  if (datadiff < 0) {
      console.log("开始时间应小于结束时间");
      return false;
  }
  if (datadiff > time) {
      console.log("时间间隔大于" + parseInt(compDay) + "天");
      return false;
  }
  return true;
}


/**
 * JS 计算两个时间间隔多久（时分秒）
 * @param startTime "2022-07-18 14:40:52"
 * @param endTime "2022-07-18 10:55:37"
 * @return 3时45分15秒
 */
 export function twoTimeInterval(startTime, endTime) {
  try {
    if(!startTime || !endTime){
      return;
    }
    // 开始时间
    let d1 = startTime.replace(/\-/g, "/");
    let date1 = new Date(d1);

    // 结束时间
    let d2 = endTime.replace(/\-/g, "/");
    let date2 = new Date(d2);

    // 时间相差秒数
    let dateDiff = date2.getTime() - date1.getTime();

    // 计算出相差天数
    let days = Math.floor(dateDiff / (24 * 3600 * 1000));

    // 计算出小时数
    let residue1 = dateDiff % (24 * 3600 * 1000); // 计算天数后剩余的毫秒数
    let hours = Math.floor(residue1 / (3600 * 1000));

    // 计算相差分钟数
    let residue2 = residue1 % (3600 * 1000); // 计算小时数后剩余的毫秒数
    let minutes = Math.floor(residue2 / (60 * 1000));

    // 计算相差秒数
    let residue3 = residue2 % (60 * 1000); // 计算分钟数后剩余的毫秒数
    let seconds = Math.round(residue3 / 1000);

    let returnVal =
      ((days == 0) ? "" : days + "天") +
      ((hours == 0) ? "" : hours + "时") +
      ((minutes == 0) ? "" : minutes + "分") +
      ((seconds == 0) ?  "" : seconds + "秒"); 

    if(!returnVal){  // 0秒特殊处理，显示为1秒 ，避免为空
      returnVal = "1秒"
    }

    return returnVal;
  } catch (e) {
    console.error("twoTimeInterval", e);
    return 0;
  }
}

 // 判断是否跨天
export const checkIsMultiDay = (beginDt, dueDt) => {
  if(isEmpty(beginDt) || isEmpty(dueDt)){
    return false
  }
  return new Date(beginDt).getDate() !== new Date(dueDt).getDate();
}

// tree扁平化 
export function treeToArray(tree) {
  return tree.reduce((pre, cur) => {
    const { children, ...i } = cur
    return pre.concat(i, children && children.length ? treeToArray(children) : [])
  }, [])
}

// 数组转tree 
export function arrayToTree(array) {
  // const 
}

export function getAttrPropByType(propList=[],type){
  return propList.find(itemx => itemx.propType.toString() == type.toString()) || {};
}

// 将searchQuery转换为params
export function transformSearhQueryParams (searchQuery){
  let params = {};
  if(!isEmpty(searchQuery)){
    searchQuery.forEach(search => {
      params[search.col] = search.begin;
    });
  }
  return params;
}

/**
 * 单层List上移下移, 备注：第一个上移会移动到最后一个
 */
export const handleMoveAction = (data, record, moveType) => {
  const _data = [...data];
  const index = _data.findIndex((item) => item.key == record.key);
  if (moveType == eMoveType.eMoveUp) { // 上移
    if (index != 0) {
      _data[index] = _data.splice(index - 1, 1, _data[index])[0];
    } else {
      _data.push(_data.shift());
    }
  } else if (moveType == eMoveType.eMoveDown) { // 下移
    if (index != _data.length - 1) {
      _data[index] = _data.splice(index + 1, 1, _data[index])[0];
    } else {
      _data.unshift(_data.splice(index, 1)[0]);
    }
  }
  return _data;
};

// 系统自定义图标
export const getSysIconList = (selectionList = [])=>{
  let iconOptionList = selectionList.filter(el => el.selectionId == eSelectionListId.Selection_1939_Icon) || []
  return iconOptionList;
 }

// 新建资源分组
export const assembleGroup = (list, _actionTypeFlg) => {
  let _list = list.reduce((pre, current, index) => {
    if(!!current.groupName){ // groupName为空，直接认为是主节点，没有分组
      const groupIndex = pre.findIndex(_pre => _pre.name == current.groupName)
      if (groupIndex == -1) {
        const group = {
          id: "",
          parentId: current.parentId,
          name: current.groupName,
          nameSuffix: "",
          groupName: "",
          actionType: _actionTypeFlg ? current.actionType : "", // 新建资源来说，actionType是nodeType，没有用处， 个性化设置的actionType有用
          privType: "",
          seqNo: "",
          type: eCtxOptionType.eGroup,
          disabled: true,
          children : [current],
        };
        pre.push(group);
      } else {
        pre[groupIndex].children.push(current);
      }
    } else {
      pre.push({...current, type: eCtxOptionType.eGroup,}); // 分组，但是可点击
    }
    return pre;
  }, []);
  return _list;
}