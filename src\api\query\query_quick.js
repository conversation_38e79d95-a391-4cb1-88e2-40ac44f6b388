import { team_107_get_query_detail } from '../http_quick';

// issue模块使用，待校验能否合并
export const team_107_get_query_detail_query = (teamId, nodeId) => ({
  queryKey: ['team_107_get_query_detail_issue', teamId, nodeId], // queryKey 和 search模块区分开，逻辑有区分，考虑合并
  queryFn: async () => {
    let result = await team_107_get_query_detail({ teamId, nodeId })
    if (result.resultCode == 200) {
      let _criteriaList = [];
      result.criterias.forEach(_criteria => {
        // 判断_criteriaList是否已有相同attrNid的数据
        let findItemIndex = _criteriaList.findIndex(el => el.col == _criteria.attrNid)
        // 如果有，则不用再次push，直接用当前index的数据进行处理；日期处理形式（大于等于， 小于等于）
        if (findItemIndex >= 0) {
          if (_criteria.attrValueParse && _criteria.relOpType == 6) {
            // 如果有区间relOpType == 6，》=则用end属性存储，不考虑存在多个条件
            _criteriaList[findItemIndex].end = _criteria.attrValueParse
          } else if (_criteria.attrValueParse && _criteria.relOpType == 4) {
            // 如果有区间relOpType == 4，《= 则用begin属性存储，不考虑存在多个条件
            _criteriaList[findItemIndex].begin = _criteria.attrValueParse
          } else {
            // 考虑有多条件，使用toString()确保被转换的数据为字符串
            _criteriaList[findItemIndex].begin = _criteria.attrValueParse?.toString().split(",") //将用逗号隔开的字符串，转为数组
          }
        } else {
          let criteriaUi = { col: _criteria.attrNid };
          if (_criteria.attrValueParse && _criteria.relOpType == 6) {
            criteriaUi.end = _criteria.attrValueParse
          } else if (_criteria.attrValueParse && _criteria.relOpType == 4) {
            criteriaUi.begin = _criteria.attrValueParse
          } else {
            criteriaUi.begin = _criteria.attrValueParse?.toString().split(",")
          }
          _criteriaList.push(criteriaUi)
        }
      });
      return _criteriaList;
    }
  }
})

