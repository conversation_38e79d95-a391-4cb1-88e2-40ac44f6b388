import React, { useEffect, useState } from "react";
import {Box, Typography, Button} from "@mui/material";
import "./Cust_Part_8_Footer.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../utils/commonUtils";
import { formatVariantColor } from "../../../utils/muiThemeUtils";

export default function Cust_Part_8_Footer({data, openApplyDrawer, openContactUsModal}) {
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  }, [data?.widgetList]);
  return (
    <>
      {/* iTeam与您携手共赢未来-start */}
      <Box className="bottom-iTeam-custom" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography variant="h5" style={{ color: "white" }}>{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
        <Box className="bottom-iTeam-custom-btn">
          {filterWidgetList(moduleData, 4).map((item, index) => (
            <Button 
              key={item.widgetId}
              variant={formatVariantColor(item.style)?.variant}
              color={formatVariantColor(item.style)?.color}
              onClick={item.linkUrl === "cooperate" ? () => {} : item.value == '申请合作' ? openApplyDrawer : openContactUsModal}
            >
              {item.value}
            </Button>
          ))}
        </Box>
      </Box>
      {/* iTeam与您携手共赢未来-end */}
    </>
  );
}
