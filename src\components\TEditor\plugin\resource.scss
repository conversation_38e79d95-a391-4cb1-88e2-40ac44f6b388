.tribute-container li.no-match {
  cursor: default;
}

// mention block
.fr-tribute {
  display: inline-block;
  background: #EBECF0;
  padding: 1px 7px 3px 4px;
  border-radius: 0.5em;
  border: 0;
  line-height: 1em;
  white-space: nowrap;
  color: #42526E;
  line-height: 14px;
  cursor: pointer;
}

.fr-tribute.fr-tribute-active{
  background-color: #0052CC;
  color: #FFFFFF;
}

.tribute-container{
  z-index: 10001!important;
  position: absolute;
  top: 0;
  left: 0;
  height: auto;
  max-height: 300px;
  max-width: 500px;
  overflow: auto;
  display: block;
  z-index: 999999;

  -moz-box-shadow: 0 3px 6px #00000033;
  -webkit-box-shadow: 0 3px 6px #00000033;

  width: 248px;
  box-shadow: rgb(0 0 0 / 10%) 0px 2px 16px 0px;
  background-color: rgb(255, 255, 255);
  &::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }

  &::-webkit-scrollbar-track {
    // background-color: #f5f5f5;
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #00000024;
    border-radius: 10px;
  }
  .tribute-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
    overflow: hidden;
    .tribute-header{
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 36px;
      width: 36px;
      .tribute-header-icon{
        font-size: 24px;
        line-height: 24px;
        color: #C3C4C6;
      }
      img{
        display: inline-block;
        width: 30px;
        height: 30px;
        border-radius: 50%;
      }
    } 
    .tribute-content{
      box-sizing: border-box;
      margin-left: 8px;
      vertical-align: top;
      flex: 1;
      overflow: hidden;
      .tribute-content-name{
        color: #41464b;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .tribute-content-describe{
        color: #a5a5a5;
        font-size: 12px;
      }
    }
  }
}

.tribute-container > ul {
  margin: 0;
  padding: 0;
  list-style: none;
  width: 100%;
}

.tribute-container > li {
  cursor: pointer;
  width: 100%;
}

.tribute-container>ul>li:first {
  margin-top: 5px;
}

.tribute-container>ul>li[data-index] {
  height: 50px;
  padding: 7px 15px;
  box-sizing: border-box;
  line-height: 1.5;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  background: transparent;
  opacity: 1;
  cursor: pointer;
}

.tribute-container>ul>li.highlight[data-index] {
  background: #f7f7f7;
}

.tribute-item-group {
  height: 18px;
  line-height: 18px;
  font-weight: 500;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  color: rgb(165, 165, 165);
  padding-left: 15px;
  padding-right: 15px;
  margin-top: 5px;
}


