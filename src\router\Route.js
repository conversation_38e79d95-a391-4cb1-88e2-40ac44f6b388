import React, { lazy } from 'react';
import issueRouters from '../views/issueTrack/routerConfig/RouterConfig'
import { Navigate } from 'react-router-dom';

const Home = lazy(()=>import("../views/home")); // 首页
const VersionHistory = lazy(()=>import("../views/versionHistory")); // 版本历史
const Price = lazy(()=>import("../views/price")); // 价格
const HelpCenter = lazy(()=>import("../views/helpCenter")); // 帮助中心
const HelpCenterDetail = lazy(()=>import("../views/helpCenter/HelpCenterDetail")); // 帮助中心-详情
const WorkOrder = lazy(()=>import("../views/workOrder")); // 工单问答
const AllProducts = lazy(()=>import("../views/allProducts")); // 更多产品
const ProductsDetail = lazy(()=>import("../views/allProducts/productsDetail")); // 产品详情
const CanvassBusiness = lazy(()=>import("../views/cooperate/canvassBusiness")); // 招商合作
const CustomMade = lazy(()=>import("../views/cooperate/customMade")); // 定制合作
const DemoTeam = lazy(()=>import("../views/DemoTeam")); // 在线演示
const TurnOsLogin = lazy(()=>import("../views/turnOsLogin")); // 登录
const HelpVideo = lazy(()=>import("../views/HelpVideo")); // 帮助视频
const HelpVideoDetail = lazy(()=>import("../views/HelpVideo/detail")); // 帮助视频详情

export default [
  {path:"demo", element: DemoTeam, meta: {title: "在线演示"}},
  {path:"customize", element: CustomMade, meta: {title: "定制合作"}},
  {path:"partner", element: CanvassBusiness, meta: {title: "招商合作"}},
  {path:"product/:productId", element: ProductsDetail, meta: {title: "产品单页"}},
  {path:"allproducts", element: AllProducts, meta: {title: "全部产品"}},
  {path:"version", element: VersionHistory, meta: {title: "版本历史"}},
  {path:"price", element: Price, meta: {title: "价格"},},
  {path:"help/doc", element: HelpCenter, meta: {title: "帮助中心"},children:[
    {path:":nodeId", element: HelpCenterDetail}
  ]},
  // {path:"help/video", element: HelpCenter, meta: {title: "视频介绍"},children:[
  //   {path:":nodeId", element: HelpCenterDetail}
  // ]},
  {path:"help/software", element: HelpCenter, meta: {title: "软件下载"},children:[
    {path:":nodeId", element: HelpCenterDetail}
  ]},
  {path:"help/os", element: HelpCenter, meta: {title: "操作指引"},children:[
    {path:":nodeId", element: HelpCenterDetail}
  ]},
  {path:"help/video", element: HelpVideo, meta: {title: "帮助视频"}},
  {path:"help/video/:nodeId", element: HelpVideoDetail, meta: {title: "帮助视频详情"}},
  {path:"workorder", element: WorkOrder, children:issueRouters, meta:{title: "系统工单"}},
  {path:"login", element: TurnOsLogin},
  {path:"", element: Home, meta:{title: "首页"}},
  {path:"*", element: () => <Navigate replace to="/"/>, meta:{title: "首页"}},
]

