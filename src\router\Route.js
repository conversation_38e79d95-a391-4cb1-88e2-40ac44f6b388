import React, { lazy } from 'react';
import issueRouters from '../views/issueTrack/routerConfig/RouterConfig'
import { Navigate } from 'react-router-dom';

const Home = lazy(()=>import("../views/HomePage")); // 首页
const VersionHistory = lazy(()=>import("../views/Versions")); // 版本历史
const Price = lazy(()=>import("../views/price/PriceHome")); // 价格
const HelpCenter = lazy(()=>import("@/views/helpDoc/HelpDocHome")); // 帮助中心
const DocDetail = lazy(()=>import("@/views/helpDoc/DocDetail")); // 帮助中心-详情
const WorkOrder = lazy(()=>import("../views/WorkOrder")); // 工单问答
const AllProducts = lazy(()=>import("@/views/productIntro/_obsolete/AllProdsPage")); // 更多产品
const ProductsDetail = lazy(()=>import("@/views/productIntro/ProductDetailPage")); // 产品详情
const Partner = lazy(()=>import("../views/cooperate/canvassBusiness/CooperatePage")); // 招商合作
const Customize = lazy(()=>import("../views/cooperate/customMade/CustomizationPage")); // 定制合作
const DemoTeam = lazy(()=>import("../views/DemoTeam")); // 在线演示
const osLogin = lazy(()=>import("../views/OsLogin")); // 登录
const HelpVideo = lazy(()=>import("../views/HelpVideo/VideosHome")); // 帮助视频
const HelpVideoDetail = lazy(()=>import("../views/HelpVideo/VideoDetail")); // 帮助视频详情

export default [
  {path:"demo", element: DemoTeam, meta: {title: "在线演示"}},
  {path:"customize", element: Customize, meta: {title: "定制合作"}},
  {path:"partner", element: Partner, meta: {title: "招商合作"}},
  {path:"product/:productId", element: ProductsDetail, meta: {title: "产品单页"}},
  {path:"allproducts", element: AllProducts, meta: {title: "全部产品"}}, //不怎么使用，页面不美观，有dhx组件痕迹 20250801 Jim Song
  {path:"version", element: VersionHistory, meta: {title: "版本历史"}},
  {path:"price", element: Price, meta: {title: "价格"},},
  {path:"help/doc", element: HelpCenter, meta: {title: "帮助中心"},children:[
    {path:":nodeId", element: DocDetail}
  ]},
  // {path:"help/video", element: HelpCenter, meta: {title: "视频介绍"},children:[
  //   {path:":nodeId", element: HelpCenterDetail}
  // ]},
  {path:"help/software", element: HelpCenter, meta: {title: "软件下载"},children:[
    {path:":nodeId", element: DocDetail}
  ]},
  {path:"help/os", element: HelpCenter, meta: {title: "操作指引"},children:[
    {path:":nodeId", element: DocDetail}
  ]},
  {path:"help/video", element: HelpVideo, meta: {title: "帮助视频"}}, //帮助视频功能也没有完善，不美观 20250801 Jim Song
  {path:"help/video/:nodeId", element: HelpVideoDetail, meta: {title: "帮助视频详情"}},
  {path:"workorder", element: WorkOrder, children:issueRouters, meta:{title: "系统工单"}},
  {path:"login", element: osLogin},
  {path:"", element: Home, meta:{title: "首页"}},
  {path:"*", element: () => <Navigate replace to="/"/>, meta:{title: "首页"}},
]

