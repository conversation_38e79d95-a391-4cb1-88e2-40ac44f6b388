.DemoTeam {
    height: 100vh;
}

.DemoTeam-TopMenuBar-box {
    position: fixed;
    top: 0;
    // left: 50%;
    // transform: translate(-50%, 0);
    margin: auto;
    padding: 0 20px;
    width: 100%;
    z-index: 10;
    background-color: white;
    box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
    width: 100vw;
    left: 0;
}

// 从模板开始标题
.DemoTeam-start-title {
    padding: 50px 0;
    text-align: center;
    background-color: #f5f5f5;
}

// 模板列表
.DemoTeam-list {
    display: flex;
    flex-wrap: wrap;
    padding: 40px 20px;
    margin: auto;
    width: 1260px;
    border-bottom: 1px solid #f2f2f2;
    gap: 40px;
}
.DemoTeam-list-li {
    display: flex;
    // margin-bottom: 40px;
    &-describe { // 描述
        width: 380px;
        &-bgbox {
            padding: 6px;
            border-radius: 5px;
            box-shadow: 0 4px 16px 0 rgba(77, 77, 77, 0.12), 0 1px 2px 0 rgba(77, 77, 77, 0.06);
        }
        &-bg {
            position: relative;
            height: 210px;
            border-radius: 5px;
            background-size: 100% 210px !important;
            .DemoTeam-broadcast-mask {
                visibility: hidden;
                position: absolute;
                left: 0;
                top: 0;
                z-index: 8;
                width: 100%;
                height: 100%;
                background-color: rgba(0 ,0 ,0 , .3);
            }
            .DemoTeam-broadcast {
                position: absolute;
                left: 50%;
                top: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                padding-left: 4px;
                width: 50px;
                height: 50px;
                font-size: 40px;
                color: white;
                background-color: #333;
                border-radius: 50%;
                transform: translate(-50%, -50%);
                z-index: 9;
                cursor: pointer;
                transition: all .5s 0s;
            }
        }
        &-title {
            padding: 20px 60px;
            text-align: center;
        }
    }
    &-table { // 表格
        .ant-table {
            &-container {
                .ant-table-content {
                    .ant-table-thead {
                        font-size: 12px;
                        .ant-table-cell {
                            padding: 6px;
                            background-color: #f2f2f2;
                        }
                    }
                    .ant-table-tbody {
                        font-size: 12px;
                        .ant-table-row {
                            .ant-table-cell {
                                padding: 6px;
                            }
                        }
                    }
                }
            }
        }
    }
}

// .DemoTeam-list-li:not(:nth-child(3n)) {
//     margin-right: 40px;
// }

.DemoTeam-list-li-Popover {
    padding-left: 0;
    padding-right: 0;
    .ant-popover-content {
        .ant-popover-inner {
            box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.2), 0 6px 16px 0 rgba(0, 0, 0, 0.2), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
            &-content {
                padding: 10px;
            }
        }
    }
}

.TextLineClamp {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}