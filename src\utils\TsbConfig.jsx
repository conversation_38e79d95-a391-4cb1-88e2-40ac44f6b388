// import bdboardImg from "@common/assets/images/bdboard.png";
// import ganntImg from "@common/assets/images/gannt.png";
// import issueImg from "@common/assets/images/issue.png";

export const eNodeTypeId = {
  nt_300_objtype_space:                   300,

  nt_302_objtype_folder:                  302,
  nt_302105_objtype_folder_label:         302105, // 标签文件夹
  nt_302106_objtype_folder_search:        302106, // 搜索文件夹
  nt_302107_objtype_folder_chart:         302107, // 报表文件夹
  nt_302109_objtype_folder_boards:        302109, // 仪表板文件夹
  nt_302312_objtype_folder_docs:          302312, // 文档库文件夹
  nt_302112_objtype_folder_docs:          302112, // 我的文档库文件夹
  nt_302316_objtype_folder_api:           302316, // api文件夹
  nt_302317_objtype_folder_issue:         302317, // 问题跟踪文件夹
  nt_30231703_objtype_folder_issue:       30231703, // 问题跟踪分区文件夹
  nt_302500_objtype_folder_cdb:           302500, // 在线数据库文件夹
  nt_302118_objtype_folder_my:            302118, // 我的文件夹

  nt_303_objtype_package:                 303,
  nt_311_objtype_doc:                     311,
  nt_312_objtype_tutorial:                312,
  nt_31201_objtype_tutorial_doc:          31201,
  nt_31401_objtype_diagramming_single:    31401,
  nt_314_objtype_diagramming:             314,
  nt_315_objtype_ide:                     315,
  nt_316_objtype_api_project:             316,
  nt_317_objtype_issue_project:           317,
  nt_501_objtype_cdb_mysql_conn:          501,
  nt_502_objtype_cdb_pgsql_conn:          502,
  nt_503_objtype_cdb_mssql_conn:          503,
  nt_334_objtype_task_batch:              334,
  nt_504_objtype_cdb_srv:                 504,
  //
  nt_0_team:                            0,
  nt_100_quick_access:                    100,
  nt_101_quick_access_dash:               101,
  nt_102_quick_access_recent_visit:       102,
  nt_103_quick_access_recent_update:      103,
  nt_104_quick_access_favorite:           104,
  nt_105_quick_access_tag:                105,
  nt_10501_quick_access_tag_single:                10501,
  nt_106_quick_access_query:              106,
  nt_10601_quick_access_query_signle:              10601,
  nt_107_quick_access_reporting:          107,
  nt_10701_quick_access_reporting_signle:          10701, 
  nt_108_quick_access_sys_subscription:   108,
  nt_109_quick_access_dashboard:        109,
  nt_10901_quick_access_dashboard_signle:   10901,
  nt_110_quick_access_work_hour:          110,
  nt_111_quick_access_watch:          111,
  nt_112_quick_access_docs:             112, // 我的文档库
  nt_113_objtype_task_todo	:           113,    // 日常任务
  nt_117_quick_access_team	:          117,
  nt_118_quick_access_my	:          118,
  nt_121_quick_access_share	:          121,
  nt_122_objtype_calendar_team	:           122, // 团队日程
  nt_200_ecommerce:                       200,
  nt_201_ecommerce_obj_lib:               201,
  nt_202_ecommerce_store:                 202,
  nt_20201_ecommerce_store_obj:             20201,
  nt_20202_ecommerce_mgmt:                  20202,
  nt_2020201_ecommerce_product:               2020201,
  nt_2020202_ecommerce_promotion:             2020202,
  nt_2020202_ecommerce_order:                 2020203,
  nt_2020204_ecommerce_payment:               2020204,
  nt_2020205_ecommerce_comment:               2020205,
  nt_2020206_ecommerce_customer_service:      2020206,
  nt_301_obj_tree:                        301,
  nt_304_objtype_shortcut:                304,
  nt_310_objtype_excel:                   310,
  nt_313_objtype_questiongrp_paper_exam	: 313,
  nt_330_objtype_task_todo	:             330,    // 日常任务
  nt_331_objtype_questiongrp:             331,    // 题库
  nt_332_objtype_paper:                   332,    // 试卷
  nt_333_objtype_exam:                    333,    // 考试
  nt_33101_objtype_question:              33101     , // 题目
  nt_31601_objtype_api_item:             31601,
  nt_31701_objtype_issue_project_dash:      31701,
  nt_31702_objtype_issue_project_default_grp: 31702,
  nt_31703_objtype_issue_project_adhoc_grp: 31703,
  nt_31704_objtype_issue_item:              31704,
  nt_31705_objtype_issue_list:              31705,
  nt_31706_objtype_issue_phasegrp:          31706,
  nt_31708_objtype_issue_querylist:         31708,
  nt_31709_objtype_issue_kanban:          31709,
  nt_318_objtype_gantt:                   318,
  nt_319_objtype_calendar:               319, // 空间下的日程
  nt_320_objtype_workhour_registration:           320, // 工时登记
  nt_119_objtype_workhour_statistics_report: 119, // 工时统计报表
  nt_321_project_manager:                 321, // 项目管理
  nt_401_objtype_mail_wkrpt:                401, // 邮件&报告
  nt_40101_objtype_mail_inbox:              40101, // 收件箱
  nt_40102_objtype_mail_sent:               40102,
  nt_40103_objtype_mail_flagging:           40103,
  nt_40104_objtype_mail_draft:              40104,
  nt_40105_objtype_mail_trash:              40105,
  nt_40107_objtype_mail_adhoc:              40107,
  nt_40108_objtype_mail_wkrpt_item:         40108, // 邮件&报告(单个)
  nt_500_objtype_cdb:                     500,
  nt_505_objtype_cdb_srv_acct:            505,
  nt_510_objtype_redis:                   510, // redis
  nt_511_objtype_ssh:                     511, // ssh
  nt_600_objtype_task_kpi:                600,
  nt_604_objtype_kpi:                     604,
  nt_700_objtype_cdisk:                   700,
  nt_888_objtype_shop:                    888, // 商铺
  nt_999_objtype_shortcut:                -999, // 快捷方式
};

/**
 * @description 菜单跳转路径
 */
export const eNodeType = {
  [eNodeTypeId.nt_0_team]:                            {nodeType: 0,icon:"tuandui",name:"主工作区",key:"nt_0_team",iconColor: ''},
  [eNodeTypeId.nt_100_quick_access]:                    {nodeType: 100,icon:"kuaijie",name:"快速访问",key:"nt_quick_access",iconColor: ''},
  // 桌面 nodeId 前端固定写死 8686868686，by walt from otis 2023-02-17
  [eNodeTypeId.nt_101_quick_access_dash]:               {nodeType: 101,icon:"shouyefill",name:"概览",key:"nt_quick_access_dash",iconColor: '#FD372E', nodeId: "8686868686"},
  [eNodeTypeId.nt_102_quick_access_recent_visit]:       {nodeType: 102,icon:"zuijinlaifang",name:"近期访问",key:"nt_quick_access_recent_visit",iconColor: ''},
  [eNodeTypeId.nt_103_quick_access_recent_update]:      {nodeType: 103,icon:"jianchagengxin",name:"近期更新",key:"nt_quick_access_recent_update",iconColor: ''},
  [eNodeTypeId.nt_104_quick_access_favorite]:           {nodeType: 104,icon:"shoucang1",name:"我的收藏",key:"nt_quick_access_favorite",iconColor: ''},
  [eNodeTypeId.nt_105_quick_access_tag]:                {nodeType: 105,icon:"tongytongbiaoqian",name:"标签",key:"nt_quick_access_tag",iconColor: ''},
  [eNodeTypeId.nt_10501_quick_access_tag_single]:       {nodeType: 10501,icon:"tongytongbiaoqian",name:"标签",key:"nt_10501_quick_access_tag_single",iconColor: ''},
  [eNodeTypeId.nt_106_quick_access_query]:              {nodeType: 106,icon:"wangyuanjing",name:"搜索",key:"nt_quick_access_query",iconColor: ''},
  [eNodeTypeId.nt_10601_quick_access_query_signle]:              {nodeType: 10601,icon:"wangyuanjing",name:"搜索",key:"nt_10601_quick_access_query_signle",iconColor: ''},
  [eNodeTypeId.nt_107_quick_access_reporting]:          {nodeType: 107,icon:"a-jieduanbeifen2",name:"报表",key:"nt_quick_access_reporting",iconColor: ''},
  [eNodeTypeId.nt_10701_quick_access_reporting_signle]: {nodeType: 10701,icon:"a-jieduanbeifen2",name:"报表", nameEn: "report", key:"nt_10701_quick_access_reporting_signle",iconColor: ''},
  [eNodeTypeId.nt_108_quick_access_sys_subscription]:   {nodeType: 108,icon:"dingyue1",name:"系统订阅",key:"nt_quick_access_sys_subscription",iconColor: ''},
  [eNodeTypeId.nt_109_quick_access_dashboard]:          {nodeType: 109,icon:"baobiao",name:"仪表板", nameEn: "dashboard", key:"nt_109_quick_access_dashboard",iconColor: ''},
  [eNodeTypeId.nt_10901_quick_access_dashboard_signle]:          {nodeType: 10901,icon:"baobiao",name:"仪表板",key:"nt_10901_quick_access_dashboard_signle",iconColor: ''},
  [eNodeTypeId.nt_110_quick_access_work_hour]:          {nodeType: 110,icon:"gongshitongji1",name:"我的工时",key:"nt_110_quick_access_work_hour",iconColor: ''},
  [eNodeTypeId.nt_111_quick_access_watch]:              {nodeType: 111,icon:"eye2",name:"我的关注",key:"nt_111_quick_access_watch",iconColor: ''},
  [eNodeTypeId.nt_112_quick_access_docs]:                {nodeType: 112,icon:"085shucezhongxin",name:"我的文档库", nameEn:"docs",key:"nt_112_quick_access_docs",iconColor: '', modulePath: "docs"},
  [eNodeTypeId.nt_113_objtype_task_todo]:              {nodeType: 113,icon:"renwu",name:"日常任务",key:"nt_113_objtype_task_todo",iconColor: ''},
  [eNodeTypeId.nt_117_quick_access_team]:              {nodeType: 117,icon:"dongtai_",name:"团队动态",key:"nt_117_quick_access_team",iconColor: ''},
  [eNodeTypeId.nt_118_quick_access_my]:              {nodeType: 118,icon:"gerendaiban",name:"我的",key:"nt_118_quick_access_my",iconColor: ''},
  [eNodeTypeId.nt_121_quick_access_share]:              {nodeType: 121,icon:"fenxiang2",name:"团队共享",key:"nt_121_quick_access_share",iconColor: ''},
  [eNodeTypeId.nt_200_ecommerce]:                       {nodeType: 200,icon:"goods-copy1",name:"资源商城",key:"nt_ecommerce",iconColor: ''},
  [eNodeTypeId.nt_201_ecommerce_obj_lib]:               {nodeType: 201,icon:"a-01_shangpinku",name:"资源素材库",key:"nt_ecommerce_obj_lib",iconColor: ''},
  [eNodeTypeId.nt_202_ecommerce_store]:                 {nodeType: 202,icon:"dianpujingxuan1",name:"我的店铺",key:"nt_ecommerce_store",iconColor: ''},
  [eNodeTypeId.nt_20201_ecommerce_store_obj]:             {nodeType: 20201,icon:"",name:"店铺资源",key:"nt_ecommerce_store_obj",iconColor: ''},
  [eNodeTypeId.nt_20202_ecommerce_mgmt]:                  {nodeType: 20202,icon:"",name:"费用中心",key:"nt_ecommerce_mgmt",iconColor: ''},
  [eNodeTypeId.nt_2020201_ecommerce_product]:               {nodeType: 2020201,icon:"",name:"商品管理",key:"nt_ecommerce_product",iconColor: ''},
  [eNodeTypeId.nt_2020202_ecommerce_promotion]:             {nodeType: 2020202,icon:"",name:"优惠券管理",key:"nt_ecommerce_promotion",iconColor: ''},
  [eNodeTypeId.nt_2020202_ecommerce_order]:                 {nodeType: 2020203,icon:"",name:"订单列表",key:"nt_ecommerce_order",iconColor: ''},
  [eNodeTypeId.nt_2020204_ecommerce_payment]:               {nodeType: 2020204,icon:"",name:"财务管理",key:"nt_ecommerce_payment",iconColor: ''},
  [eNodeTypeId.nt_2020205_ecommerce_comment]:               {nodeType: 2020205,icon:"",name:"评论管理",key:"nt_ecommerce_comment",iconColor: ''},
  [eNodeTypeId.nt_2020206_ecommerce_customer_service]:      {nodeType: 2020206,icon:"",name:"客服管理",key:"nt_ecommerce_customer_service",iconColor: ''},
  [eNodeTypeId.nt_300_objtype_space]:                   {nodeType: 300,icon:"kongjian",name:"空间",key:"nt_objtype_space",iconColor: ''},
  [eNodeTypeId.nt_301_obj_tree]:                        {nodeType: 301,icon:"tree_fill",name:"资源树",key:"nt_obj_tree",iconColor: ''},

  [eNodeTypeId.nt_302_objtype_folder]:                  {nodeType: 302,icon:"folder-fill",name:"文件夹",key:"nt_objtype_folder",iconColor: ''},
  [eNodeTypeId.nt_310_objtype_excel]:                   {nodeType: 310,icon:"file-excel",name:"在线Excel", nameEn:"excel", key:"nt_310_objtype_excel",iconColor: '', modulePath: "excel"},
  [eNodeTypeId.nt_302105_objtype_folder_label]:           {nodeType: 302105,icon:"folder-fill",name:"文件夹",key:"nt_302105_objtype_folder_search",iconColor: ''},
  [eNodeTypeId.nt_302106_objtype_folder_search]:           {nodeType: 302106,icon:"folder-fill",name:"文件夹",key:"nt_302106_objtype_folder_search",iconColor: ''},
  [eNodeTypeId.nt_302107_objtype_folder_chart]:           {nodeType: 302107,icon:"folder-fill",name:"文件夹",key:"nt_302107_objtype_folder_chart",iconColor: ''},
  [eNodeTypeId.nt_302109_objtype_folder_boards]:           {nodeType: 302109,icon:"folder-fill",name:"文件夹",key:"nt_302109_objtype_folder_boards",iconColor: ''},
  [eNodeTypeId.nt_302316_objtype_folder_api]:           {nodeType: 302316,icon:"folder-fill",name:"文件夹",key:"nt_302316_objtype_folder_api",iconColor: ''},
  [eNodeTypeId.nt_302317_objtype_folder_issue]:           {nodeType: 302317,icon:"folder-fill",name:"文件夹",key:"nt_302317_objtype_folder_issue",iconColor: ''},
  [eNodeTypeId.nt_30231703_objtype_folder_issue]:           {nodeType: 30231703,icon:"folder-fill",name:"文件夹",key:"nt_30231703_objtype_folder_issue",iconColor: ''},
  [eNodeTypeId.nt_302500_objtype_folder_cdb]:           {nodeType: 302500,icon:"folder-fill",name:"文件夹",key:"nt_302500_objtype_folder_issue",iconColor: ''},
  [eNodeTypeId.nt_302118_objtype_folder_my]:           {nodeType: 302118,icon:"folder-fill",name:"文件夹",key:"nt_302118_objtype_folder_my",iconColor: ''},
  
  [eNodeTypeId.nt_302312_objtype_folder_docs]:           {nodeType: 302312,icon:"folder-fill",name:"文件夹",key:"nt_302312_objtype_folder_docs",iconColor: ''},
  [eNodeTypeId.nt_302112_objtype_folder_docs]:           {nodeType: 302112,icon:"folder-fill",name:"文件夹",key:"nt_302112_objtype_folder_docs",iconColor: ''},
  [eNodeTypeId.nt_303_objtype_package]:                 {nodeType: 303,icon:"ziyuanbaoguanli-tianchong",name:"资源包",key:"nt_objtype_package",iconColor: ''},
  [eNodeTypeId.nt_304_objtype_shortcut]:                {nodeType: 304,icon:"",name:"快捷方式",key:"nt_objtype_shortcut",iconColor: ''},
  [eNodeTypeId.nt_311_objtype_doc]:                     {nodeType: 311,icon:"file-word-fill",name:"文档", nameEn:"doc", key:"nt_objtype_doc",iconColor: '', modulePath: "doc"},
  [eNodeTypeId.nt_312_objtype_tutorial]:                {nodeType: 312,icon:"085shucezhongxin",name:"文档库", nameEn:"docs",key:"nt_objtype_tutorial",iconColor: '', modulePath: "docs"},
  [eNodeTypeId.nt_31201_objtype_tutorial_doc]:          {nodeType: 31201,icon:"file-word-fill",name:"文档库-文档", nameEn:"docgrp", key:"nt_objtype_tutorial_doc",iconColor: '',  modulePath: "docs"},
  [eNodeTypeId.nt_313_objtype_questiongrp_paper_exam]	: {nodeType: 313,icon:"shitibeifen", name:"题库,试卷,考试", nameEn:"questions",key:"nt_objtype_questiongrp_paper_exam",iconColor: '#EB3727'},
  [eNodeTypeId.nt_33101_objtype_question]	:             {nodeType: 33101,icon:"shitibeifen", name:"题目", nameEn:"question",key:"nt_objtype_questiongrp_paper_exam",iconColor: '#EB3727'},
  [eNodeTypeId.nt_331_objtype_questiongrp]:             {nodeType: 331,icon:"shitibeifen",name:"题库",key:"nt_objtype_questiongrp",iconColor: '#EB3727'},
  [eNodeTypeId.nt_332_objtype_paper]:                   {nodeType: 332,icon:"a-shijuanbeifen2",name:"试卷",key:"nt_objtype_paper",iconColor: '#EB3727'},
  [eNodeTypeId.nt_314_objtype_diagramming]:             {nodeType: 314,icon:"a-morenwenjianjiabeifen3",name:"在线作图",key:"nt_314_objtype_diagramming",iconColor: '#F7932A'},
  [eNodeTypeId.nt_31401_objtype_diagramming_single]:    {nodeType: 31401,icon:"a-morenwenjianjiabeifen3",name:"在线作图", nameEn: "diagram",key:"nt_31401_objtype_diagramming_single",iconColor: '#F7932A'},
  [eNodeTypeId.nt_315_objtype_ide]:                     {nodeType: 315,icon:"a-morenwenjianjiabeifen6",name:"在线运行", nameEn:"code_online", key:"nt_objtype_ide",iconColor: '#53B8D2'},
  [eNodeTypeId.nt_316_objtype_api_project]:             {nodeType: 316,icon:"APIjiekouguanli",name:"接口测试",key:"nt_objtype_api_project",iconColor: '#9173E8'},
  [eNodeTypeId.nt_31601_objtype_api_item]:             {nodeType: 31601,icon:"APIjiekouguanli",name:"接口", nameEn:"apiMock", key:"nt_objtype_api_item", iconColor: '#9173E8',  modulePath: 'apimock', },
  [eNodeTypeId.nt_317_objtype_issue_project]:           {nodeType: 317,icon:"bug", name:"问题跟踪项目", key:"nt_objtype_issue_project",iconColor: '#3279fe', modulePath:'issues'},
  [eNodeTypeId.nt_31701_objtype_issue_project_dash]:      {nodeType: 31701,icon:"gailan",name:"issue项目概览",key:"nt_objtype_issue_project_dash",iconColor: ''},
  [eNodeTypeId.nt_31702_objtype_issue_project_default_grp]: {nodeType: 31702,icon:"a-morenwenjianjiabeifen7",name:"issue默认类型",key:"nt_objtype_issue_project_default_grp",iconColor: ''},
  [eNodeTypeId.nt_31703_objtype_issue_project_adhoc_grp]: {nodeType: 31703,icon:"tuceng",name:"issue自定义分区",key:"nt_objtype_issue_project_adhoc_grp",iconColor: ''},
  [eNodeTypeId.nt_31704_objtype_issue_item]:              {nodeType: 31704,icon:"a-morenwenjianjiabeifen7",name:"问题", nameEn:"issue", key:"nt_objtype_issue_item",iconColor: '', modulePath: 'issue'},
  [eNodeTypeId.nt_31705_objtype_issue_list]:              {nodeType: 31705,icon:"issueliebiao",name:"问题列表",key:"nt_objtype_issue_list",iconColor: ''},
  [eNodeTypeId.nt_31706_objtype_issue_phasegrp]:          {nodeType: 31706,icon:"renshengjieduan",name:"问题阶段",key:"nt_objtype_issue_phasegrp",iconColor: ''},
  [eNodeTypeId.nt_31708_objtype_issue_querylist]:         {nodeType: 31708,icon:"a-issuesousuo",name:"问题搜索条件",key:"nt_objtype_issue_querylist",iconColor: ''},
  [eNodeTypeId.nt_31709_objtype_issue_kanban]:         {nodeType: 31709,icon:"igw-f-dashboard-rectangle",name:"看板",key:"nt_objtype_issue_kanban",iconColor: ''},
  [eNodeTypeId.nt_318_objtype_gantt]:                   {nodeType: 318,icon:"gantetubeifen",name:"甘特图", nameEn:"gannt", key:"nt_objtype_gantt",iconColor: ''},
  [eNodeTypeId.nt_319_objtype_calendar]:               {nodeType: 319,icon:"fenghuangxiangmutubiao_richeng",name:"日程",key:"nt_objtype_scheduler",iconColor: ''}, 
  [eNodeTypeId.nt_122_objtype_calendar_team]:               {nodeType: 122,icon:"fenghuangxiangmutubiao_richeng",name:"日程",key:"nt_122_objtype_calendar_team",iconColor: ''},
  [eNodeTypeId.nt_320_objtype_workhour_registration]:               {nodeType: 320,icon:"gongshitongji1",name:"工时登记",key:"nt_objtype_workhour_registration",iconColor: ''},
  [eNodeTypeId.nt_321_project_manager]:               {nodeType: 321,icon:"xiangmuguanli",name:"项目管理",key:"nt_321_project_manager",iconColor: ''},
  [eNodeTypeId.nt_119_objtype_workhour_statistics_report]:               {nodeType: 119,icon:"gongshitongjibaobiao",name:"工时统计报表",key:"nt_objtype_workhour_statistics_report",iconColor: ''},
  [eNodeTypeId.nt_401_objtype_mail_wkrpt]:              {nodeType: 401,icon:"htmal5icon05",name:"站内信/报告",key:"nt_objtype_mail_wkrpt",iconColor: ''},
  [eNodeTypeId.nt_40101_objtype_mail_inbox]:              {nodeType: 40101,icon:"shoujianxiangbeifen",name:"收件箱",key:"nt_objtype_mail_inbox",iconColor: ''},
  [eNodeTypeId.nt_40102_objtype_mail_sent]:               {nodeType: 40102,icon:"fajianxiangbeifen",name:"发件箱",key:"nt_objtype_mail_sent",iconColor: ''},
  [eNodeTypeId.nt_40103_objtype_mail_flagging]:           {nodeType: 40103,icon:"biaojixinxiangbeifen",name:"标记邮件",key:"nt_objtype_mail_flagging",iconColor: ''},
  [eNodeTypeId.nt_40104_objtype_mail_draft]:              {nodeType: 40104,icon:"a-caogaoxiangbeifen2",name:"草稿箱",key:"nt_objtype_mail_draft",iconColor: ''},
  [eNodeTypeId.nt_40105_objtype_mail_trash]:              {nodeType: 40105,icon:"shanchu2",name:"垃圾箱",key:"nt_objtype_mail_trash",iconColor: ''},
  [eNodeTypeId.nt_40107_objtype_mail_adhoc]:              {nodeType: 40107,icon:"",name:"自定义类型",key:"nt_objtype_mail_adhoc",iconColor: ''},
  [eNodeTypeId.nt_40108_objtype_mail_wkrpt_item]:         {nodeType: 40108,icon:"htmal5icon05",name:"站内信/报告", nameEn: "reports", key:"nt_objtype_mail_adhoc",iconColor: ''},
  [eNodeTypeId.nt_500_objtype_cdb]:                     {nodeType: 500,icon:"database-full",name:"在线数据库",key:"nt_objtype_cdb",iconColor: '#3279fe'},
  [eNodeTypeId.nt_501_objtype_cdb_mysql_conn]:          {nodeType: 501,icon:"mysqlyunshujukuMySQLban",name:"mysql数据库连接",key:"nt_objtype_cdb_mysql_conn",iconColor: ''},
  [eNodeTypeId.nt_502_objtype_cdb_pgsql_conn]:          {nodeType: 502,icon:"pgsql",name:"pgsql数据库连接",key:"nt_objtype_cdb_pgsql_conn",iconColor: ''},
  [eNodeTypeId.nt_503_objtype_cdb_mssql_conn]:          {nodeType: 503,icon:"sql-server",name:"mssql数据库连接",key:"nt_objtype_cdb_mssql_conn",iconColor: ''},
  [eNodeTypeId.nt_504_objtype_cdb_srv]:                 {nodeType: 504,icon:"shujukubeifen",name:"数据库",key:"nt_objtype_cdb_srv",iconColor: '#3279fe'},
  [eNodeTypeId.nt_505_objtype_cdb_srv_acct]:            {nodeType: 505,icon:"tianjiayonghu-tianchong",name:"数据库账号",key:"nt_objtype_cdb_srv_acct",iconColor: ''},
  [eNodeTypeId.nt_510_objtype_redis]:                   {nodeType: 510,icon:"redis",name:"redis", nameEn: "redis", key:"nt_510_objtype_redis",iconColor: ''},
  [eNodeTypeId.nt_511_objtype_ssh]:                   {nodeType: 511,icon:"ssh1",name:"ssh",key:"nt_511_objtype_ssh",iconColor: ''},
  [eNodeTypeId.nt_600_objtype_task_kpi]:                {nodeType: 600,icon:"KPI",name:"任务&考核",key:"nt_objtype_task_kpi",iconColor: ''},
  [eNodeTypeId.nt_330_objtype_task_todo]:              {nodeType: 330,icon:"renwu",name:"日常任务",key:"nt_objtype_task_daily",iconColor: ''},
  [eNodeTypeId.nt_333_objtype_exam]:             {nodeType: 333,icon:"a-zaixianxuexikaoshipeixunkecheng-56",name:"考试",key:"nt_objtype_exam_homewk",iconColor: '#A2CD2B'},
  [eNodeTypeId.nt_334_objtype_task_batch]:              {nodeType: 603,icon:"chanpinpici",name:"批次任务",key:"nt_objtype_task_batch",iconColor: ''},
  [eNodeTypeId.nt_604_objtype_kpi]:                     {nodeType: 604,icon:"chanpinpici",name:"考核",key:"nt_objtype_kpi",iconColor: ''},
  [eNodeTypeId.nt_700_objtype_cdisk]:                   {nodeType: 700,icon:"yunpan",name:"云盘",key:"nt_objtype_cdisk",iconColor: ''},
  [eNodeTypeId.nt_888_objtype_shop]:                    {nodeType: 888,icon:"",name:"商铺", nameEn:"shop", key:"nt_888_objtype_shop",iconColor: ''},
  [eNodeTypeId.nt_999_objtype_shortcut]:                {nodeType: -999,icon:"kuaijie1",name:"快捷方式",key:"nt_999_objtype_shortcut",iconColor: ''},
}

export const eCtxTypeId = {
  ctx_1_rename: 1,                  //此菜单项触发的是进入编辑状态
  ctx_100_rename_confirmed: 100,      //当编辑文本框失去焦点或确认后，实际发起接口调用
  ctx_101_rename_cancle: 101,      // 键盘esc取消编辑
  ctx_2_strike_txt: 2,          //添加删除线
  ctx_3_color_txt: 3,            //标题颜色
  ctx_4_view_priv: 4,            //查看权限
  ctx_5_move_node: 5,            //移至
  ctx_6_copy_url: 6,            //复制当前链接
  ctx_7_delete_node: 7,         //删除
  ctx_8_convert_to_pkg: 8,      //转资源包
  ctx_9_convert_to_folder: 9,   //转文件夹
  ctx_10_view_in_browser_tab: 10, //新页面打开
  ctx_11_short_url: 11,             //短链接
  ctx_12_create_shortcut: 12,     // 创建快捷方式
  ctx_13_bookmark: 13,            //添加至快捷访问
  ctx_14_copy_node: 14,           //复制
  ctx_15_cut_node: 15,            //剪切
  ctx_16_paste_node: 16,          //粘贴
  ctx_17_clone_node: 17,          //创建副本
  ctx_18_set_figure_tag: 18,      //图标颜色
  ctx_19_view_settings: 19,       //查看设置
  ctx_20_clone_cdb_conn: 20,      //克隆数据库连接
  ctx_21_toggle_cdb_conn: 21,     //开/关数据库连接
  ctx_22_sql_query_in_window: 22, //打开sql查询窗口
  ctx_23_delete_cdb_conn: 23,     //删除数据库连接
  ctx_24_backup_db: 24,           //备份数据库
  ctx_25_restore_db: 25,          //还原数据库
  ctx_26_delete_db: 26,           //删除数据库
  ctx_27_refresh_db: 27,          //刷新
  ctx_28_refresh_conn_db: 28,     //刷新数据库连接
  ctx_29_close_cdb_conn: 29,      //关数据库连接
  ctx_30_edit_cdb_conn: 30,       //编辑数据库连接
  // ctx_32_add_quick_bookmark: 32,  //加入快捷访问  废弃，更改为CTX_999_CRETESHORTCUT 创建
  // ctx_33_del_quick_bookmark: 33,  //删除快捷访问  废弃，更改为 team_501_trash_obj_node 接口删除
  ctx_34_del_strike_txt: 34,      //取消删除线
  ctx_35_edit_apigrp: 35,         //编辑api项目
  ctx_36_subclass: 36,            //自定义表单
  CTX_37_FAVORITE: 37,            //收藏
  CTX_38_CREATE: 38,              //新建
  CTX_39_PERSONALIZATION: 39,     //个性化设置
  ctx_42_set_icon: 42,            //设置图标（文档库下的文档）

  CTX_40_TOP: 40,                 //置顶（报告独有）
  CTX_41_READOP: 41,              //已读/未读（报告独有）

  CTX_999_CRETESHORTCUT: -999,     //新建快捷方式
  CTX_99901_SHORTCUT_DESKTOP: 99901, //快捷方式放至桌面
  CTX_99902_SHORTCUT_MINE: 99902,    //快捷方式放至我的
  CTX_99903_SHORTCUT_POSITION: 99903,//快捷方式选择位置

}

// 中树自定义更多事件
export const eCustomCtxTypeId = {
  // 搜索
  ctx_1060101_edit_search: 1060101,  // 编辑搜索
  // 报表
  ctx_1070101_edit_report: 1070101,  // 编辑报表
  // Api
  ctx_3160101_edit_api: 3160101,     // 编辑api
  // 仪表板
  ctx_1090101_set_overview: 1090101, // 仪表板 设置概览
}

/**
 * 新建图标
 * @param {*} maps 
 */
export const eNodeTypeCreateIcon = {
  [eNodeTypeId.nt_302_objtype_folder]:                     {icon:"xjwjj",          name:"新建文件夹", iconColor: '#3279fe'},
  [eNodeTypeId.nt_311_objtype_doc]:                        {icon:"xinjianwenjian", name:"新建文档", iconColor: '#3279fe'},
  [eNodeTypeId.nt_31201_objtype_tutorial_doc]:             {icon:"xinjianwenjian", name:"新建文档", iconColor: '#3279fe'},
  [eNodeTypeId.nt_31601_objtype_api_item]:                 {icon:"xinjianwenjian", name:"新建接口", iconColor: '#3279fe'},
  [eNodeTypeId.nt_10701_quick_access_reporting_signle]:    {icon:"xinjianwenjian", name:"新建报表", iconColor: '#3279fe'},
  [eNodeTypeId.nt_10901_quick_access_dashboard_signle]:    {icon:"xinjianwenjian", name:"新建仪表板", iconColor: '#3279fe'},
  [eNodeTypeId.nt_10601_quick_access_query_signle]:        {icon:"xinjianwenjian", name:"新建搜索", iconColor: '#3279fe'},
}

/**
 * 更多图标
 * @param {*} maps 
 */
export const eNodeTypeCtxIcon = {
  [eCtxTypeId.ctx_1_rename]: {icon:"zhongmingming",   name:"重命名", iconColor: ''},
  [eCtxTypeId.ctx_4_view_priv]: {icon:"guanliyuan",   name:"查看权限", iconColor: ''},
  [eCtxTypeId.ctx_5_move_node]: {icon:"drag",   name:"移至", iconColor: ''},
  [eCtxTypeId.ctx_6_copy_url]: {icon:"fuzhi1",   name:"复制链接地址", iconColor: ''},
  [eCtxTypeId.ctx_7_delete_node]: {icon:"shanchu2",   name:"删除", iconColor: ''},
  [eCtxTypeId.ctx_10_view_in_browser_tab]: {icon:"liulanqi",   name:"新页面打开", iconColor: ''},
  [eCtxTypeId.ctx_11_short_url]: {icon:"lianjie",   name:"查看短链", iconColor: ''},
  [eCtxTypeId.ctx_12_create_shortcut]: {icon:"kuaijie1",   name:"创建快捷方式", iconColor: ''},
  [eCtxTypeId.ctx_17_clone_node]: {icon:"fuzhi2",   name:"创建副本", iconColor: ''},
  [eCtxTypeId.ctx_19_view_settings]: {icon:"shezhixitongshezhigongnengshezhishuxing",   name:"查看设置", iconColor: ''},
  [eCtxTypeId.ctx_20_clone_cdb_conn]: {icon:"fuzhi1",   name:"克隆连接", iconColor: ''},
  [eCtxTypeId.ctx_21_toggle_cdb_conn]: {icon:"charulianjie",   name:"开启连接", iconColor: ''},
  [eCtxTypeId.ctx_23_delete_cdb_conn]: {icon:"shanchu2",   name:"删除连接", iconColor: ''},
  [eCtxTypeId.ctx_28_refresh_conn_db]: {icon:"shuaxin1",   name:"刷新", iconColor: ''},
  [eCtxTypeId.ctx_29_close_cdb_conn]: {icon:"quxiaolianjie",   name:"关闭连接", iconColor: ''},
  [eCtxTypeId.ctx_30_edit_cdb_conn]:        {icon:"bianji3",  name:"编辑连接", iconColor: ''},
  // [eCtxTypeId.ctx_32_add_quick_bookmark]: {icon:"tianjiashuqian",   name:"加入书签", iconColor: ''},
  [eCtxTypeId.ctx_36_subclass]: {icon:"biaodanzujian-biaoge",   name:"自定义表单", iconColor: ''},
  [eCtxTypeId.CTX_37_FAVORITE]: {icon:"shoucang1",   name:"收藏", iconColor: ''},
  [eCtxTypeId.CTX_38_CREATE]: {icon:"jia",   name:"新建", iconColor: ''},
  [eCtxTypeId.CTX_39_PERSONALIZATION]: {icon:"gexinghuamoban",   name:"个性化设置", iconColor: ''},
  [eCtxTypeId.CTX_40_TOP]: {icon:"zhidingmian",   name:"置顶", iconColor: ''},
  [eCtxTypeId.CTX_41_READOP]: {icon:"xinfengdakai",   name:"已读/未读", iconColor: ''},

  [eCtxTypeId.CTX_99901_SHORTCUT_DESKTOP]: {icon:"tuandui",   name:"放至\"桌面\"", iconColor: ''},
  [eCtxTypeId.CTX_99902_SHORTCUT_MINE]: {icon:"gerendaiban",   name:"放至\"我的\"", iconColor: ''},
  [eCtxTypeId.CTX_99903_SHORTCUT_POSITION]: {icon:"sousuoleimu",   name:"选择位置...", iconColor: ''},

  // Api
  [eCustomCtxTypeId.ctx_3160101_edit_api]:        {icon:"bianji3",  name:"编辑接口", iconColor: ''},
}

// 资源状态（右击菜单：get_node_ctx_options）
export const eMenuStatus = {
  eInit: 0,         // 0初始状态 不呈现
  eBuyValid: 1,     // 1购买有效
  eExpired: 2,      // 2 购买过期（试用过期右击菜单不返回可新建资源）
  eTrial: 3,        // 3试用中
}

// 资源状态图标
export const eMenuStatusIcon = {
  [eMenuStatus.eInit]:      { icon:"",             name:"初始状态", iconColor: ''},
  [eMenuStatus.eBuyValid]:  { icon:"",             name:"购买有效", iconColor: ''},
  [eMenuStatus.eExpired]:   { icon:"31tishi1", name:"过期",     iconColor: '#fcd53f'},
  [eMenuStatus.eTrial]:     { icon:"",             name:"试用中",   iconColor: ''},
}

// 资源状态图标
export const getMenuStatusIcon = (type) => {
  return eMenuStatusIcon[type] || {};
}

// 获取右击菜单图标
export const getCtxIconByType = (type) => {
  return eNodeTypeCtxIcon[type]?.icon || '';
}

// 获取右击菜单图标属性
export const getCtxIconObjByType = (type) => {
  return eNodeTypeCtxIcon[type] || {};
}

//获取节点图标
export const getIconByNodeType = (nodeTypeId) => {
  return eNodeType[nodeTypeId]?.icon
}

//获取节点类型名称
 export const getNodeNameByNodeType = (nodeTypeId) => {
  return eNodeType[nodeTypeId]?.name;
}

//获取节点信息
export const getNodeConfigByNodeType = (nodeTypeId) => {
  if(!eNodeType[nodeTypeId]){
    return {}
  }
  return eNodeType[nodeTypeId]
}

/**
 * @description 根据子节点获取父节点key(List形式)
 * @param {String} key 叶子节点
 * @param {Array} arr 需要展开的节点
 * @param {Array} list 数据源
 * @returns 
 */
 export const getParentsKeysBykey = (arr,key,list)=>{
  let node = list.find(item => item.nodeId == key)
  if(node){
    arr.push(node.nodeParentId)
    arr = getParentsKeysBykey(arr,node.nodeParentId,list)
  }
  return arr;
}

/**
 * @description 根据子节点获取父节点key (Tree形式)
 * @param {String} rootNode Tree数组
 * @param {Array} key 选中的节点, 可能为objId或者nodeId
 * @param {Array} includeOwn 是否包含自身节点，默认false
 * @returns 
 */

export function getParentsKeysBykeyInTree(rootNode, key, includeOwn = false) {
  for (let i in rootNode) {
    if (rootNode[i].key == key) {
      return includeOwn ? [rootNode[i].key] : [];  // 是否返回自身节点信息
    }

    if (rootNode[i].children) {
      let node = getParentsKeysBykeyInTree(rootNode[i].children, key, includeOwn);
      if (node !== undefined) {
        return node.concat(rootNode[i].key);  //查询到把父节点连起来
      }
    }
  }
}

//颜色定义
export const ColorOption = [
  {type: 1, value: '#7BBE02', title: "标题1", className: "tree-more-select-color-7BBE02" },
  {type: 2, value: '#FFB228', title: "标题2",  className: "tree-more-select-color-FFB228" },
  {type: 3, value: '#FF6347', title: "标题3", className: "tree-more-select-color-FF6347"},
  {type: 4, value: '#72D6C9', title: "标题4", className: "tree-more-select-color-72D6C9" },
  {type: 5, value: '#3799FF', title: "标题5", className: "tree-more-select-color-3799FF"},
  // 颜色标签
  {type: 6, value: "#B39DDB",  title: "标题6", className: "tree-more-select-color-B39DDB" },
  {type: 7, value: "#D24646",  title: "标题7", className: "tree-more-select-color-D24646" },
  {type: 8, value: "#E17D3F",  title: "标题8", className: "tree-more-select-color-E17D3F" },
  {type: 9, value: "#649B1E",  title: "标题9", className: "tree-more-select-color-649B1E" },
  {type: 10, value: "#666666", title: "标题10",  className: "tree-more-select-color-666666",},
]

//获取颜色
export const getColorByType = (type) => {
  return ColorOption.find(item => item.type == type)?.value || "";
}

// 仪表板、甘特图、问题跟踪缩略图
// export const eNodeTypeThumb = {
//   [eNodeTypeId.nt_10901_quick_access_dashboard_signle]: bdboardImg,
//   [eNodeTypeId.nt_318_objtype_gantt]: ganntImg,
//   [eNodeTypeId.nt_317_objtype_issue_project]: issueImg,
// }

// opType 操作类型
export const opTypeOption = [
  { opType: 1, typeName: '评论', option: "", },
  { opType: 2, typeName: '提及', option: "", },
  { opType: 3, typeName: '分配任务', option: "", },
  { opType: 4, typeName: '更新任务', option: "", },
  { opType: 5, typeName: '移除任务', option: "", },
  { opType: 6, typeName: '点赞', option: "", },
  { opType: 7, typeName: '生成', option: "", },
  { opType: 8, typeName: '更新', option: "", },
  { opType: 9, typeName: '加入团队', option: "", },
  { opType: 10, typeName: '退出团队', option: "", },
  { opType: 11, typeName: "申请加入", option: "去审批", },
  { opType: 12, typeName: "移除团队", option: "", },
  { opType: 13, typeName: "团队扩容", option: "去扩容", },
  { opType: 14, typeName: "产品续费", option: "续费", },
  { opType: 15, typeName: "云盘扩容", option: "去扩容", },
  { opType: 16, typeName: "日程", option: "", },
  { opType: 17, typeName: "团队邀请", option: "接受邀请", },
  { opType: 18, typeName: "产品购买", option: "购买", },
]

// 获取操作
export const getOptionByType = (opType) => {
  return opTypeOption.find(item => item.opType == opType)?.option || "";
}

// 文件夹类型集合
export const folderNodeTypeList = [
  eNodeTypeId.nt_302_objtype_folder,
  eNodeTypeId.nt_302105_objtype_folder_label,
  eNodeTypeId.nt_302106_objtype_folder_search,
  eNodeTypeId.nt_302107_objtype_folder_chart,
  eNodeTypeId.nt_302109_objtype_folder_boards,
  eNodeTypeId.nt_302312_objtype_folder_docs,
  eNodeTypeId.nt_302112_objtype_folder_docs,
  eNodeTypeId.nt_302316_objtype_folder_api,
  eNodeTypeId.nt_302317_objtype_folder_issue,
  eNodeTypeId.nt_30231703_objtype_folder_issue,
  eNodeTypeId.nt_302500_objtype_folder_cdb,
  eNodeTypeId.nt_302118_objtype_folder_my,
];

// 是否是文件夹类型
export const getFolderFlg = (nodeType) =>{
  // 狭义定义: 302开头的nodeType
  return folderNodeTypeList.some(folder=> folder == nodeType);
}
