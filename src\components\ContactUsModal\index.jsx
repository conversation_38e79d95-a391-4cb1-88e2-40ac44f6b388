import { Button,Layout,message,Form,Input,Modal,Checkbox,Image,Carousel,Space } from "antd";
import React, { useEffect, useState, useRef } from "react";
import { CheckCircleOutlined } from '@ant-design/icons';
import { useParams } from "react-router-dom";
import "./index.scss";
import {
  team_1301_get_page_part_widget,
  team_1401_save_contact_info,
} from "../../api/http_common";
import PolychromeIcon from "../PolychromeIcon";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../utils/commonUtils";
import {partTypeEnum} from 'src/utils/enum';

const { Search } = Input;
export default function ContactUsModal({open, onCancel}) {
  const [form] = Form.useForm();
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if(open) {
      form.resetFields()
      getPagePartWidget(6666, "/contact_us")
    }
  },[open])

  // 获取联系我们页面元素
  const getPagePartWidget = (teamId, menuUrl) => {
    team_1301_get_page_part_widget({teamId, menuUrl}).then(res => {
      if(res.resultCode == 200){
        if(isArrayUtils(res.partList.filter(el => el.partType === partTypeEnum.part_type_69_coop_contact_us)[0]?.widgetList)) {
          let formatData = formatTreeData([], res.partList.filter(el => el.partType === partTypeEnum.part_type_69_coop_contact_us)[0]?.widgetList)
          setModuleData([...formatData])
        }
      }
    });
  }

  // 发送
  const ContactUsFinish = (value) => {
    team_1401_save_contact_info({ teamId: 6666, ...value }).then((res) => {
      if(res.resultCode === 200) {
        message.success(res.resultMessage)
        onCancel()
      } else {
        message.error(res.resultMessage)
      }
    })
  }
  return (
    <Modal
    className="ContactUsModal"
    width={840}
    centered
    maskStyle={{ backgroundColor: 'rgba(255, 255, 255, .3)' }}
    footer={null}
    // maskClosable={false}
    closable={false}
    onCancel={onCancel}
    open={open}
    >
      <div className="ContactUsModal-left">
        <div className="ContactUsModal-left-heard">
          <PolychromeIcon size="120" value="logojianjie" style={{ height: 50, color: '#333' }}/>
          <span className="ContactUsModal-left-heard-line"/>
          <span className="ContactUsModal-left-heard-title">联系我们</span>
        </div>
        <div className="ContactUsModal-left-image" style={{ overflow: 'hidden' }}>
          <img width="100%" src={filterWidgetList((moduleData), 3)[0]?.value}/>
        </div>
        <div className="ContactUsModal-left-help">
          <div className="ContactUsModal-left-help-title">{filterWidgetList((moduleData), 1)[0]?.value}</div>
          
          {filterWidgetList(moduleData, 2).map((item, index) => (
            <div className="ContactUsModal-left-help-li" key={item.widgetId}>
              <CheckCircleOutlined style={{ color: '#72dd9c' }}/>
              <span className="ContactUsModal-left-help-li-text">{item.value}</span>
            </div>
          ))}
        </div>
        {(filterWidgetList((moduleData), 8).length > 0 && filterWidgetList((moduleData), 9).length > 0) && <div className="ContactUsModal-left-contact">
          <div className="ContactUsModal-left-contact-title">您也可以通过以下方式联系我们</div>
          <div className="ContactUsModal-left-contact-span">
            {filterWidgetList((moduleData), 8).length > 0 && <span className="fontsize-16 icon_span iconfont dianhua">{filterWidgetList((moduleData), 8)[0]?.value}</span>}
            {filterWidgetList((moduleData), 9).length > 0 && <span className="fontsize-16 icon_span iconfont youxiang">{filterWidgetList((moduleData), 9)[0]?.value}</span>}
          </div>
        </div>}
      </div>
      <div className="ContactUsModal-right">
        <Form className="ContactUsModal-right-form" form={form} colon={false} layout="vertical" onFinish={ContactUsFinish}>
          <Form.Item>
            <Form.Item 
            className="ContactUsModal-right-form-title" 
            label="请填写真实信息，我们会在3个工作日内与您联系"/>
            <Form.Item 
            label="姓名" 
            name="name"
            rules={[
              {required: true, message: '姓名不能为空！'}
            ]}>
              <Input/>
            </Form.Item>

            <Form.Item 
            label="手机号码"
            name="mobileNo"
            required={true}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  let phoneReg = /^[1][3,4,5,7,8][0-9]{9}$/;
                  if(!value) return Promise.reject(new Error('手机号码不能为空！'));
                  if (phoneReg.test(value)) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('请输入正确的手机号！'));
                },
              }),
            ]}>
              <Input maxLength={11}/>
            </Form.Item>

            <Form.Item 
            label="公司全称"
            name="companyName">
              <Input/>
            </Form.Item>

            <Form.Item 
            label="公司规模"
            name="companySize">
              <Input/>
            </Form.Item>

            <Form.Item 
            label="所属行业"
            name="industry">
              <Input/>
            </Form.Item>

            <Form.Item 
            label="描述"
            name="requireDesc">
              <Input/>
            </Form.Item>
          </Form.Item>
          <Form.Item className="ContactUsModal-right-form-submit">
            <Button style={{ width: '100%' }} htmlType="submit" type="primary">发送</Button>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
}