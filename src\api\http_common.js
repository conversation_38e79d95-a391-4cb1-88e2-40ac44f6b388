import * as httpBase from "../utils/httpBase";
import api_name from "./api_name";
const {api_tms_team, api_tms_work} = httpBase;

// team-120 get_obj_favorite_status 获取对象收藏、关注、点赞状态
// https://confluence.ficent.com/pages/viewpage.action?pageId=78379157
export const team_120_get_obj_favorite_status= (data) => httpBase.post(api_tms_team + api_name.team_120_get_obj_favorite_status, data)
// team-029 obj_social_op 点赞 及取消操作
// https://confluence.ficent.com/pages/viewpage.action?pageId=78363573
export const team_029_obj_social_op= (data) => httpBase.post(api_tms_team + api_name.team_029_obj_social_op, data)
// team-503 get_attachment_list 获取附件列表
// https://confluence.ficent.com/pages/viewpage.action?pageId=78365974
export const team_503_get_attachment_list = (data) => httpBase.post(api_tms_team + api_name.team_503_get_attachment_list, data) 
// team-504 get_comment_list 获取评论列表
// https://confluence.ficent.com/pages/viewpage.action?pageId=78365977
export const team_504_get_comment_list = (data) => httpBase.post(api_tms_team + api_name.team_504_get_comment_list, data) 
// team-505 get_obj_list 获取关联资源列表
// https://confluence.ficent.com/pages/viewpage.action?pageId=78365980
export const team_505_get_obj_list = (data) => httpBase.get(api_tms_team + api_name.team_505_get_obj_list, data) 
// team-506 upload_file 上传附件
// https://confluence.ficent.com/pages/viewpage.action?pageId=78368196
export const team_506_upload_file = (data) => httpBase.post(api_tms_team + api_name.team_506_upload_file, data,{"Content-Type": 'application/x-www-form-urlencoded'}) 
// team-509 add_comment 添加评论
// https://confluence.ficent.com/pages/viewpage.action?pageId=78368200
export const team_509_add_comment= (data) => httpBase.post(api_tms_team + api_name.team_509_add_comment, data)
// team-524 delete_comment 删除评论
// https://confluence.ficent.com/pages/viewpage.action?pageId=78379791
export const team_524_delete_comment= (data) => httpBase.post(api_tms_team+api_name.team_524_delete_comment,data)
// team-525 edit_comment 编辑评论
// https://confluence.ficent.com/pages/viewpage.action?pageId=78379811
export const team_525_edit_comment= (data) => httpBase.post(api_tms_team+api_name.team_525_edit_comment,data)
// team_530 get_obj_node_info获取某个节点详情
// https://confluence.ficent.com/pages/viewpage.action?pageId=78380170
export const team_530_get_obj_node_info = (data) => httpBase.get(api_tms_team+api_name.team_530_get_obj_node_info,data)
// team-531 comment_social_op 评论点赞,取消点赞,倒赞,取消倒赞
// https://confluence.ficent.com/pages/viewpage.action?pageId=78380377
export const team_531_comment_social_op= (data) => httpBase.post(api_tms_team+api_name.team_531_comment_social_op,data)
// team-545 select_table_fields "保存"用户勾选的显示字段
// https://confluence.ficent.com/pages/viewpage.action?pageId=98404386
export const team_545_select_table_fields= (data) => httpBase.post(api_tms_team + api_name.team_545_select_table_fields, data)
// team-552 save_front_query 保存页面查询条件
// https://confluence.ficent.com/pages/viewpage.action?pageId=98409393
export const team_552_save_front_query = (data) => httpBase.post(api_tms_team + api_name.team_552_save_front_query, data)
// team-523 get_front_query 获取页面查询条件
// https://confluence.ficent.com/pages/viewpage.action?pageId=98409396
export const team_553_get_front_query = (data) => httpBase.post(api_tms_team + api_name.team_553_get_front_query, data)
// team-619 user_info 用户个人设置: 获取用户信息
// https://confluence.ficent.com/pages/viewpage.action?pageId=98409743
export const team_619_user_info = (data) => httpBase.get(api_tms_team+api_name.team_619_user_info,data,{},false/* ,{ignore: "true"} */)
// team-630 user_log_out 退出登录
// https://confluence.ficent.com/pages/viewpage.action?pageId=98409749
export const team_630_user_log_out= (data) => httpBase.get(api_tms_team+api_name.team_630_user_log_out,data) 
// 新闻(版本)
// team-1101 get_news_list 获取新闻列表
// https://confluence.ficent.com/pages/viewpage.action?pageId=98415202
export const portal_1101_get_news_list=(data) => httpBase.post(api_tms_work+api_name.portal_1101_get_news_list,data)
// team-1102 get_news_detail 获取新闻详情
// https://confluence.ficent.com/pages/viewpage.action?pageId=98415207
export const portal_1102_get_news_detail=(data) => httpBase.post(api_tms_work+api_name.portal_1102_get_news_detail,data)
// 菜单(头部/底部)
// team-1201 get_menu_list 获取官网菜单列表
// https://confluence.ficent.com/pages/viewpage.action?pageId=98415311
export const team_1201_get_menu_list=(data) => httpBase.get(api_tms_team+api_name.team_1201_get_menu_list,data)

// 页面元素
// team-1301 get_page_part_widget 获取页面模块及组件
// https://confluence.ficent.com/pages/viewpage.action?pageId=98415314
export const team_1301_get_page_part_widget=(data) => httpBase.get(api_tms_team+api_name.team_1301_get_page_part_widget,data)
// team-1302 get_price_solution 获取价格方案和矩阵
// https://confluence.ficent.com/pages/viewpage.action?pageId=98415324
export const team_1302_get_price_solution=(data) => httpBase.get(api_tms_team+api_name.team_1302_get_price_solution,data)
// team-1401 save_contact_info 保存个人信息(联系我们)
// https://confluence.ficent.com/pages/viewpage.action?pageId=98415330
export const team_1401_save_contact_info=(data) => httpBase.post(api_tms_team+api_name.team_1401_save_contact_info,data)
// team-1402 save_company_info 保存公司信息(代理商申请)
// https://confluence.ficent.com/pages/viewpage.action?pageId=98415337
export const team_1402_save_company_info=(data) => httpBase.post(api_tms_team+api_name.team_1402_save_company_info,data)
// team-564 load_obj_list 获取某个节点下的资源列表 (权限过滤)
// https://confluence.ficent.com/pages/viewpage.action?pageId=98411330
export const team_564_load_obj_list = (data) => httpBase.post(api_tms_team +api_name.team_564_load_obj_list, data)

// team-017 move_obj_node 将某节点拖拽到另一个节点下
// https://confluence.ficent.com/pages/viewpage.action?pageId=78370083
export const team_017_move_obj_node=(data) => httpBase.post(api_tms_team+api_name.team_017_move_obj_node,data)


// team-1503 get_os_help_list 获取操作系统内的帮助文档
// https://confluence.ficent.com/pages/viewpage.action?pageId=98417071
export const team_1503_get_os_help_list = (data) => httpBase.get(api_tms_team+api_name.team_1503_get_os_help_list,data,{source: "portal"})