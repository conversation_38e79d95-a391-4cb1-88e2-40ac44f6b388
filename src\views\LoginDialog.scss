.login-Dialog {
    width: auto !important;
    max-width: 732px;
    border-radius: 5px;
    .ant-modal-content {
        border-radius: 5px;
        .ant-modal-body {
            padding: 0;
        }
    }
    .Login {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        background-size: 100% 100%;
        overflow-y: auto;
        border-radius: 5px;
        background-color: white;
        &-box {
            display: flex;
            align-items: center;
            // box-shadow: 1px 1px 5px 1px #e6e6e6;
        }
        &-leftContent {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            width: 332px;
            height: 500px;
            background-color: white;
            &-wechatbox {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 100%;
                &-title {
                    display: flex;
                    align-items: center;
                    .wechatIcon {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-right: 10px;
                        width: 30px;
                        height: 30px;
                        font-size: 18px;
                        color: white;
                        border-radius: 50%;
                        background-color: #0abe65;
                    }
                }
            }
            &-erweima, .erweimaExpired {
                display: flex;
                align-items: center;
                margin: 40px 0;
                .erweima_Image {
                    position: relative;
                    width: 138px;
                    height: 138px;
                    transform: translate(56px, 0);
                    animation: erweima 5s forwards;
                }
                // 二维码动画
                @keyframes erweima {
                    0% {}
                    10% {}
                    20% {transform: translate(0, 0);}
                    30% {transform: translate(0, 0);}
                    40% {transform: translate(0, 0);}
                    50% {transform: translate(0, 0);}
                    60% {transform: translate(0, 0);}
                    70% {transform: translate(0, 0);}
                    80% {transform: translate(0, 0);}
                    90% {transform: translate(0, 0);}
                    100% {}
                }
                .tips_Image {
                    width: 110px;
                    height: 110px;
                    font-size: 12px;
                    background: url(../assets/images/wechat_tips.png) no-repeat center center;
                    background-size: 100% 100%;
                    opacity: 0;
                    animation: tips 5s forwards;
                }
                // 提示图片动画
                @keyframes tips {
                    0% {opacity: 0}
                    10% {opacity: 0}
                    20% {opacity: 1;visibility: visible;}
                    30% {opacity: 1;visibility: visible;}
                    40% {opacity: 1;visibility: visible;}
                    50% {opacity: 1;visibility: visible;}
                    60% {opacity: 1;visibility: visible;}
                    70% {opacity: 1;visibility: visible;}
                    80% {opacity: 1;visibility: visible;}
                    90% {opacity: 1;visibility: visible;}
                    95% {opacity: 0}
                    100% {opacity: 0}
                }
            }
            &-erweima:hover .erweima_Image {
                -webkit-transition: all 5s;
                transition: all 5s;
                transform: translate(0, 0);
            }
            &-erweima:hover .tips_Image {
                visibility: visible;
                -webkit-transition: all 5s;
                transition: all 5s;
                opacity: 1 !important;
            }
            &-Qqbox {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
                width: 100%;
                .Qqline {
                    width: 100px;
                    height: 1px;
                    background-color: #f2f2f2;
                }
                .QqIcon {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 30px;
                    height: 30px;
                    font-size: 18px;
                    color: white;
                    border-radius: 50%;
                    background-color: #4ec8fd;
                }
            }
        }
        &-rightContent {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            width: 400px;
            height: 500px;
            border-left: 1px solid #ebebeb;
            background-color: white;
            &-box {
                &-title {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0 50px;
                    margin-bottom: 40px;
                    font-size: 18px;
                    color: #282828;
                }
                &-form {
                    &-inp {
                        padding: 0 15px;
                        width: 358px;
                        height: 50px;
                        border-radius: 50px;
                        box-shadow: none;
                        .ant-input {
                            background-color: transparent;
                        }
                    }
                    &-search {
                        width: 358px;
                        height: 50px;
                        .ant-input-affix-wrapper {
                            padding: 0 15px;
                            height: 50px;
                            border-top-left-radius: 50px !important;
                            border-bottom-left-radius: 50px !important;
                            box-shadow: none;
                            .ant-input {
                                background-color: transparent;
                            }
                        }
                        .ant-input-group-addon {
                            height: 50px;
                            background-color: transparent;
                            .ant-btn {
                                height: 50px;
                                color: #2c8eff;
                                background-color: white;
                                border: 1px solid #d9d9d9;
                                box-shadow: none;
                                border-top-right-radius: 50px !important;
                                border-bottom-right-radius: 50px !important;
                            }
                        }
                    }
                    &-check {
                        .ant-checkbox-wrapper {
                            .ant-checkbox {
                                border-radius: 50%;
                                overflow: hidden;
                            }
                            .ant-checkbox-input {
                                background-color: transparent;
                                border: none;
                                border-radius: 50%;
                            }
                            .ant-checkbox-inner {
                                border-radius: 50%;
                                border: 1px solid #DCDFE6;
                            }
                            .ant-checkbox-checked::after {
                                border-radius: 50% !important;
                                border: none;
                            }
                        }
                    }
                }
                &-a {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-weight: 500;
                    color: #ccc;
                }
            }
        }
    }
    
    // 二维码已过期
    .erweima_Image-refresh {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        transform: translate(-50%, -50%);
        background-color: rgba(242, 242, 242, .8);
    }
    
    .qqLoginBtn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        // background-color: #4fc8fd;
        background-color: #40a9ff;
        a {
            display: flex;
            img {
                width: 20px;
            }
        }
    }
}