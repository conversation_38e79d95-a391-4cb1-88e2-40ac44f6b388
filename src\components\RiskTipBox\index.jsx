import React, { useEffect, useState } from "react";
import { Collapse, Button, Input, message, Modal, Checkbox } from "antd";
import { CheckCircleOutlined, CloseCircleOutlined, QuestionCircleOutlined, InfoCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import "./index.scss"
const { Panel } = Collapse;
export default function RiskTipBox({ title,content, width, icon, visible, onOk, onCancel }) {
  const [modalWidth, setModalWidth] = useState(300)
  useEffect(() => {
    if(width == '' || width == undefined || typeof width != 'number') {
      setModalWidth(300)
    } else {
      setModalWidth(width)
    }
  },[width])
  // 图标样式
  const iconDisplay = (style) => {
    let iconData;
    switch (style) {
      case 'success':
        iconData = <CheckCircleOutlined style={{ color: '#73d13d' }} /> // success
        break;
      case 'error':
        iconData = <CloseCircleOutlined style={{ color: 'red' }} /> // error
        break;
      case 'doubt':
        iconData = <QuestionCircleOutlined style={{ color: 'red' }} /> // doubt
        break;
      case 'default':
        iconData = <InfoCircleOutlined style={{ color: 'red' }} /> // default
        break;
      case 'warning':
        iconData = <ExclamationCircleOutlined style={{ color: 'red' }} /> // warning
        break;
      default:
        iconData = <></>
        break;
    }
    return iconData
  }
  return (
    <Modal
      className="RiskTipBox"
      title={title}
      width={modalWidth}
      centered
      maskClosable={false}
      keyboard={true}
      open={visible}
      cancelText="取消"
      onCancel={onCancel}
      onOk={onOk}
      okText="确定"
    >
      <div className="RiskTipBox-body">
        <span className="RiskTipBox-body-icon">{iconDisplay(icon)}</span>
        <span className="RiskTipBox-body-content">{content}</span>
      </div>
    </Modal>
  );
}
