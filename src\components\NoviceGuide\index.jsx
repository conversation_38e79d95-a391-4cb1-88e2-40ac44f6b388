import { Button,Modal,Image,Carousel } from "antd";
import React, { useEffect, useState, useRef } from "react";
import { LeftOutlined, RightOutlined, CloseOutlined } from '@ant-design/icons';
import { useParams } from "react-router-dom";
import "./index.scss";

export default function NoviceGuide({open, onClose, noviceGuideInfo}) {
  const noviceGuideRef = useRef();
  const [noviceGuideNum, setNoviceGuideNum] = useState(0);

  const noviceGuideAfter = (current) => {
    setNoviceGuideNum(current)
  }

  // 面板切换
  const noviceGuideChange = (type) => {
    if(type === "prev") {
      if(noviceGuideNum <= 0) return
      noviceGuideRef.current.prev()
    } else {
      if(noviceGuideNum >= noviceGuideInfo.slideList.length - 1) return
      noviceGuideRef.current.next() 
    }
  }

  const noviceGuideBefor = (from, to) => {
    const {tourId, tourStepId} = noviceGuideInfo.slideList[to]
  }

  // 下一步
  const noviceGuideNextStep = () => {
    if(noviceGuideNum < noviceGuideInfo.slideList.length - 1) {
      const {tourId, tourStepId} =  noviceGuideInfo.slideList[noviceGuideNum + 1]
      noviceGuideRef.current.next()
    } else {
      setNoviceGuideNum(0)
      onClose()
    }
  }

  return (
    <Modal
    className="NoviceGuide-Modal"
    width="920px"
    centered
    maskStyle={{ backgroundColor: 'rgba(0, 0, 0, .5)' }}
    title={
      <>
        <span className="fontsize-14">{`示例团队-${noviceGuideInfo.title}`}</span>
        <Button 
        style={{ padding: 0, color: '#333', fontSize: 12 }} 
        type="link"
        icon={<CloseOutlined />}
        onClick={() => {
          setNoviceGuideNum(0)
          onClose()
        }}
        />
      </>
    }
    footer={
      <>
        <span style={{ color: '#333' }}>{noviceGuideNum + 1}/{noviceGuideInfo.slideList.length}</span>
        <Button 
        className="fontsize-12"
        style={{ width: 80, borderRadius: 5 }}
        type="primary" 
        size="small"
        onClick={noviceGuideNextStep}>{noviceGuideNum + 1 >= noviceGuideInfo.slideList.length ? "关闭" : "下一步"}</Button>
      </>
    }
    maskClosable={false}
    closable={false}
    open={open}
    destroyOnClose
    >
      <Button 
      className="carouselChange-icon carouselChange-left" 
      type="primary"
      shape="circle"
      icon={<LeftOutlined />}
      onClick={() => noviceGuideChange("prev")}/>
      <Button 
      className="carouselChange-icon carouselChange-right"
      type="primary"
      shape="circle"
      icon={<RightOutlined />}
      onClick={() => noviceGuideChange("next")}/>
      <Carousel 
      className="NoviceGuide-Carousel"
      dots={false}
      ref={noviceGuideRef}
      afterChange={noviceGuideAfter}
      beforeChange={noviceGuideBefor}>
        {noviceGuideInfo.slideList.map((item, index) => (
          <div className="NoviceGuide-Carousel-li" key={index}>
            <div className="NoviceGuide-Carousel-li-img">
            {!item.slidePic
             ? <div style={{ borderRadius: 5 }}></div>
             : <Image className="NoviceGuide-Carousel-li-img-url" width={"100%"} preview={false} src={item.slidePic}/>
             }
            </div>
            <div className="NoviceGuide-Carousel-li-text">{item.slideDesc}</div>
          </div>
        ))}
      </Carousel>
    </Modal>
  );
}