import hljs from "highlight.js";
import $ from "jquery";

(function (global, factory) {
  factory(require("froala-editor"));
})(this, function (FE) {
  "use strict";

  FE = FE && FE.hasOwnProperty("default") ? FE["default"] : FE;

  const brPlugin = {
    "before:highlightBlock": ({ block }) => {
      if (block) {
        block.innerHTML = block.innerHTML.replace(/<br[ /]*>/g, "\n");
      }
    },
    "after:highlightBlock": ({ result }) => {
      if (result) {
        result.value = result.value.replace(/\n/g, "<br>");
      }
    },
  };

  // how to use it
  hljs.addPlugin(brPlugin);
  hljs.configure({ 
    useBR: true,
  });

  // Define popup template.
  Object.assign(FE.POPUP_TEMPLATES, {
    "code.edit": "[_BUTTONS_]",
  });





  // The custom popup is defined inside a plugin (new or existing).
  FE.PLUGINS.codeEdit = function (editor) {
    var _codeEditButtons = ["insertPreEnter", "insertEnter", "|", "codeLanguage"];

    // Create custom popup.
    function initPopup() {
      // Load popup template.
      var table_buttons = '<div class="fr-buttons codeLanguage-pop">'.concat(
        editor.button.buildList(_codeEditButtons),
        "</div>"
      );

      var template = {
        buttons: table_buttons,
      };

      // Create popup.
      var $popup = editor.popups.create("code.edit", template);

      return $popup;
    }

    // Show the popup
    function showPopup(boundingRect) {
      // Get the popup object defined above.
      var $popup = editor.popups.get("code.edit");

      // If popup doesn't exist then create it.
      // To improve performance it is best to create the popup when it is first needed
      // and not when the editor is initialized.
      if (!$popup) $popup = initPopup();

      // Set the editor toolbar as the popup's container.
      editor.popups.setContainer("code.edit", editor.$box);

      // If the editor is not displayed when a toolbar button is pressed, then set BODY as the popup's container.
      // editor.popups.setContainer('customPlugin.popup', $('body'));

      // Trigger refresh for the popup.
      // editor.popups.refresh('customPlugin.popup');

      // This custom popup is opened by pressing a button from the editor's toolbar.
      // Get the button's object in order to place the popup relative to it.
      // var $btn = editor.$tb.find('.fr-command[data-cmd="myButton"]')

      // Compute the popup's position.
      var left = boundingRect.left + boundingRect.width / 2;
      var top = boundingRect.top - 50;// (editor.opts.toolbarBottom ? 10 : boundingRect.height - 10);

      // Show the custom popup.
      // The button's outerHeight is required in case the popup needs to be displayed above it.
      editor.popups.show("code.edit", left, top, boundingRect.height, true);
      if (!editor.popups.isVisible("code.edit")) {
        editor.popups.get("code.edit").addClass("fr-active");
      }
      // setTimeout(()=>{
      //   editor.popups.get('code.edit').find('.codeLanguage-pop .fr-command.fr-btn').trigger('mouseover');
      // },0)
    }

    // Hide the custom popup.
    function hidePopup() {
      editor.popups.hide("code.edit");
    }

    // Methods visible outside the plugin.
    return {
      showPopup: showPopup,
      hidePopup: hidePopup,
    };
  };

  // Define the plugin.
  // The editor parameter is the current instance.
  FE.PLUGINS.codeSnippet = function (editor) {

    function _init(){
      // 监听插入数据
      editor.events.on("paste.before", onPasteBefore);
      // 监听数据变化
      editor.events.on("input", preContentChange);
      // 监听切换语言
      editor.$el.off("mousedown", "pre>code")
      editor.$el.on("mousedown", "pre>code", showCodeTypePop.bind(editor))
    }

    function _style($blk) {
      // insert
      return $("<code" + editor.node.attributes($blk.get(0)) + ">")
        .addClass("fr-inner")
        .addClass("hljs")
        .addClass("language-plaintext")
        .html($blk.html())
        .removeAttr("data-empty");
    }

    function apply() {

      var blocks = editor.selection.blocks(); // Save selection to restore it later.

      editor.selection.save();
      // 选中pre 或者 code 标签都不会有作用
      if ($(blocks).children("pre").length == 0 && $(blocks).children("code").length == 0) {
        $(blocks[0]).before($("<pre temp='true' class='fr-deletable' style='margin-top:10px;display:inline-block;width: 100%;background: #fafafa'></pre>"));
        for (var i = 0; i < blocks.length; i++) {
          if (!editor.node.isList(blocks[i])) {
            var $blk = $(blocks[i]);

            // if ($blk[0].nextSibling === null) {
            //   var br = $("<br>");
            //   $blk[0].after(br[0]);
            // }

            var elem = _style($blk);
            $(editor.$el.find("[temp='true']")[0]).append(elem);
            $blk.remove();
          }
        }

        editor.$el
          .find("pre[temp='true']")
          .find("code:not(:first-child)")
          .each(function () {
            $(this).prev().append("&#10;");
            $(this).prev().append($(this).html());
            $(this).remove();
          });
        editor.$el.find("pre").removeAttr("temp"); // Unwrap temp divs.
      }

      editor.selection.restore();
    }

    function onPasteBefore(e) {
      if ($(editor.selection.element()).parents("pre").length) {
        if (e && e.clipboardData && e.clipboardData.getData) {
          var clipboard_txt = e.clipboardData.getData("text");
          editor.html.insert(clipboard_txt, true);
          preContentChange()
        }
        return false;
      }
    }

    function showCodeTypePop(e) {
      if (!this.popups.isVisible("customPlugin.popup")) {
        var $block = $(e.target).is("pre")?$(e.target): $(e.target).parents("pre");
        var left = $block.length && $block.offset().left;
        var right = $block.length && $block.offset().left + $block.outerWidth();
        var top = $block.length && $block.offset().top;
        var bottom = $block.length && $block.offset().top + $block.outerHeight();
        var height = $block.outerHeight();
        var width = $block.outerWidth();
        var offset = {
          left: left,
          right: right,
          top: top,
          bottom: bottom,
          height: height,
          width: width,
          x: left,
          y: top,
        };
        setTimeout(()=>{
          this.codeEdit.showPopup(offset);
        },300)
      }
    }

    function hideCodeTypePop(e) {
      var enable = $(e.toElement).hasClass(".fr-popup");
      if(!enable){
        enable = !!$(e.toElement).parents(".fr-popup").length 
      }
      if(!enable){
        this.codeEdit.hidePopup();
      }
    }

    /**
     * 监听当前<pre> 输入行的内容变化，执行 [prettyCode]
     */
    function preContentChange() {
      var _block = editor.selection.element();
      if (!$(_block).is("code") && !$(_block).parents("code").length) return;

      editor.selection.save();
      var block = editor.selection.element();
      var codeElem = $(block).is("code")? $(block):$(block).parents("code")[0];

      // var codeText = $(codeElem).text()
      // var className = codeElem.attr("class");
      // var language = className.split(" ").find(css=> css.match(/(^|\s)language-\S+/g)).split("-")[1];
      // var highlightValue = hljs.highlight(codeText,{language: language})
      // var replaceStr = highlightValue.value.replace(/\u200B/, FE.START_MARKER).replace(/\u200B/, FE.END_MARKER).replace(/\u0020/g, "&nbsp;");
      // $(codeElem).html(replaceStr)

      hljs.highlightElement($(codeElem).get(0));
      var replaceStr = $(codeElem).prop("outerHTML").replace(/\u200B/, FE.START_MARKER).replace(/\u200B/, FE.END_MARKER); // .replace(/\u0020/g, "你好")
      $(codeElem).replaceWith(replaceStr)
      editor.selection.restore();
    }

    // function prettyCode() {
    //   hljs.highlightAll();
    // }

    /**
     * 是否在编辑代码
     */
    function isCodeSnippet() {
      var block = editor.selection.blocks();
      if (!$(block).is("code") && !$(block).parents("code").length) {
        return false;
      }
      return true;
    }

    function refreshOnShow($btn, $dropdown) {
      $dropdown.find('.fr-command.fr-active').removeClass('fr-active').attr('aria-selected', false);
      $dropdown.find(".fr-command[data-param1=\"".concat(_getSelection(), "\"]")).addClass('fr-active').attr('aria-selected', true);
    }

    function _getSelection() {
      var val = $(editor.selection.element()).css('language-*');
      return val;
    }

    return {
      options: {
        "language-plaintext": "plaintext",
        "language-html": "html",
        "language-json": "json",
        "language-java": "java",
        "language-javascript": "javascript",
        "language-bash": "bash",
        "language-sql": "sql",
      },
      _init: _init,
      run: apply,
      prettyCode: preContentChange,
      refreshOnShow: refreshOnShow,
    };
  };

  FE.DefineIcon("insertPreEnter", {
    NAME: "insertPreEnter",
    PATH: "M 0.34375 23.769531 L 2.421875 23.769531 C 2.574219 23.769531 2.699219 23.644531 2.699219 23.492188 L 2.699219 5.546875 L 19.539062 5.546875 L 19.539062 8.074219 C 19.539062 8.308594 19.808594 8.441406 19.988281 8.292969 L 24.90625 4.414062 C 24.972656 4.359375 25.011719 4.28125 25.011719 4.195312 C 25.011719 4.109375 24.972656 4.027344 24.90625 3.976562 L 19.988281 0.0976562 C 19.90625 0.03125 19.789062 0.015625 19.695312 0.0625 C 19.597656 0.109375 19.535156 0.207031 19.539062 0.316406 L 19.539062 2.914062 L 2.285156 2.914062 C 1.058594 2.914062 0.0703125 3.90625 0.0664062 5.128906 L 0.0664062 23.492188 C 0.0664062 23.644531 0.191406 23.769531 0.34375 23.769531 Z M 0.34375 23.769531"
  })

  FE.RegisterCommand("insertPreEnter", {
    title: "上方换行",
    focus: true,
    undo: false,
    callback: function() {
      var editor = this;
      // 对内容进行回车
      // 移动光标到
      var blocks = editor.selection.element();
      var preElem = $(blocks).parents("pre");
      if(preElem.length){
        editor.markers.remove();
        $(preElem).before(FE.MARKERS);
        editor.selection.restore();
        editor.cursor.enter();
        editor.codeEdit.hidePopup();
      }
    }
  })

  FE.DefineIcon("insertEnter", {
    NAME: "insertEnter",
    PATH: "M23.6702128,0 L21.6755319,0 C21.5292553,0 21.4095745,0.119680851 21.4095745,0.265957447 L21.4095745,17.4867021 L5.25265957,17.4867021 L5.25265957,15.0598404 C5.25265957,14.8371011 4.99335106,14.7107713 4.82047872,14.8503989 L0.103058511,18.5738032 C-0.0332446809,18.6801862 -0.0332446809,18.8863032 0.103058511,18.9926862 L4.82047872,22.7160904 C4.99667553,22.8557181 5.25265957,22.7293883 5.25265957,22.5066489 L5.25265957,20.0132979 L21.8085106,20.0132979 C22.9820479,20.0132979 23.9361702,19.0591755 23.9361702,17.8856383 L23.9361702,0.265957447 C23.9361702,0.119680851 23.8164894,0 23.6702128,0 Z"
  })

  FE.RegisterCommand("insertEnter", {
    title: "回车",
    focus: true,
    undo: false,
    callback: function() {
      var editor = this;
      // 对内容进行回车
      // 移动光标到
      var blocks = editor.selection.element();
      var preElem = $(blocks).parents("pre");
      if(preElem.length){
        editor.markers.remove();
        $(preElem).after(FE.MARKERS);
        editor.selection.restore();
        editor.cursor.enter();
        editor.codeEdit.hidePopup();
      }
    }
  })

  // Define an icon.
  FE.DefineIcon("codeLanguage", { NAME: "cog", SVG_KEY: "cogs" });

  // Define a dropdown button.
  FE.RegisterCommand("codeLanguage", {
    title: "code language",
    type: "dropdown",
    displaySelection: function displaySelection(editor) {
      return true;
    },
    defaultSelection: function defaultSelection(editor) {
      return "plaintext";
    },
    html: function html() {
      var c = '<ul class="fr-dropdown-list" role="presentation">';
      var options = this.codeSnippet.options;

      for (var val in options) {
        if (options.hasOwnProperty(val)) {
          c += "<li role=\"presentation\"><a class=\"fr-command\" tabIndex=\"-1\" role=\"option\" data-cmd=\"codeLanguage\" data-param1=\""
            .concat(val, "\" \n        ")
            .concat(val, " title=\"")
            .concat(options[val], "\">")
            .concat(options[val], "</a></li>");
        }
      }

      c += '</ul>';
      return c;
    },
    focus: true,
    undo: false,
    refreshAfterCallback: true,
    callback: function (cmd, val) {
      if (!val) return;
      var editor = this;
      var $elem = editor.selection.element();
      var $pre = $($elem).parents("pre");
      $pre.children("code").removeClass(function (index, css) {
        return (css.match(/(^|\s)language-\S+/g) || []).join(" ");
      });

      $pre.children("code").addClass(val);

      editor.codeSnippet.prettyCode();
    },
    // Callback on refresh.
    refresh: function ($btn) {
      var editor = this;
      var $elem = editor.selection.element();
      var $pre = $($elem).parents("pre");
      var className = $pre.children("code").attr("class");
      var language = className.split(" ").find(css=> css.match(/(^|\s)language-\S+/g));
      $btn.find('> span').text(language.split("-")[1]);
    },
    // Callback on dropdown show.
    refreshOnShow: function ($btn, $dropdown) {
      this.codeSnippet.refreshOnShow($btn, $dropdown);
    },
  });

  // 自定义 code block 代码块
  FE.DefineIcon("code", {
    NAME: "code",
    PATH: "M 5.773438 13.246094 L 10.195312 15.164062 C 10.234375 15.183594 10.265625 15.160156 10.265625 15.117188 L 10.265625 13.988281 C 10.265625 13.945312 10.234375 13.898438 10.191406 13.882812 L 7.15625 12.6875 C 7.117188 12.667969 7.117188 12.644531 7.15625 12.628906 L 10.191406 11.460938 C 10.234375 11.441406 10.265625 11.398438 10.265625 11.351562 L 10.265625 10.214844 C 10.265625 10.171875 10.234375 10.148438 10.195312 10.167969 L 5.773438 12.085938 C 5.730469 12.101562 5.699219 12.152344 5.699219 12.195312 L 5.699219 13.136719 C 5.699219 13.179688 5.730469 13.230469 5.773438 13.246094 Z M 11.28125 16.070312 C 11.40625 16.070312 11.5 16.042969 11.5625 15.988281 C 11.628906 15.929688 11.675781 15.851562 11.710938 15.753906 C 11.742188 15.65625 11.777344 15.519531 11.820312 15.34375 L 13.09375 10.109375 C 13.15625 9.863281 13.1875 9.691406 13.1875 9.597656 C 13.1875 9.46875 13.148438 9.371094 13.066406 9.296875 C 12.988281 9.226562 12.871094 9.191406 12.71875 9.191406 C 12.542969 9.191406 12.425781 9.238281 12.363281 9.34375 C 12.304688 9.449219 12.242188 9.636719 12.175781 9.917969 L 10.90625 15.152344 C 10.847656 15.429688 10.816406 15.601562 10.816406 15.671875 C 10.816406 15.9375 10.96875 16.070312 11.28125 16.070312 Z M 13.804688 15.171875 L 18.226562 13.246094 C 18.269531 13.230469 18.300781 13.179688 18.300781 13.136719 L 18.300781 12.207031 C 18.300781 12.164062 18.269531 12.113281 18.226562 12.097656 L 13.804688 10.1875 C 13.765625 10.171875 13.734375 10.191406 13.734375 10.234375 L 13.734375 11.351562 C 13.734375 11.394531 13.765625 11.441406 13.808594 11.460938 L 16.851562 12.636719 C 16.894531 12.65625 16.894531 12.679688 16.851562 12.695312 L 13.808594 13.890625 C 13.765625 13.90625 13.734375 13.957031 13.734375 14 L 13.734375 15.128906 C 13.734375 15.167969 13.765625 15.191406 13.804688 15.171875 Z M 21.238281 2.761719 L 2.761719 2.761719 C 2.0625 2.761719 1.5 3.324219 1.5 4.019531 L 1.5 19.980469 C 1.5 20.675781 2.0625 21.238281 2.761719 21.238281 L 21.238281 21.238281 C 21.9375 21.238281 22.5 20.675781 22.5 19.980469 L 22.5 4.019531 C 22.5 3.324219 21.9375 2.761719 21.238281 2.761719 Z M 21.238281 19.980469 L 2.761719 19.980469 L 2.761719 6.121094 L 21.238281 6.121094 Z M 21.238281 19.980469",
  });

  FE.RegisterCommand("code", {
    title: "代码块",
    focus: true,
    undo: false,
    refreshAfterCallback: false,
    callback: function () {
      this.codeSnippet.run();
    },
  });
});
