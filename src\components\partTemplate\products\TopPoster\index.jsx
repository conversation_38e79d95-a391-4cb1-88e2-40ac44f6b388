import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { Box, Typography, Button, Stack } from "@mui/material";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../../utils/commonUtils";
import { formatVariantColor } from "../../../../utils/muiThemeUtils";

export default function TopPoster(props) {
  const { data, anchorClick } = props;
  const navigate = useNavigate();
  const location = useLocation();
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData])
    }
  }, [data?.widgetList]);

  const productsClick = (linkUrl) => {
    navigate(linkUrl)
  }
  return (
    <>
      {/* 产品主页-start */}
      <Box className="allProducts-content"
        style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}  
      >
        <Typography 
        variant="h5" 
        display="inline-block" style={{ fontSize: 34 }}>
          {filterWidgetList(moduleData, 1)[0]?.value}
        </Typography>
        <Typography 
        sx={{ margin: '20px auto', maxWidth: '1200px', color: '#666' }} 
        variant="subtitle1" >
          {filterWidgetList(moduleData, 2)[0]?.value}
        </Typography>
        <Box 
        sx={{
          margin: 'auto',
          maxWidth: '800px',
          textAlign: 'center'
        }}>
          <Stack 
          sx={{ flexWrap: 'wrap', justifyContent: 'center' }}
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          >
            {(filterWidgetList(moduleData, 6)[0]?.children || []).map((item, index, arr) => (
              <Button 
              key={item.widgetId}
              className="products-stack-btn"
              variant={formatVariantColor(item.style)?.variant}
              color={formatVariantColor(item.style)?.color}
              onClick={() => {
                  !anchorClick
                   ? productsClick(item.linkUrl)
                   : anchorClick(item.linkUrl)
                }
              }
              >
                {item.value}
              </Button>
            ))}
          </Stack>
        </Box>
      </Box>
      {/* 产品主页-end */}
    </>
  );
}
