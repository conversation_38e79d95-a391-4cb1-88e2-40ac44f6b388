import "./more.css";
/**
 * @description 时间组件
 */
(function (global, factory) {
  factory(require("froala-editor"));
})(this, function (FE) {
  "use strict";

  FE = FE && FE.hasOwnProperty("default") ? FE["default"] : FE;

  Object.assign(FE.DEFAULTS, {
    toolbarInsertMoreButtons: ["taskUL", "pickaday"],
  });

  FE.PLUGINS.insertMore = function (editor) {
    var $ = editor.$;

    function _init() {}

    return {
      _init: _init,
    };
  };

  FE.DefineIcon("insertMore", {
    NAME: "insertMore",
    SVG_KEY: `add`,
  });
  
  FE.RegisterCommand("insertMore", {
    title: "插入更多",
    type: "dropdown",
    icon: "insertMore",
    html: function () {
      let editor =  this;
      // 获取 options的Buttons
      // editor.icon.create(); // 生成图标
      var li_list = [];
      var toolbarInsertMoreButtons = this.opts.toolbarInsertMoreButtons || [];
      

      toolbarInsertMoreButtons.forEach(cmd_name => {
        var cmd_info = FE.COMMANDS[cmd_name];
        if (cmd_info) {
          // button
          li_list.push(`<li class="fr-insertmore-item">
            <a class="fr-command" aria-selected="false" data-cmd="${cmd_name}">
              <span class="fr-insertmore-image">${editor.icon.create(cmd_info.icon || cmd_name)}</span>
              <span class="fr-insertmore-title">${cmd_info.title}</span>
            </a>
          </li>`)
        } else if (cmd_name === '-') {
          li_list.push('<li class="fr-separator fr-hs" role="separator" aria-orientation="horizontal"></li>')
        }
      });
      
      return `<ul class="fr-insertmore">${li_list.join("")}</ul>`;
    },
    undo: false,
    focus: false,
    refreshAfterCallback: false,
    // hasOptions: function hasOptions() {
    //   return true;
    // },
    // refresh: function ($btn) {
    //   // console.log(this.selection.element());
    //   // 
    // },
    // refreshOnShow: function ($btn, $dropdown) {
    //   // console.log(this.selection.element());
    // },
    plugin: "insertMore"
  });
});
