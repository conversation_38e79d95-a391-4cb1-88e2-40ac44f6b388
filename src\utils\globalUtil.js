// import {JsonTurnUrl} from "@common/utils/toolUtil";
import {resolvePath} from "react-router-dom"

let _navigate = null; // 全局history
let _location = null; // 全局location
let _queryClient = null; // 全局 queryClient
let _msgApi = null; //全局弹出消息
let _msgKey = 'default';
let _duration2s = 2; //2秒
let _duration10s = 10; //10秒

let _showMessage = (content,type,msgKey,duration) => {
  var duration = duration || _duration2s; //2秒
  var msgKey = msgKey || _msgKey;
  if(_msgApi)
    _msgApi.open({
      key: msgKey,
      type: type,
      duration: duration+Math.random()*0.1, //使用随机数，避免有时候消息不显示，有时候消息不消失
      content:content
    })
}

export const globalUtil = {
  setGlobalHistory : (navigate,location) => {
    _navigate = navigate;
    _location = location;
  },
  getGlobalHistory : () => _navigate,
  getGlobalLocation : () => _location,
  getGlobalMsgApi : () => _msgApi,
 /*  goBackLoginPage : () => {  // 返回登录页
    // 清除token
    // localStorage.clear();
    let redirectUrl = "";

    if(_location){
      redirectUrl = JsonTurnUrl({
        redirect: _location.pathname + _location.search
      });
    }

    // 如果当前页面处于登录、注册、重置密码 不进行跳转
    if(_location.pathname.indexOf("/login") != -1 || _location.pathname.indexOf("/forgotuserpassword") != -1 || _location.pathname.indexOf("/register") != -1){
     return;
    }
    _navigate?.({pathname: `/login`,search: redirectUrl})
  }, */
  success: (content,msgKey,duration) => {
    _showMessage(content,'success',msgKey,duration);
  },
  info: (content,msgKey,duration) => {
    _showMessage(content,'info',msgKey,duration);
  },
  warning: (content,msgKey,duration) => {
    _showMessage(content,'warning',msgKey,duration);
  },
  error: (content,msgKey,duration) => {
    _showMessage(content,'error',msgKey,duration);
  },
  loading: (content,msgKey,duration) => {
    _showMessage(content,'loading',msgKey,duration);
  },
  setMsgApi: (msgApi) => {
    _msgApi = msgApi;
  },
  setQueryClient: (queryClient) => {
    _queryClient = queryClient;
  },
  getQueryClient: () => _queryClient, // 注意，请不要直接使用，请封装方法后再使用
}





