import React, { useEffect, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import {
  Button
} from "antd";
import {
  CloseOutlined,CheckCircleOutlined,CloseCircleOutlined
} from '@ant-design/icons';
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../utils/commonUtils";

//更多产品
export default function MoreProducts({data}) {
  const navigate = useNavigate();
  const [moreProductData, setMoreProductData] = useState([]);
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData])
    }
  },[data?.widgetList])

  // 查看更多产品
  const moreProductsClick = (linkUrl) => {
    navigate(linkUrl)
  }

  return (
    moduleData.length > 0 ? <div className="MoreProducts" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
      <Button className="MoreProducts-btn" type="link" onClick={() => moreProductsClick(filterWidgetList(moduleData, 4)[0]?.linkUrl)}>
        {filterWidgetList(moduleData, 4)[0]?.value}
      </Button>
    </div>
    : <></>
  );
}