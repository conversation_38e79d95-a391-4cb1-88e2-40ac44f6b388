import React, { useEffect, useState, useRef } from "react";
import Slider from "react-slick";
import {
  Box,
  Typography,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../../utils/commonUtils";

export default function OurUsers(props) {
  const { data } = props;
  const sliderRef = useRef();
  const [moduleData, setModuleData] = useState([]);
  
  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData])
    }
  },[data?.widgetList])

  const sliderSettings = {
    className: "our-users-content-slider",
    dots: false,
    infinite: true,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    speed: 3000,
    autoplaySpeed: 0,
    cssEase: "linear",
    touchMove: false,
  };

  return (
    <>
      {/* 我们的用户-start */}
      <Box className="our-users" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography className="our-users-title" variant="h4">{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
        <Box className="our-users-content">
          <Box
            sx={{ padding: "20px" }}
          >
            <ArrowBackIcon sx={{ fontSize: "34px", color: "#0077f2" }} onClick={() => sliderRef.current.slickPrev()}/>
          </Box>
          <Box className="our-users-content-swiper">
            <Slider ref={sliderRef} style={{ width: '100%' }} {...sliderSettings}>
              {filterWidgetList(moduleData, 6).map((user, index) => (
                <Box key={user.key} className="our-users-content-swiper-li">
                  <Box className="our-users-content-swiper-li-topbar"></Box>
                  <Box className="our-users-content-swiper-li-imagebox">
                    <Box className="our-users-content-swiper-li-imagebox-image">
                      <img height="100%" src={filterWidgetList(user.children, 3)[0]?.value} />
                    </Box>
                  </Box>
                  <Box className="our-users-content-swiper-li-name">{filterWidgetList(user.children, 1)[0]?.value}</Box>
                  <Box className="our-users-content-swiper-li-introduce">{filterWidgetList(user.children, 2)[0]?.value}</Box>
                </Box>
              ))}
            </Slider>
          </Box>
              
          <Box sx={{ padding: "20px" }} onClick={() => sliderRef.current.slickNext()}>
            <ArrowForwardIcon
              sx={{ fontSize: "34px", color: "#0077f2" }}
            />
          </Box>
        </Box>
      </Box>
      {/* 我们的用户-end */}
    </>
  );
}