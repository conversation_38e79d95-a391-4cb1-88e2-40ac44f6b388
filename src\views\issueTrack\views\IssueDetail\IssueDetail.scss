//issue详情
.issue-detail {
  width: 100%;
  background: #fff;
  .ant-tabs-nav .ant-tabs-nav-list {
    padding: 0px 28px;
  }
  
  //评论列表 + 操作历史列表
  &-tabs {
    //调整多条评论之间间距
    .ant-comment-inner {
      margin: 10px 0px;
      padding: 0;
    }
    //调整评论内容外边距
    .fr-editor-preview {
      width: 100%;
      overflow: auto;
      padding: 5px;
    }
    // 去除边线
    .ant-tag {
      border: unset;
    }
  }
  .showIcon:hover .bianjishuru {
    display: block;
  }
  .showIcon {
    p {
      margin: 0;
      font-size: 12px;
    }
    .bianjishuru {
      display: none;
    }
  }
  .optionhistory-title {
    // height: 40px;
    display: flex;
    align-items: baseline;
    font-weight: bold;
    padding: 6px 28px;
    color: #333333;
    border-top: 1px solid #f2f2f2;
    button {
      padding: unset;
    }
  }
  // 历史操作
  .issueHistoryList {
    padding: 5px 28px;
    .ant-list-item {
      border: unset !important;
      .ant-list-item-meta-description {
        color: unset !important;
      }
    }
  }
  .issue-detail-des-title {
    font-size: 12px;
    height: 64px;
    display: flex;
    align-items: center;
    color: #333333;
  }
}

.issue-detail-header {
  width: 100%;
  padding: 10px 28px;
  .issue-detail-title:hover .bianjishuru {
    display: block;
  }
  .issue-detail-title {
    height: 28px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    line-height: 16px;
    display: flex;
    align-items: center;
    .bianjishuru {
      display: none;
    }
  }

  .issue-detail-des {
    margin-top: -5px;
    font-size: 12px;
    font-weight: 400;
    color: #3279fe;
    line-height: 17px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// issue详情
.issue-detail-base {
  padding: 10px 28px 0px 28px;

  .issue-detail-editor-preview {
    display: flex;
    align-items: center;
    flex: auto;
    .fr-editor-preview {
      border: 1px solid #f2f2f2;
    }
  }
  .fr-editor-preview {
    width: 100%;
    overflow: auto;
    padding: 10px;
  }

  // 编辑按钮
  .issue-edit-btn {
    display: none;
  }
  .issue-detail-col:hover .issue-edit-btn {
    display: block;
  } 
  
  .issue-detail-col-block:hover .issue-edit-btn {
    display: block;
  }
  // 一行显示
  .issue-detail-col {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 2px 0px;
  }
  // 多行显示
  .issue-detail-col-block {
    font-size: 14px;
    padding: 2px 0px;
  }
  .issue-detail-col-label{
    width: 100px;
    font-weight: 400;
    color: #666;
  }
  .issue-detail-col-value{
    display: flex;
    flex: auto;
    align-items: center;
    font-weight: 400;
    color: #333;
  }
  // 图片
  .issue-detail-image {
    border: 1px solid #999;
  }
}

.issue-resource {
  padding: 6px 0px;
  border-top: 1px solid #f2f2f2;
  .ant-list-header {
    display: none;
  }
  &-title {
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    margin-left: 28px;
    button {
      margin-left: 5px;
      padding: unset;
    }
  }
}
// 隐藏评论数及空数据区
.comment-list {
  .ant-list-header {
    display: none;
  }
  .ant-list-empty-text {
    display: none;
  }
}

// 评论
.issue-comment {
  padding: 28px 28px 0px 28px;
  &-text {
    display: flex;
    textarea.ant-input {
      line-height: 28px;
    }
  }
  &-options {
    display: flex;
    justify-content: space-between;
    padding: 10px 0px 0px 50px;
    a {
      padding-right: 15px;
    }
    button {
      border-radius: 5px;
    }
  }
}
.tms-comment {
  padding: 0px 28px;
}
.ant-comment {
  padding: 0px 28px;
  .ant-comment-inner {
    margin: 5px 0px;
    padding: 0;
  }
}
.issues {
  flex: auto;
  overflow-y: auto;
  height: 0;
}
//在一定范围内，隐藏滚动条
.issues::-webkit-scrollbar {
  display: none;
}

.Content_header {
  display: flex;
  justify-content: space-between;
}
.order {
  display: flex;
  align-items: center;
}
.astyle {
  margin-right: 30px;
}
.Content_foot {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  .buttonsty {
    margin: 15px;
    border-radius: 5px;
  }
}
.tms-timeline {
  padding-top: 0px !important;
  padding-left: 5px !important;
  .tms-timeline-item-label {
    display: none !important;
  }
}
//取消表格点击行/选中变色
.ant-table-tbody > tr.ant-table-row-selected > td {
  /* background: #e6f7ff; */
  background: unset !important;
  border-color: rgba(0, 0, 0, 0.03);
}

.padding-0 {
  .ant-table-thead > tr > th {
    padding: 5px 8px !important;
  }
  .ant-table-tbody > tr > td,
  .ant-table tfoot > tr > th,
  .ant-table tfoot > tr > td {
    padding: 0px 8px !important;
  }
}

.issueDetail-title {
  padding-left: 28px;
  margin-top: 10px;
  font-weight: bold;
}

.op-btn {
  width: 32px;
  color: #666;
  height: 20px;
  line-height: 20px;
}