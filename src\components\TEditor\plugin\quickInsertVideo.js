(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(require('froala-editor')) :
  (factory(global.FroalaEditor));
}(this, (function (FE) { 'use strict';

  FE = FE && FE.hasOwnProperty('default') ? FE['default'] : FE;

  FE.RegisterQuickInsertButton('quickInsertVideo', {
    icon: 'insertVideo',
    requiredPlugin: 'video',
    title: 'Insert Video',
    undo: false,
    callback: function callback() {
      var editor = this;
      var $ = editor.$;

      if (!editor.shared.$qi_image_input) {
        editor.shared.$qi_video_input = $(document.createElement('input')).attr('accept', 'video/' + editor.opts.videoAllowedTypes.join(', video/').toLowerCase()).attr('name', "quickInsertVideo".concat(this.id)).attr('style', 'display: none;').attr('type', 'file');
        $('body').first().append(editor.shared.$qi_video_input);
        editor.events.$on(editor.shared.$qi_video_input, 'change', function () {
          var inst = $(this).data('inst');

          if (this.files) {
            inst.quickInsert.hide();
            inst.video.upload(this.files);
          } // Chrome fix.


          $(this).val('');
        }, true);
      }

      editor.$qi_video_input = editor.shared.$qi_video_input;
      if (editor.helpers.isMobile()) editor.selection.save();
      editor.events.disableBlur();
      editor.$qi_video_input.data('inst', editor)[0].click();
    }
  });

})));