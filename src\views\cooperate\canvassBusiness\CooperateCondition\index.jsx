import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Stack
} from "@mui/material";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../../utils/commonUtils";

export default function WinFuture(props) {
  const { data } = props;
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  },[data?.widgetList])
  return (
    <>
      {/* 合作条件-start */}
      <Box className="cooperate-condition" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography variant="h5" sx={{ fontSize: "34px" }}>{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
        {!!filterWidgetList(moduleData, 2)[0]?.value && <Typography sx={{ mt: '10px', color: '#666' }} variant="body2">{filterWidgetList(moduleData, 2)[0]?.value}</Typography>}
        {filterWidgetList(moduleData, 6).length > 0 && <Box className="cooperate-condition-stack">
          <Stack
          sx={{ flexWrap: "wrap", justifyContent: 'center', mt: "40px" }}
          direction={{ xs: "column", sm: "row" }}
          spacing={3}
          >
            {filterWidgetList(moduleData, 6).map((item, index) => (
              <Box className="cooperate-condition-stack-li" key={item.widgetId}>
                <Box className="cooperate-condition-stack-li-img">
                  <img height="100%" src={filterWidgetList((item?.children || []), 3)[0]?.value}/>
                </Box>
                <Typography sx={{ margin: '12px 0', fontWeight: 'bold' }} variant="subtitle1">{filterWidgetList((item?.children || []), 1)[0]?.value}</Typography>
                <Typography className="fontsize-12" sx={{ color: '#666' }} variant="body2">
                  {filterWidgetList((item?.children || []), 2)[0]?.value}
                </Typography>
              </Box>
            ))}
          </Stack>
        </Box>}
      </Box>
      {/* 合作条件-end */}
    </>
  );
}
