import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Box
} from "@mui/material";
import "../index.scss";
import {
  team_1301_get_page_part_widget,
} from "../../../api/http_common";
import TopMenuBar from "../../../components/TopMenuBar";
import FooterBar from "../../../components/FooterBar";
import TopAdvert from "./TopAdvert"; // 定制合作-头部广告
import MultiScenario from "./MultiScenario"; // 定制合作-多端多场景
import Solution from "./Solution"; // 解决方案
import DeepCustomization from "./DeepCustomization"; // 深度定制
import DeploymentPlan from "./DeploymentPlan"; // 部署方案
import GuaranteeOperation from "./GuaranteeOperation"; // 保障运营
import DeliveryProcess from "./DeliveryProcess"; // 交付流程
import BottomITeamCustom from "./BottomITeamCustom"; // 底部广告
import ApplyCooperationDrawer from "../../../components/ApplyCooperationDrawer"; // 代理商合作
import ContactUsModal from "../../../components/ContactUsModal";
import {partTypeEnum} from 'src/utils/enum';

export default function Cooperate() {
  const location = useLocation();
  const [partList, setPartList] = useState([]);
  const [applyOpen, setApplyOpen] = useState(false);
  const [contactUsOpen, setContactUsOpen] = useState(false);
  useEffect(() => {
    getPagePartWidget(6666, location.pathname)
  },[])

  const getPagePartWidget = (teamId, menuUrl) => {
    team_1301_get_page_part_widget({teamId, menuUrl}).then(res => {
      if(res.resultCode == 200){
        setPartList([...res.partList || []])
      }
    });
  }

  // 打开联系我们弹窗
  const openApplyDrawer = () => setApplyOpen(true)

  // 关闭联系我们弹窗
  const cancelApplyDrawer = () => setApplyOpen(false)

  // 打开联系我们弹窗
  const openContactUsModal = () => setContactUsOpen(true)

  // 关闭联系我们弹窗
  const cancelContactUsModal = () => setContactUsOpen(false)

  // 定制合作-内容渲染
  const getCustomMadeContent = (type, data, index) => {
    switch (type) {
      case partTypeEnum.part_type_61_coop_customize_banner :
        return <TopAdvert key={index} data={data}/>

      case partTypeEnum.part_type_62_coop_customize_scenario :
        return <MultiScenario key={index} data={data}/>

      case partTypeEnum.part_type_63_coop_customize_solution :
        return <Solution key={index} data={data}/>

      case partTypeEnum.part_type_64_coop_customize_digitalize :
        return <DeepCustomization key={index} data={data}/>

      case partTypeEnum.part_type_65_coop_customize_deploy :
        return <DeploymentPlan key={index} data={data}/>

      case partTypeEnum.part_type_66_coop_customize_service :
        return <GuaranteeOperation key={index} data={data}/>

      case partTypeEnum.part_type_67_coop_customize_deliver :
        return <DeliveryProcess key={index} data={data}/>

      case partTypeEnum.part_type_68_coop_customize_footer :
        return <BottomITeamCustom key={index} data={data} openApplyDrawer={openApplyDrawer} openContactUsModal={openContactUsModal}/>
    
      default:
        return <></>
    }
    
  }
  
  return (
    <div className="cooperate">
      <Box sx={{ height: 76 }} />
      <Box sx={{ width: "100%", padding: "0 100px" }}>
        <Box className="cooperate-TopMenuBar-box">
          <TopMenuBar/>
        </Box>
      </Box>

      {partList.map((item, index) => {
        return <React.Fragment key={index}>
          {getCustomMadeContent(item.partType, item, index)}
        </React.Fragment>
      })}
      
      <FooterBar />
      <ContactUsModal open={contactUsOpen} onCancel={cancelContactUsModal}/>
      <ApplyCooperationDrawer open={applyOpen} onCancel={cancelApplyDrawer}/>
    </div>
  );
}
