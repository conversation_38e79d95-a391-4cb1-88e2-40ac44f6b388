.NoviceGuide-Modal {
    .ant-modal-content {
        padding: 20px 10px;
        background-color: white;
        box-shadow: none;
        .ant-modal-header {
            padding: 0 10px;
            background-color: transparent;
            border: none;
            .ant-modal-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #333;
            }
        }
        .ant-modal-body {
            padding: 0;
        }
        .ant-modal-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            border: none;
        }
    }
}

.carouselChange-icon, .carouselChange-icon:hover, .carouselChange-icon:focus {
    position: absolute;
    top: 258px;
    z-index: 1;
    color: white;
    border-color: #ccc;
    background-color: #ccc;
}

.carouselChange-left {
    left: 24px;
}
.carouselChange-right {
    right: 24px;
}

.NoviceGuide-Carousel {
    .NoviceGuide-Carousel-li {
        padding: 10px 10px 0;
        overflow: hidden;
        .NoviceGuide-Carousel-li-img {
            height: 486px;
            border-radius: 5px;
            overflow: hidden;
            border: 1px solid #f2f2f2;
            background-color: #f2f2f2;
            box-shadow: 0px 0px 5px 1px rgba(0,0,0,0.1);
            .ant-image {
                border-radius: 5px;
            }
        }
        .NoviceGuide-Carousel-li-text {
            margin-top: 10px;
            height: 46px;
            font-size: 14px;
            color: #333;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}