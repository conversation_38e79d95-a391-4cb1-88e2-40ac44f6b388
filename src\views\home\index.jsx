import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { Box } from "@mui/material";
import "./index.scss";
import TopMenuBar from "../../components/TopMenuBar";
import {
  team_1301_get_page_part_widget,
  portal_1101_get_news_list,
} from "../../api/http_common";
import Loading from "../../components/Loading";
import {partTypeEnum} from 'src/utils/enum';

const FooterBar = React.lazy(() => import("../../components/FooterBar"));
const UserEvaluation = React.lazy(() => import("../../components/partTemplate/common/UserEvaluation"));
const TopRecommend = React.lazy(() => import("../../components/TopRecommend"));
const Advertisement = React.lazy(() => import("../../components/partTemplate/common/Advertisement")); // 首页广告
const FunctionIntroduction = React.lazy(() => import("../../components/partTemplate/common/FunctionIntroduction")); // 功能简介
const HowToGetStart = React.lazy(() => import("../../components/partTemplate/home/<USER>")); // 如何开始使用
const WhyChoose = React.lazy(() => import("../../components/partTemplate/home/<USER>")); // 为什么选择iTeam
const OurUsers = React.lazy(() => import("../../components/partTemplate/common/OurUsers")); // 我们的用户
const QuickRecom = React.lazy(() => import("../../components/partTemplate/common/QuickRecom")); // 快捷推荐
const MoreProducts = React.lazy(() => import("../../components/MoreProducts")); // 查看更多产品
const IteamStatement = React.lazy(() => import("../../components/partTemplate/home/<USER>")); // 声明


export default function Home() {
  const location = useLocation();
  const [partList, setPartList] = useState([]);
  const [scrollTop, setScrollTop] = useState(0);
  const [newsList, setNewsList] = useState([]);
  
  useEffect(() => {
    getPagePartWidget(6666, location.pathname || "/")
    getNewsList(6666, 32464380349878, 3, '1', '100')
    window.addEventListener('scroll', scrollChange, true)
    scrollChange()
    return () => {
      window.removeEventListener('scroll', scrollChange, false)
    }
  },[])

  const getPagePartWidget = (teamId, menuUrl) => {
    team_1301_get_page_part_widget({teamId, menuUrl}).then(res => {
      if(res.resultCode == 200){
        setPartList([...res.partList])
      }
    });
  }

  // 获取首页头部轮播广告
  const getNewsList = (teamId, objNodeId, newsType, pageNum, pageSize) => {
    portal_1101_get_news_list({teamId, objNodeId, newsType, pageNum, pageSize}).then(res => {
      if(res.resultCode == 200){
        setNewsList([...res.newsList])
      }
    });
  }

  const scrollChange = () => {
    setScrollTop(document.getElementById('home')?.scrollTop)
  }

  // 首页内容渲染
  const getHomeContent = (partType, data, index) => {
    switch (partType) {
      case partTypeEnum.part_type_1_home_banner :
        return <Advertisement key={index} data={data} isBgFlag={true}/>
      case partTypeEnum.part_type_2_home_intro :
        return <FunctionIntroduction data={data}/>
      case partTypeEnum.part_type_3_home_how_to :
        return <HowToGetStart key={index} data={data}/>
      case partTypeEnum.part_type_4_home_why :
        return <WhyChoose data={data}/>
      case partTypeEnum.part_type_5_clients :
        return <OurUsers key={index} data={data}/>
      case partTypeEnum.part_type_6_feedback :
        return <UserEvaluation key={index} data={data}/>
      case partTypeEnum.part_type_7_more_links :
        return <QuickRecom key={index} data={data}/>
      case partTypeEnum.part_type_8_home_claim :
        return <IteamStatement key={index} data={data}/>
      case partTypeEnum.part_type_9_prod_more : 
        return <MoreProducts key={index} data={data}/>
      default:
        return <React.Fragment key={index}></React.Fragment>
    }
  }


  //样式home会导致首页顶部有一个乱码，会折行
  return (
    <div className="home" id="home">
      <TopRecommend data={newsList}/>
      <Box sx={{ height: 76 }} />
      <Box sx={{ width: "100%", padding: "0 100px" }}>
        <Box className={["home-TopMenuBar-box", scrollTop > 30 ? "start_scrolling" : "scroll_to_top"].join(' ')}>
          <TopMenuBar/>
        </Box>
      </Box>
      <React.Suspense fallback={<Loading/>}>
        {partList.map((item,index) => {
          return getHomeContent(item.partType, item, index)
        })}
      </React.Suspense>
      <FooterBar/>
    </div>
  );
}
