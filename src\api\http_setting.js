import * as httpBase from "../utils/httpBase";
import api_name from "./api_name";
const {api_tms_team} = httpBase;

// setting-202 get_team_allusergrp 获取"全部成员"组成员列表
// https://confluence.ficent.com/pages/viewpage.action?pageId=78368426
export const setting_202_get_team_allusergrp=(data) => httpBase.get(api_tms_team+api_name.setting_202_get_team_allusergrp,data)
// setting-227 save_team_user_tree_width 团队成员树宽度
// https://confluence.ficent.com/pages/viewpage.action?pageId=98406144
export const setting_227_save_team_user_tree_width=(data) => httpBase.post(api_tms_team+api_name.setting_227_save_team_user_tree_width,data)
// setting-228 get_team_user_tree_width 获取团队成员树宽度信息
// https://confluence.ficent.com/pages/viewpage.action?pageId=98406149
export const setting_228_get_team_user_tree_width=(data) => httpBase.get(api_tms_team+api_name.setting_228_get_team_user_tree_width,data)
// setting_320 get_node_priv 获取节点权限
// https://confluence.ficent.com/pages/viewpage.action?pageId=98403045
export const setting_320_get_node_priv=(data) => httpBase.get(api_tms_team+api_name.setting_320_get_node_priv,data, undefined, false)
// setting-402 update_attr_seq_no 变更attr显示排序(上移/下移)
// https://confluence.ficent.com/pages/viewpage.action?pageId=78362333
export const setting_402_update_attr_seq_no= (data) => httpBase.post(api_tms_team + api_name.setting_402_update_attr_seq_no, data)
// setting-404 update_attr_prop_value 保存attr节点prop值
// https://confluence.ficent.com/pages/viewpage.action?pageId=78362555
export const setting_404_update_attr_prop_value= (data) => httpBase.post(api_tms_team + api_name.setting_404_update_attr_prop_value, data) 
// setting-407 get_console_selection_list 获取下拉字典
// https://confluence.ficent.com/pages/viewpage.action?pageId=78370031
export const setting_407_get_console_selection_list= (data) => httpBase.get(api_tms_team + api_name.setting_407_get_console_selection_list, data)
// setting-408 get_console_attrgrp_props 获取attr组(tab)的所有property - 工作区设置端
// https://confluence.ficent.com/pages/viewpage.action?pageId=78370168
export const setting_408_get_console_attrgrp_props = (data) => httpBase.post(api_tms_team + api_name.setting_408_get_console_attrgrp_props, data)
// setting-409 get_team_attrgrp_props 获取attr组(tab)的所有property - 工作区端
// https://confluence.ficent.com/pages/viewpage.action?pageId=78370600
export const setting_409_get_team_attrgrp_props = (data) => httpBase.post(api_tms_team + api_name.setting_409_get_team_attrgrp_props, data) 
// team-501 trash_obj_node 删除节点(放回收站)
// https://confluence.ficent.com/pages/viewpage.action?pageId=78361955
export const team_501_trash_obj_node= (data) => httpBase.get(api_tms_team + api_name.team_501_trash_obj_node, data) 
// team-571 get_space_valid_user_list 获取协作群有效的人员列表   https://confluence.ficent.com/pages/viewpage.action?pageId=98415129
export const team_571_get_space_valid_user_list = (data) => httpBase.get(api_tms_team+api_name.team_571_get_space_valid_user_list,data)