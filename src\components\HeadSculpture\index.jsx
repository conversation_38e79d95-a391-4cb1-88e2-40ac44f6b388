import React, { useEffect, useState, useRef } from "react";
import { useQuery,useQueryClient } from "@tanstack/react-query";
import { useNavigate, useLocation } from "react-router-dom";
import { defer } from "react-router-dom"
import {
  Avatar,
  Box,
  Toolbar,
  Typography,
  Menu,
  Link,
  Grid,
  MenuItem,
  Button,
  Container,
  IconButton,
  Popover,
  Stack,
} from "@mui/material";
import "./index.scss";
import avatar from "../../assets/images/avatar.png"

// 页头
export default function HeadSculpture(props) {
  const { sx={}, src, userName } = props;
  return (
    <>
      <Avatar sx={{...sx}} src={src || avatar}/>
    </>
  );
}
