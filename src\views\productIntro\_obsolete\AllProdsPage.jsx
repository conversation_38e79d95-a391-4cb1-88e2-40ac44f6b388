import React, { useEffect, useState, useRef } from "react";
import { useLocation } from "react-router-dom";
import {Box, Button, Tab, Tabs,} from "@mui/material";
import CheckIcon from "@mui/icons-material/Check";
import {team_1301_get_page_part_widget,} from "../../../api/http_common";
import "./AllProdsPage.scss";
import TopMenuBar from "../../../components/TopMenuBar";
import FooterBar from "../../../components/FooterBar";
import TopPoster from "../../../components/partTemplate/products/TopPoster"; // 头部广告
import ProductDescription from "./ProductDescription"; // 产品说明
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../utils/commonUtils";
import { formatVariantColor } from "../../../utils/muiThemeUtils";
import {partTypeEnum} from '@/utils/enum';

//全部产品，这个页面有Bug，一般不会走到这个页面来  http://localhost:3000/#/allproducts
export default function AllProdsPage() {
  const location = useLocation();
  const [partList, setPartList] = useState([]);
  useEffect(() => {
    getPagePartWidget(6666, location.pathname)
  },[])

  const getPagePartWidget = (teamId, menuUrl) => {
    team_1301_get_page_part_widget({teamId, menuUrl}).then(res => {
      if(res.resultCode == 200){
        setPartList([...res.partList])
      }
    });
  }

  // 点击定位锚点
  const posterAnchorPositon = (anchor) => {
    setTimeout(() => {
      let anchorElement = document.getElementById(anchor);
      anchorElement?.scrollIntoView()
    }, 500);
  }

  // 全部产品内容渲染
  const getAllProductsContent = (data) => {
    switch (data.partType) {
      case partTypeEnum.part_type_31_prod_more_banner : // 产品主页
        return <TopPoster key={data.widgetId} data={data} anchorClick={posterAnchorPositon}/>
      case partTypeEnum.part_type_32_prod_more_intro : // 操作系统
        return <HomeSnapshot key={data.widgetId} data={data}/>
      case partTypeEnum.part_type_33_prod_more_single_intro : // 单个产品介绍
        return <ProductDescription key={data.widgetId} data={data}/>
      default:
        return <React.Fragment></React.Fragment>
    }
  }

  return (
    <div className="allProducts">
      <Box sx={{ height: 76 }} />
      <Box sx={{ width: "100%", padding: "0 100px" }}>
        <Box className="allProducts-TopMenuBar-box">
          <TopMenuBar />
        </Box>
      </Box>    
      {partList.map((item,index) => {
        return getAllProductsContent(item)
      })}  
      <FooterBar/>
    </div>
  );
}


// 更多产品组合说明
function HomeSnapshot({data}) {
  const [snapshotValue, setSnapshotValue] = useState(0);  
  const [title, setTitle] = useState("");
  const [moduleData, setModuleData] = useState([]);
  const [currentModuleItem, setCurrentModuleItem] = useState([]);

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let title = filterWidgetList(formatTreeData([], data.widgetList), 1)[0]?.value || ""
      let formatData = filterWidgetList(formatTreeData([], data.widgetList), 5)
      setTitle(title)
      setModuleData([...formatData])
      setCurrentModuleItem([...formatData[0]?.children || []])
      setSnapshotValue(filterWidgetList(data.widgetList, 5)[0]?.widgetId)
    }
  },[data?.widgetList])

  useEffect(() => {
    if(snapshotValue) {
      let arr_ = moduleData.filter(el => el.widgetId === snapshotValue)[0]?.children || []
      setCurrentModuleItem([...arr_])
    }
  },[snapshotValue])

  // 切换简介tabs
  const snapshotValueChange = (e, value) => {
    setSnapshotValue(value);
  };

  // 获取模板排列位置
  const getDemoPosition = (type) => {
    let className_ = "";
    switch (type) {
      case "left":
      case "right":
        className_ = "home-snapshot-" + type
        break;    
      default:
        break;
    }
    return className_
  }

  return (
    <Box 
      className={[
        "home-snapshot", 
        getDemoPosition(filterWidgetList(moduleData, 11)[0]?.value)
      ].join(" ")}
      style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}
    >
      {!!title && <div className="home-snapshot-title">{title}</div>}
      <Box className="home-snapshot-content">
        <Tabs
          className="home-snapshot-content-tabs tms-portal-tabs"
          value={snapshotValue}
          centered
          onChange={snapshotValueChange}
        >
          {moduleData.map((item, index) => (
            <Tab key={item.widgetId} value={item.widgetId} label={item.value} />
          ))}
        </Tabs>

        <Box className="home-snapshot-content-tabsBody">
          <Box className="home-snapshot-content-tabsBody-left">
            <div className="tabsBody-left-title">
              {filterWidgetList(currentModuleItem, 1)[0]?.value}
            </div>
            <div className="tabsBody-left-textList">
              {filterWidgetList(currentModuleItem, 2).map((item, index) => (
                <div className="tabsBody-left-text" key={item.widgetId}>
                  <CheckIcon sx={{ fontSize: 18, color: "#1683f3" }} />
                  <div style={{ marginLeft: 10, fontSize: 12 }}>
                    {item.value}
                  </div>
                </div>
              ))}
            </div>
            <Button
            sx={{
              mt: "30px",
              width: 120,
              borderRadius: 10,
            }}
            variant={formatVariantColor(filterWidgetList(currentModuleItem, 4)[0]?.style)?.variant}
            color={formatVariantColor(filterWidgetList(currentModuleItem, 4)[0]?.style)?.color}
            href={filterWidgetList(currentModuleItem, 4)[0]?.linkUrl}
            >
              {filterWidgetList(currentModuleItem, 4)[0]?.value}
            </Button>
          </Box>
          <Box className="home-snapshot-content-tabsBody-right" sx={!filterWidgetList(currentModuleItem, 3)[0]?.value ? {visibility: 'hidden'} : {visibility: 'visible'}}>
            <Box className="home-snapshot-content-tabsBody-right-topbar">
              <Box className="white-bar"></Box>
              <Box className="dot-list">
                <Box className="dot-li dot-red"></Box>
                <Box className="dot-li dot-yellow"></Box>
                <Box className="dot-li dot-green"></Box>
              </Box>
            </Box>
            <img width="800" src={filterWidgetList(currentModuleItem, 3)[0]?.value} />
          </Box>
        </Box>
      </Box>
    </Box>
  )
}