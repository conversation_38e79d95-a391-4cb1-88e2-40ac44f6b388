import React, { useEffect, useRef, useState } from "react";
import { <PERSON>, Typo<PERSON>, Button, Tab, Tabs } from "@mui/material";
import { Image } from "antd";
import CheckIcon from "@mui/icons-material/Check";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData, findWidget } from "../../../../utils/commonUtils";
import { formatVariantColor } from "../../../../utils/muiThemeUtils";
import Slider from "react-slick";
import { VideoJsWrap } from "../../../VideoJs";

const videoJsOptions = {
  controls: true,
  playing: false,
  playsinline: true,
  muted: false,
  volume: 0
};

// 功能简介-默认模板
export default function FunctionIntroduction({data}) {
  const [snapshotValue, setSnapshotValue] = useState();
  const [moduleData, setModuleData] = useState([]);
  const [currentModuleItem, setCurrentModuleItem] = useState([]);
  const sliderRef = useRef(null);
  const carouselListRef = useRef([]);
  const [activeIndex,setActiveIndex] = useState(0);
  const videoJsRefs = useRef({});

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      let currentModuleItem_ = filterWidgetList(formatData, 5)[0]?.children || [];
      let childWidgets = data.widgetList.filter(item => currentModuleItem_.some(itemx => item.parentId === itemx.widgetId));
      let currentIndex_ = filterWidgetList(formatData, 14)[0]?.value || 0;
      setModuleData([...formatData])
      setSnapshotValue(filterWidgetList(formatData, 5)[parseInt(currentIndex_) || 0]?.widgetId)
      setCurrentModuleItem([...currentModuleItem_,...childWidgets]);
      carouselListRef.current = [...currentModuleItem_,...childWidgets].filter(el => el.widgetType === 3 || el.widgetType === 10 || el.widgetType === 15).map((item, index) => "id" + item.widgetId)
    }
  },[data?.widgetList])

  useEffect(() => {
    if(snapshotValue) {
      let arr_ = filterWidgetList(moduleData, 5).filter(el => el.widgetId === snapshotValue)[0]?.children || [];
      let childWidgets = data?.widgetList.filter(item => arr_.some(itemx => item.parentId === itemx.widgetId));
      carouselListRef.current = [...arr_,...childWidgets].filter(el => el.widgetType === 3 || el.widgetType === 10).map((item, index) => "id" + item.widgetId)
      setCurrentModuleItem([...arr_,...childWidgets])
    }
  },[snapshotValue])

  // 切换简介tabs
  const snapshotValueChange = (e, value) => {
    carouselListRef.current = []
    setSnapshotValue(value);
  };

  // 获取模板排列位置
  const getDemoPosition = (type) => {
    let className_ = "";
    switch (type) {
      case "left":
      case "right":
        className_ = "FunctionIntroduction-" + type
        break;    
      default:
        break;
    }
    return className_
  }

  
  const setVideoRef = (widgetId,_ref) => {
    videoJsRefs.current[widgetId] = _ref;
  }

  // 点击播放按钮
  const videoPlay = (e) => {
    sliderRef.current?.slickPause && sliderRef.current.slickPause() // 用户手动播放视频，暂停轮播的自动播放
  }

  // 暂停播放
  const videoPause = (e) => {
    sliderRef.current?.slickPlay && sliderRef.current.slickPlay() // 用户手动暂停视频/播放时长完毕后暂停，启动轮播的自动播放
  }

  const slickBeforeChange = (current, next) => {
    // console.log("slickBeforeChange", current, next);
    setActiveIndex(next);
    // const currentDocs = document.querySelectorAll(`#${carouselListRef.current[current]}`)
    // currentDocs.forEach((doc, index) => { // 判断是视频元素
    //   if(doc?.nodeName === "VIDEO") {
    //     if(!(doc.paused || doc.ended || doc.seeking || doc.readyState < doc.HAVE_FUTURE_DATA)) { // 判断视频是否在播放
    //       console.log("在播放"); // 暂停视频
    //       doc.pause()
    //     } else {
    //       console.log("没有播放");
    //     }
    //   }
    // })
  }

  return (
    <>
      {/* iTeam简介start */}
      <Box 
        className={[
          "FunctionIntroduction", 
          getDemoPosition(filterWidgetList(moduleData, 11)[0]?.value)
        ].join(" ")}
        style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}
      >
        {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography className="FunctionIntroduction-title" variant="h5">
          {filterWidgetList(moduleData, 1)[0]?.value}
        </Typography>}
        {filterWidgetList(moduleData, 5).length > 0 && <Box className="FunctionIntroduction-content">
          <Tabs
            className="FunctionIntroduction-content-tabs tms-portal-tabs"
            value={snapshotValue}
            centered
            onChange={snapshotValueChange}
          >
            {filterWidgetList(moduleData, 5).map((item, index) => (
              <Tab key={item.widgetId} value={item.widgetId} label={item.value} />
            ))}
          </Tabs>
          {/* {formatTreeData([], partList[1]?.widgetList || [])[snapshotValue].children} */}
          <Box className="FunctionIntroduction-content-tabsBody">
            <Box className="FunctionIntroduction-content-tabsBody-left">
              <Typography
                sx={{ fontWeight: "bold" }}
                className="tabsBody-left-title"
                variant="subtitle1"
              >
                {filterWidgetList(currentModuleItem, 1)[0]?.value}
              </Typography>
              <Box className="tabsBody-left-textList">
                {filterWidgetList(currentModuleItem, 2).map((item, index) => (
                  <Box key={item.widgetId} className="tabsBody-left-text">
                    <CheckIcon sx={{ fontSize: 18, color: "#1683f3" }} />
                    <Typography
                      className="fontsize-14"
                      sx={{ ml: "10px" }}
                      variant="body2"
                    >
                      {item.value}
                    </Typography>
                  </Box>
                ))}
              </Box>
              {!!filterWidgetList(currentModuleItem, 4)[0]?.value && <Button
              sx={{
                mt: "30px",
                width: 120,
                borderRadius: 10,
              }}
              variant={formatVariantColor(filterWidgetList(currentModuleItem, 4)[0]?.style)?.variant}
              color={formatVariantColor(filterWidgetList(currentModuleItem, 4)[0]?.style)?.color}
              >
                {filterWidgetList(currentModuleItem, 4)[0]?.value}
              </Button>}
            </Box>
            <Box className="FunctionIntroduction-content-tabsBody-right">
              <Box className="FunctionIntroduction-content-tabsBody-right-topbar">
                <Box className="white-bar"></Box>
                <Box className="dot-list">
                  <Box className="dot-li dot-red"></Box>
                  <Box className="dot-li dot-yellow"></Box>
                  <Box className="dot-li dot-green"></Box>
                </Box>
              </Box>
              {
                [...filterWidgetList(currentModuleItem, 3), ...filterWidgetList(currentModuleItem, 10)].length > 1
                 ? 
                  <Slider {
                    ...{
                      dots: true,
                      infinite: true,
                      lazyLoad: "lazyLoad",
                      slidesToShow: 1,
                      slidesToScroll: 1,
                      // autoplay: true,
                      // speed: 1000,
                      // autoplaySpeed: 4000,
                      touchMove: false,
                      arrows: true,
                      pauseOnHover: true
                    }
                  }
                  className="portal-slider"
                  beforeChange={slickBeforeChange}
                  ref={sliderRef}>
                    {[...(currentModuleItem || []).filter(el => el.widgetType === 3 || el.widgetType === 10)].map((item, index) => (
                      item.widgetType === 10 ? 
                      <VideoJsWrap 
                        key={item.widgetId} 
                        isActive={activeIndex === index}
                        widgetId={item.widgetId}
                        light={<img width={"100%"} src={findWidget(currentModuleItem,15,item.widgetId)?.value} alt='Thumbnail' />}
                        setVideoRef={setVideoRef}
                        {...videoJsOptions} 
                        url={item.value} 
                        onPlay={videoPlay} 
                        onPause={videoPause} 
                        onEnded={videoPause}/>
                      : 
                      <Image 
                        key={item.widgetId} 
                        src={item.value}
                        style={{width: "800px", height: "434px"}}/>
                    ))}
                  </Slider>
                 : ([...(currentModuleItem || []).filter(el => el.widgetType === 3 || el.widgetType === 10)].map((item, index) => (
                  item.widgetType === 10 ? 
                  <VideoJsWrap 
                    key={item.widgetId} 
                    isActive={activeIndex === index}
                    widgetId={item.widgetId}
                    light={<img width={"100%"} src={findWidget(currentModuleItem,15,item.widgetId)?.value || ''} alt='Thumbnail' />}
                    setVideoRef={setVideoRef}
                    {...videoJsOptions} 
                    url={item.value} 
                    onPlay={videoPlay} 
                    onPause={videoPause} 
                    onEnded={videoPause}/>
                  : 
                  <Image 
                    key={item.widgetId} 
                    src={item.value}
                    style={{width: "800px", height: "434px"}}/>
                )))
              }
            </Box>
          </Box>
        </Box>}
      </Box>
      {/* iTeam简介end */}
    </>
  );
}
