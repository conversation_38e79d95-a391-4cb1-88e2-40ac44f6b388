import React, { useEffect, useState, useRef } from "react";
import { QueryObserver, useQuery,useQueryClient } from "@tanstack/react-query";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { defer } from "react-router-dom"
import {
  AppBar,
  Box,
  Toolbar,
  Typography,
  Menu,
  Grid,
  MenuItem,
  Button,
  Container,
  Popover,
  Stack,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import {
  team_630_user_log_out,
} from "../../api/http_common";
import {team_1201_get_menu_list_query} from "../../api/query/query"
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import "./index.scss";
import HeadSculpture from "../HeadSculpture"; // 头像
import PortalLink from "../PortalLink"; // 通用link
import { skipOS } from "../../utils/commonUtils";
import {menuFuncUi} from "../../views/MenuContextConfig";
import { globalEventBus } from "../../utils/eventBus";
import LoginModal from "../../views/LoginDialog";
import { useAuthContext } from "@/context/AppAuthContextProvider";
import {globalUtil} from "../../utils/globalUtil";
import Icon from '@ant-design/icons';
import { ReactComponent as LogoHeader } from "../../assets/svg/iteamHeader.svg";

// 页头
export default function TopMenuBar({width="1220px"}) {
  const queryClient = useQueryClient();
  //const [anchorElNav, setAnchorElNav] = useState(null);
  const [headMenuList, setHeadMenuList] = useState([]);
  const [avatarAnchorEl, setAvatarAnchorEl] = useState(null);
  const team1201Result = useQuery(team_1201_get_menu_list_query(6666))
  const authActions = useAuthContext();

  useEffect(() => {
    if(Array.isArray(team1201Result.data?.headMenuList)) {
      setHeadMenuList([...team1201Result.data.headMenuList.filter(el => el.id !== 1001)])
    }
  },[team1201Result.data?.headMenuList])

  const formatTreeData = (cur=[],arr=[]) => {
    // 生成根目录
    if(cur.length == 0){
      // 格式化数据格式
      arr = arr.map(item => {
        return {
          key: item.id,
          ...item
        }
      })
      cur = arr.filter(item => arr.every(itemx => itemx.id != item.parentId));
    }
    cur.forEach(item => {
      let childs = arr.filter(itemx => itemx.parentId == item.id);
      if(childs.length){
        let turnChilds = formatTreeData(childs,arr);
        item.children = turnChilds;
      }
    });
    return cur;
  }

/*  const handleOpenNavMenu = (event) => {
    setAnchorElNav(event.currentTarget);
  };

  const handleCloseNavMenu = () => {
    setAnchorElNav(null);
  };*/

  // 打开登录/注册弹窗
  const openLogon = () => {
    globalEventBus.emit("openLoginModal", "", {},);
  }

  // 退出登录
  const logOut = () => {

    authActions.logout()
      .then(result => {
        if(result.resultCode == 200){
          setAvatarAnchorEl(null)
          queryClient.invalidateQueries();
          globalUtil.success('退出成功',"",1)
          // setTimeout(() => {
          //   window.location.reload()
          // }, 1000)
        }
      })
  }

  // 跳转个人中心
  const personalClick = () => {
    let url_ = `${process.env.REACT_APP_TMS_URL}/personal/personaldata`
    window.open(url_)
  }

  return (
    <>
      <AppBar
        style={{width:width}}
        className="TMS-AppBar"
        position="static"
        color="inherit"
        sx={{
          position: "relative",
          zIndex: 99,
          width: "100%",
          boxShadow: "none",
          backgroundColor: "transparent",
        }}
      >
        <Container sx={{ padding: "0 !important" }}>
          <Toolbar disableGutters>

            <Box
              className="TMS-menu-grid"
              sx={{
                flexGrow: 1,
                justifyContent: "center",
                alignItems: "center",
                display: "flex",
              }}
            >
              <Grid
                sx={{
                  display: "flex",
                  alignItems: "center",
                }}
                container
              >
                <Grid>
                  <Typography
                    variant="h6"
                    noWrap
                    component="a"
                    href="/"
                    sx={{
                      mr: 2,
                      display: "flex",
                      // fontSize: 40,
                      // fontWeight: 700,
                      // color: "#0077F2",
                      textDecoration: "none",
                      margin: 0,
                    }}
                  > 

                    <Icon component={LogoHeader}  style={{fontSize:150,height:75,color:'#0077f2',transform:"translate(0,-50%)"}} />
                  </Typography>
                </Grid>
                {formatTreeData([], headMenuList).map((menu, index) => (
                  <Grid key={menu.key} sx={{ padding: "20px 45px" }}>
                    <TopMenuBarItem menu={menu} />
                  </Grid>
                  // <Grid sx={{ padding: '38px 45px' }}>
                  //   <Link
                  //     key={page.key}
                  //     sx={{
                  //       cursor: "pointer",
                  //     }}
                  //     underline="none"
                  //     color="inherit"
                  //   >
                  //     {page.title}
                  //   </Link>
                  // </Grid>
                ))}
              </Grid>
            </Box>

            <Box sx={{ justifyContent: "end", display: { md: "flex" } }}>
              <Button
                className="tms-operating-system"
                sx={{ width: 108, borderRadius: 20, overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}
                size="small" 
                variant="contained"
                color="color_2ac465_ffffff_2ac465"
                onClick={() => skipOS(authActions.isAuthenticated)}
                startIcon={
                  <span 
                  className="iconfont jinru"/>
                }>
                进入系统
              </Button>
              {!authActions.isAuthenticated && <Button
                className="btn-2ac465"
                sx={{ width: 100, borderRadius: 20 }}
                variant="contained"
                onClick={openLogon}>
                登录/注册
              </Button>}
              {authActions.isAuthenticated && <>
              <Box sx={{ cursor: 'pointer' }} onClick={e => setAvatarAnchorEl(e.currentTarget)}>
                  <HeadSculpture src={authActions.loginInfo.userInfo?.avatar} userName={authActions.loginInfo.userInfo?.userName} />
                </Box>
                <Popover
                  open={Boolean(avatarAnchorEl)}
                  anchorEl={avatarAnchorEl}
                  onClose={() => setAvatarAnchorEl(null)}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                  }}>
                  <Box className="login-Popover-content">
                    <Box className="login-Popover-content-top">
                      <Typography className="fontsize-12" variant="subtitle1">{authActions.loginInfo.userInfo?.userName}</Typography>
                      <Typography className="fontsize-12" sx={{ color: '#999'}} variant="body2">账号ID:{authActions.loginInfo.userInfo?.userId}</Typography>
                    </Box>
                    <Box className="login-Popover-content-bottom">
                      <Typography className="fontsize-12" variant="subtitle1" onClick={personalClick}>个人中心</Typography>
                      <Typography className="fontsize-12" variant="subtitle1">版本历史<span style={{marginLeft: 6, color:"#999"}}>当前版本:{process.env.REACT_APP_VERSION}</span></Typography>
                      <Typography className="fontsize-12" variant="subtitle1" onClick={logOut}>退出</Typography>
                    </Box>
                  </Box>
                </Popover>
              </>}

            </Box>
          </Toolbar>
        </Container>
      </AppBar>
      {menuFuncUi.map(item => item)}
      <LoginModal/>
    </>
  );
}

// 首页顶部导航菜单数据
function TopMenuBarItem(props) {
  const { menu } = props;
    /*const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleClick = (event) => {
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
      setAnchorEl(null);
    };*/

  // 点击菜单跳转
  const menuUrlClick = (url) => {
    if(!url) return
    window.location.href = url
  }

  return !menu.children ? (
    <Button>
      <PortalLink 
        to={menu.menuUrl} 
        state={{menuUrl: menu.menuUrl}}
        name={menu.name} 
        style={{
          color:
          window.location.hash.indexOf(menu.menuUrl) > -1
            ? "#0077f2"
            : "#333",
        }}
      />
    </Button>

  ) : (
    menu.modelType === 1
     ? <MenuHorizontal menu={menu} menuUrlClick={menuUrlClick}/>
     : <MenuVertical menu={menu} menuUrlClick={menuUrlClick}/>
  );
}

// 导航菜单模板-横向
function MenuHorizontal(props) {
  const { menu, menuUrlClick } = props;
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  // 过滤数据
  const filterWidgetList = (data, type) => {
    return data.filter(el => el.modelType === type)
  }
  
  return (
    <>
      <Button
        sx={{ color: "#333" }}
        endIcon={<KeyboardArrowDownIcon />}
        aria-controls={open ? 'basic-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleClick}
      >
        {menu.name}
      </Button>
      <Popover
        className="MenuHorizontal-Popover"
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box className="MenuHorizontal-Popover-content">
          <Stack
          className="MenuHorizontal-Popover-stack"
          sx={{ flexWrap: "wrap", justifyContent: "center" }}
          direction={{ xs: "column", sm: "row" }}
          spacing={5}
          >
            {
              (menu.children.filter(el => el.modelType !== 3)).map((item, index) => (
                <Box key={item.id} className="MenuHorizontal-Popover-stack-li">
                  <Typography sx={{ fontSize: 14, fontWeight: 'bold' }} variant="subtitle1">{item.name}</Typography>
                  {
                    Array.isArray(item?.children)
                     &&
                     <Box className="Popover-links">
                      {item.children.map((link, index1) => (
                        <PortalLink 
                        key={link.key} 
                        className="fontsize-14" 
                        style={{ marginTop: 10 }}
                        to={link.menuUrl} 
                        state={{menuUrl: link.menuUrl}}
                        name={<div dangerouslySetInnerHTML={{ __html: decodeURIComponent(link.name) }}/>}
                        />
                      ))}
                    </Box>}
                </Box>
              ))
            }
          </Stack>
          {filterWidgetList(menu.children, 3).length > 0
             &&
             <Box className="Popover-right-box">
              <img width="300px" height="180px" src={filterWidgetList(menu.children, 3)[0].name}/>
              <Button 
                className="Popover-right-box-btn" 
                variant="text" 
                href={process.env.REACT_APP_TMS_URL}
                target="_blank">
                开始使用 iTeam系统 &gt;
              </Button>
             </Box> 
          }
        </Box>
      </Popover>
    </>
  )
}

// 导航菜单模板-纵向
function MenuVertical(props) {
  const { menu, menuUrlClick } = props;
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  return (
    <>
      <Button
        id="basic-button"
        sx={{ color: "#333" }}
        endIcon={<KeyboardArrowDownIcon />}
        aria-controls={open ? 'basic-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleClick}
      >
        {menu.name}
      </Button>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        {menu.children.map((item, index) => (
          <Link key={item.key} to={item.menuUrl} state={{menuUrl: item.menuUrl}}>
            <MenuItem key={item.key}>
              <span className="PortalLink fontsize-12">{item.name}</span>
            </MenuItem>
          </Link>
        ))}
      </Menu>
    </>
  )
}
