import React, { useEffect, useRef, useState } from "react";
import { Row, Col, Button, Dropdown, Input, Menu, Space, Layout, List, Select, Pagination, Modal, Popover, Radio } from "antd";
import { ExclamationCircleOutlined, SwapLeftOutlined } from '@ant-design/icons';
import { formatSvg, getIconValueByIdType } from "@/views/issueTrack/utils/ArrayUtils";
import * as httpCommon from "@/api/http";
import copy from "copy-to-clipboard";
import { eConsoleNodeId, ePagination, eTreeOpType } from "@/utils/enum";
import { useQueryClient } from "@tanstack/react-query";
import { EllipsisOutlined } from "@ant-design/icons";
import { Outlet, useLocation, useNavigate, useOutletContext, useParams } from "react-router-dom";
import { ResizableBox } from "react-resizable";
import { useQuerySettin228_getTeamUserConfig } from "@/views/issueTrack/service/issueHooks";
import { globalUtil } from "@/utils/globalUtil";
// import ContextBoard from "@/components/ContextBoard";
import { globalEventBus } from "@/utils/eventBus";
import CreateIssueModal from "../IssueHome/CreateIssueModal";
import { issue512, issue107 } from "@/api/query/query_key";
import * as http from "@/api/http";
import { issue_512_get_issue_total_query, team_553_get_front_query_query } from "@/api/query/query";
import { useAuthContext } from "@/context/AppAuthContextProvider";
import { validateAuthActions } from "@/utils/commonUtils";
import "./Issuelist.scss";

const { Sider } = Layout;

//Issue短列表，中间第二列短列表
export default function Issuelist() {

  const authActions = useAuthContext();
  
  //totalCnt: issue总记录数, pageNo: 当前页码, issueList:当前页的至多30条的issue列表, currentIssueIdx:当前选中的issue在issueList中的序号
  const { totalCnt, issueList, pageNo, setPageNo, subclassAttrList,  selectionList, spaceUserList, /* userList, objInfo, */ iconSelectionLid,
    issueNodeId,  previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg, keywords, gotoPreviousIssue, gotoNextIssue,
    ascendingFlg, setAscendingFlg, orderBy, setOrderBy, setIssueSearchParams, criteriaList, navId, createIssueModalVisibleFlg, setCreateIssueModalVisibleFlg,
     pageSize, setCurrentIssueIdx, countType } = useOutletContext(); //从IssueHome获取到上下文数据*/
  
  const queryClient = useQueryClient();
  const teamId =  process.env.REACT_APP_WORKORDER_TEAMID;
  const issueListNodeId =  process.env.REACT_APP_WORKORDER_NODEID;
  
  // 拖拽的句柄： <DraggableCore> not mounted on DragStart!
  // https://github.com/react-grid-layout/react-resizable/issues/200
  const handle = (resizeHandle, ref) => {
    return <div className="ia-splitter-handle" ref={ref}>
      <div className="ia-splitter-handle-highlight"></div>
    </div>
  };

  const [refreshingFlg, setRefreshingFlg] = useState(false); //是否正在"刷新中"
  const [visibleTip, setVisibleTip] = useState(false);//删除弹窗
  const [issueItem, setIssueItem] = useState({});//
  const [navQuery, setNavQuery] = useState({}); // tms系统提交工单保存的信息

  // null值不为空，无法设置默认值260
  const { data: { treeWidth: width, expandNodeIds } = { width: 260, expandNodeIds: [] },
    isLoading: isLoadingTeamConfig,
    refetch: refetchTeamConfig
  } = useQuerySettin228_getTeamUserConfig(teamId, issueListNodeId);

  // tms系统提交工单跳转至官网页面，自动打开新建issue对话框
  useEffect(()=>{
    if(!!navId){
      autoOpenCreateIssueModal();
    }
  },[navId])

  async function autoOpenCreateIssueModal () {
    const query = team_553_get_front_query_query(teamId, navId);
    const result = queryClient.getQueryData(query.queryKey) ?? (await queryClient.fetchQuery({ ...query, cacheTime: Infinity }));
    if (result.resultCode == 200 && !!result.queryJson) {
      const queryJson = JSON.parse(result.queryJson);
      setNavQuery(queryJson);
    }
    setCreateIssueModalVisibleFlg(true);
  }

  //刷新issue列表
  function refreshIssueList() {
    queryClient.invalidateQueries(issue107)
    setTimeout(() => {
      setRefreshingFlg(false);
    }, 500);
  }

  // 宽度拖动中...
  const onResize = (event, { element, size, handle }) => {
  }

  // 调整宽度
  const onResizeStop = (event, { element, size, handle }) => {
    const request = {
      teamId,
      nodeId: issueListNodeId, //中间数展开节点
      treeWidth: size.width,
      // expandNodeIds:[],
    }
    httpCommon.setting_227_save_team_user_tree_width(request).then((res) => {
      if (res.resultCode === 200) {

      }
    })
  }
  // 加载更多 - 长短列表
  function gotoPageNo(page, pageSize) {
    setCurrentIssueIdx(0); // 初始化第一条
    setPageNo(page); //触发 useQueryIssue017_getIssueList 再次调用
    document.querySelector(`.issue-list`).scrollTop = 0
  }

  //删除issue
  function deleteIssue() {
    var params = { objNodeId: issueItem.nodeId, teamId: teamId }
    httpCommon.team_501_trash_obj_node(params)
      .then((res) => {
        if (res.resultCode === 200) {
          globalUtil.success("删除成功");
          queryClient.invalidateQueries(issue107)
          setVisibleTip(false)
          setIssueItem({})
        }
      });
  }

  // issue创建完成后，页面刷新
  function onPostIssueCreated(issueNodeId) {
    setIssueSearchParams({issueNodeId, navId: null}); // 跳转至新建的路由，并清除navId（避免重复新建工单）
    queryClient.removeQueries(issue_512_get_issue_total_query(teamId, issueListNodeId).queryKey) // 创建工单后，刷新统计列表数据
  }

  // 关闭Drawer
  function handleOnClose(isNotChangeFlg = true) {
    // 判断是否需要有更改 DONE 2023-03-29
    if (isNotChangeFlg) {
      setCreateIssueModalVisibleFlg(false); // 表单没有改动，直接关闭表单
      setIssueSearchParams({navId: null}); // 清除navId（避免重复新建工单）
    } else {
      Modal.confirm({
        title: "提示",
        icon: <ExclamationCircleOutlined />,
        content: "正在编辑工单，是否确定放弃编辑?",
        okText: "退出",
        cancelText: "取消",
        width:350,
        onOk: () => {
          setCreateIssueModalVisibleFlg(false); //表单有改动，确认关闭弹窗
          setIssueSearchParams({navId: null}); // 清除navId（避免重复新建工单）
        },
        onCancel: () => {
          console.log("Cancel");
        },
      });
    }
  }

  const content = (
    <div >
      <Radio.Group onChange={(e) => setOrderBy(e.target.value)} value={orderBy}>
        <Space direction="vertical">
          <Radio value={eConsoleNodeId.Nid_11118_Issue_CreateDate}>创建时间</Radio>
          <Radio value={eConsoleNodeId.Nid_11119_Issue_UpdateDate}>修改时间</Radio>
        </Space>
      </Radio.Group>
    </div>
  )

  const onMoreBtnClick = (args) => {
    globalEventBus.emit("onMoreBtnClick", "", { ...args, treeOpType: eTreeOpType.opRightContent });
  }

  const onShowMoreButtonClick = (info) => {
    console.log("onShowMoreButtonClick info", info)
    // contextboardRef.current.showContextBoard(info.event.nativeEvent || info.event, info.node, info.node.nodeId, info.needCreateFlg)
  }
  
  // 新建工单
  const handleCreateWorkOrder = () => {
    // tmsbug-4655:工单模块，未登录时的UI部分隐藏，新建工单和评论时需弹出登录对话框
    if(!validateAuthActions(authActions)) return;
    setCreateIssueModalVisibleFlg(true)
  }

  return <Layout>
    {/* 可拖拽 */}
    <ResizableBox
      width={width || 300} // 接口返回的width为null
      className="custom-box-right"
      height={Infinity}
      handle={handle}
      handleSize={[8, 8]}
      resizeHandles={['e']}
      axis="x"
      minConstraints={[300, Infinity]} //最小宽度
      maxConstraints={[650, Infinity]}
      onResize={onResize}
      onResizeStop={onResizeStop}
      style={{ height: "100%" }}
    >
      <Sider className="issue-list-sider" width={"100%"}>
        <div className="issue-list-sider-search">
          <div style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
            {!!countType || criteriaList?.length > 0 || keywords ?
              <a style={{ paddingLeft: 15, fontSize: 12, color: "#666" }} title={pageNo > 1 ? '定位至#1页' : ''} onClick={()=>pageNo > 1 ? gotoPageNo(1) : null}>结果({totalCnt}条)</a>
              : <a style={{ paddingLeft: 15, fontSize: 12, color: "#666" }} title={pageNo > 1 ? '定位至#1页' : ''} onClick={()=>pageNo > 1 ? gotoPageNo(1) : null}>全部({totalCnt}条)</a>
            }
            <Button
              className={refreshingFlg && 'refresh-icon'}
              style={{ position: 'relative', color: '#999' }}
              type="link"
              icon={<span className="refresh-position fontsize-14 iconfont shuaxin1" />}
              onClick={() => {
                setRefreshingFlg(true);
                refreshIssueList()
              }}
            />
          </div>
          <Space>
            {/* 排序功能 */}
            <Popover
              content={content} title={<div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                <div>排序方式</div>
                <div>
                  <a style={{ color: ascendingFlg ? "#3279fe" : "#999999", }} onClick={() => setAscendingFlg(true)} >
                    <span className="iconfont icarrowleft-copy fontsize-14" />
                  </a>
                  <a style={{ color: !ascendingFlg ? "#3279fe" : "#999999", }} onClick={() => setAscendingFlg(false)}>
                    <span className="iconfont icarrowleft-copy fontsize-14" style={{ transform: "rotate(180deg)", display: "inline-block" }} />
                  </a>
                  {/* <Button title="正序" type="text" icon={<span className="iconfont icarrowleft-copy fontsize-14" style={{ color: ascendingFlg ? "#3279fe" : "#999999", }} />} onClick={() => setAscendingFlg(true)} />
                  <Button title="倒序" type="text" icon={<span className="iconfont icarrowleft-copy fontsize-14" style={{ color: !ascendingFlg ? "#3279fe" : "#999999", transform: "rotate(180deg)" }} />} onClick={() => setAscendingFlg(false)} /> */}
                </div>
              </div>} trigger="click" placement="bottom">
              <Button title="排序" type="text"
                icon={<span className="iconfont paixu1 fontsize-16" style={{ color: "#999999" }} />} />
            </Popover>

            {/* 如果有queryObjId，则不显示新建工单icon */}
            {
            // objInfo?.objId ? "" :
              <Space>
                {totalCnt ? <div className="fontsize-12" style={{ color: "#666" }}>#{pageNo}页</div> : ""}
                
                <Button className="defaultBtn" type="primary" onClick={() => handleCreateWorkOrder()}>+ 工单</Button>
              </Space>
            }
          </Space>
        </div>
        {issueList && issueList?.length > 0 ?
          <>
            {/*中间列，issue短列表*/}
            <List dataSource={issueList}
              className="issue-list"
              renderItem={(_issue, index) => {
                return (
                  <div
                    className={issueNodeId == _issue.nodeId ? "issue-list-item  tms-item-checked" : "issue-list-item"}
                    onClick={() => {
                      setIssueSearchParams({issueNodeId: _issue.nodeId})
                    }}
                    // 右击事件
                    onContextMenu={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onShowMoreButtonClick && onShowMoreButtonClick({
                        event: e,
                        node: _issue,
                        needCreateFlg: false,
                      })
                    }}
                  >
                    <div className="issue-list-item-top">
                      <div style={{ display: "flex", alignItems: "center" }}>
                        {formatSvg(getIconValueByIdType(selectionList, iconSelectionLid, _issue[eConsoleNodeId.Nid_11109_Issue_Type]))}
                        <a className="issue-list-item-num">{_issue.prefix}</a>
                      </div>
                      <Button type="link"
                        icon={<EllipsisOutlined />}
                        className="tree-dir-color tree-dir-more-btn"
                        onClick={(e) => {
                          console.log("_issue", _issue)
                          e.preventDefault();
                          e.stopPropagation();
                          onShowMoreButtonClick && onShowMoreButtonClick({
                            event: e,
                            node: _issue,
                            needCreateFlg: false // 标题右击菜单需要创建操作
                          })
                        }} />
                      {_issue.favorite ?
                        <span className="iconfont shoucang1 fontsize-12" style={{ color: "#fcd53f", marginLeft: 10 }} /> : ""
                      }
                    </div>
                    <div className="issue-list-item-des">{_issue.title}</div>
                  </div>
                )
              }} />
            {/* 短列表 页码配置*/}
            <Pagination
              className={"issue-list-sider-pagination"}
              pageSize={pageSize}
              current={pageNo}
              size="small"
              showSizeChanger={false}
              total={totalCnt}
              onChange={gotoPageNo}
            />
            {/* TODO: 没有右击菜单 */}
            {/* <ContextBoard
              ref={contextboardRef}
              teamId={teamId}
              onMoreBtnClick={onMoreBtnClick}
              // onCreateBtnClick={onCreateBtnClick} 
              id={"issue-context-menu"}
              treeOpType={eTreeOpType.opLeftSider}
            /> */}
            {/* 删除提示弹窗 */}
            <Modal
              className="tms-modal"
              title={
                <div style={{ paddingLeft: 10 }}>
                  <span><ExclamationCircleOutlined style={{ color: '#FACD91', marginRight: 15 }} />提示</span>
                </div>
              }
              centered={true}
              maskClosable={false}
              open={visibleTip}
              onOk={() => deleteIssue()}
              onCancel={() => setVisibleTip(false)}
              okText="确定"
              cancelText="取消"
              width={350}>
              <div style={{ textAlign: "center", color: "#333333" }}>
                <div>是否确定删除{issueItem.fullName}?</div>
              </div>
            </Modal>
          </>
          :
          // 空白提示栏
          <div className="issue-list blank-page">
            <div className="blank-page-title">这里是空的</div>
            <div className="blank-page-des fontsize-14 flexCenter">
              <span style={{ paddingRight: 5 }}>你可以</span>
              <a onClick={() => setCreateIssueModalVisibleFlg(true)}>新建工单</a>
            </div>
          </div>
        }
      </Sider>
    </ResizableBox>
    <Layout className="issue-detail">
      <Outlet context={{ teamId, issueNodeId, selectionList, /* userList, */ gotoPreviousIssue, gotoNextIssue, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg }} />
      {/* 新建issue */}
      {createIssueModalVisibleFlg &&
        <CreateIssueModal
          teamId={teamId}
          selectionList={selectionList}
          spaceUserList={spaceUserList}
          subclassAttrList={subclassAttrList}
          createIssueModalVisibleFlg={createIssueModalVisibleFlg}
          setCreateIssueModalVisibleFlg={setCreateIssueModalVisibleFlg}
          onPostIssueCreated={onPostIssueCreated}
          onClose={handleOnClose}
          navId={navId}
          navQuery={navQuery}
        />
      }
    </Layout>
  </Layout >
}
