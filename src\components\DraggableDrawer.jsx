import React, { useRef, useState } from 'react';
import { Drawer } from 'antd';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';

function convert2Px(val, fallbackPx = "50%") {
    if(val == undefined)
        val = fallbackPx;

    // 处理百分比值，如 "50%"
    if (typeof val === 'string' && val.endsWith('%')) {
        const percent = parseFloat(val) / 100;
        return Math.round(window.innerWidth * percent);
    }
    // 处理像素值，如 "600px" 或 600
    if (typeof val === 'string' && val.toLowerCase().includes('px')) {
        return parseInt(val);
    }
    // 处理纯数字字符串，如果能转换成数值且介于0到1之间，表示是比例，否则为像素
    if (typeof val === 'string' && !isNaN(parseFloat(val))) {
        const numVal = parseFloat(val);
        if (numVal >= 0 && numVal <= 1) {
            // 0到1之间，表示比例
            return Math.round(window.innerWidth * numVal);
        } else {
            // 大于1，表示像素
            return parseInt(val);
        }
    }
    if (typeof val === 'number') {
        return val;
    }
    // 处理 fallback 值
    if (typeof fallbackPx === 'string' && fallbackPx.endsWith('%')) {
        const percent = parseFloat(fallbackPx) / 100;
        return Math.round(window.innerWidth * percent);
    }
    if (typeof fallbackPx === 'string' && fallbackPx.toLowerCase().includes('px')) {
        return parseInt(fallbackPx);
    }
    return typeof fallbackPx === 'number' ? fallbackPx : 600;
}

function pxToPercent(px) {
    return `${Math.round((px / window.innerWidth) * 100)}%`;
}

/**
 * DraggableDrawer
 * @param {boolean} draggableFlag 是否可拖拽宽度
 * @param {number|string} minWidth 最小宽度（可为百分比如"30%"或像素如"400px"或数字如400）
 * @param {number|string} maxWidth 最大宽度（可为百分比如"95%"或像素如"1000px"或数字如1000）
 * @param {number|string} width 初始宽度（可为百分比如"50%"或像素如"600px"或数字如600）
 * @param {function} onWidthChange 拖拽时回调，参数为像素值和百分比字符串
 * @param {string} fixedMinWidth 固定小宽度（如'50%'）
 * @param {string} fixedMaxWidth 固定大宽度（如'90%'）
 * @param {ReactNode} children Drawer内容
 * @param {object} rest 其他 Drawer props
 */
export default function DraggableDrawer({
                                            draggableFlag = true,
                                            minWidth = "30%", maxWidth = "95%", width = "50%",
                                            onWidthChange, fixedMinWidth, fixedMaxWidth,
                                            title, children, onZoomChange, // 新增：放大状态变化回调
                                            initialZoomed = false, // 新增：初始放大状态
                                            objType, //绘制的”业务对象"类型, 311文档
                                            ...rest
                                        }) {
    // 计算初始宽度
    const initialWidth = convert2Px(width);

    const minPx = convert2Px(minWidth);
    const maxPx = convert2Px(maxWidth);
    const [drawerWidth, setDrawerWidth] = useState(initialWidth);
    const dragging = useRef(false);
    const frameRef = React.useRef();

    // 处理窗口resize时宽度自适应
    React.useEffect(() => {
        if (typeof width === 'string' && width.endsWith('%')) {
            const handleResize = () => {
                setDrawerWidth(convert2Px(width, "50%"));
            };
            window.addEventListener('resize', handleResize);
            return () => window.removeEventListener('resize', handleResize);
        }
    }, [width]);


    // 拖拽时
    const onMouseDown = (e) => {
        if (!draggableFlag) return;
        dragging.current = true;
        const startX = e.clientX;
        const startWidth = drawerWidth;

        const onMouseMove = (moveEvent) => {
            if (!dragging.current) return;
            if (frameRef.current) return;
            frameRef.current = requestAnimationFrame(() => {
                const delta = startX - moveEvent.clientX;
                let newWidth = startWidth + delta;
                if (newWidth < minPx) newWidth = minPx;
                if (newWidth > maxPx) newWidth = maxPx;
                setDrawerWidth(newWidth);
                if (onWidthChange) {
                    onWidthChange(newWidth, pxToPercent(newWidth));
                }
                frameRef.current = null;
            });
        };

        const onMouseUp = () => {
            dragging.current = false;
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
            if (frameRef.current) {
                cancelAnimationFrame(frameRef.current);
                frameRef.current = null;
            }
        };

        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    };

    // 句柄 hover 效果
    const [hover, setHover] = useState(false);


    // 渲染 title
    let drawerTitle = title;

    return (
        <Drawer
            width={draggableFlag ? drawerWidth : initialWidth}
            title={drawerTitle}
            {...rest}
        >
            {draggableFlag && (
                <div
                    style={{
                        position: 'absolute',
                        left: -3, // 溢出Drawer左侧
                        top: 55, // 避开标题栏，从内容区域开始
                        width: 10,
                        height: 'calc(100% - 55px)', // 减去标题栏高度
                        cursor: 'ew-resize',
                        zIndex: 999, // 降低层级，不覆盖标题
                        background: hover ? 'rgba(24,144,255,0.2)' : 'rgba(0,0,0,0.05)',
                        transition: 'background 0.2s',
                        borderRadius: '0 4px 4px 0',
                        userSelect: 'none', // 防止拖拽时选中文字
                        WebkitUserSelect: 'none', // Safari 兼容
                        MozUserSelect: 'none', // Firefox 兼容
                        msUserSelect: 'none', // IE 兼容
                    }}
                    onMouseDown={onMouseDown}
                    onMouseEnter={() => setHover(true)}
                    onMouseLeave={() => setHover(false)}
                />
            )}
            {children}
        </Drawer>
    );
}