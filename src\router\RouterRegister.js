import { generatePath } from "react-router-dom";

export const RouteObject = {};

export const team_route = "/team/:teamId"

/**
 * @description 注册单个路由，使用场景 左侧树点击寻找路由，文章插入快捷链接后寻找单个节点
 * @param {*} nodeType 
 * @param {*} option 类型为string 当前记录的是path,类型为Obj 记录 path 和 search 
 * @param {*} search 
 * @param {*} isteam 是否是team 路由下的
 */
export function registerRouter(nodeType,option,isteam = true){
  if( typeof option == "string"){
    RouteObject[nodeType] = {
      path:isteam?(team_route+"/"+option): option,
      search: ""
    }
  }else if(option instanceof Object){
    RouteObject[nodeType] = {
      path:isteam?(team_route+"/"+option.path): option.path,
      search: option.search
    }
  }
  
}

/**
 * @description 获取单个节点的route
 * @param {*} nodeType 节点类型
 * @returns 
 */
export function getRoutePathByNodeType(nodeType){
  return RouteObject[nodeType]?(RouteObject[nodeType].path+RouteObject[nodeType].search):"";
}

export function hasRoutePathByNodeType(nodeType){
  return !!RouteObject[nodeType];
}

/**
 * @description 生成单个节点完整path
 * @param {number} nodeType 
 * @param {Object} params 
 * @returns 
 */
export function generateRoutePathByNodeType(nodeType,params){
  try {
    let path = RouteObject[nodeType]?.path || "";
    let search = RouteObject[nodeType]?.search || ""; 
    return {
      pathname: generatePath(path, params),
      search: generatePath(search, params)
    }
  } catch (e) {
    return {
      pathname: "",
      search: "",
    }
  }
}

/**
 * @description 获取网站根结点URL
 * @returns 
 */
export function getRootUrl(){
  var _location = window.location 
  return _location.protocol + "//"+ _location.host + (_location.hash?"/#":"")
}

/**
 * @description 生成可复制的完整的外部链接
 * @param {*} nodeType 
 */
export function createLinkUrlByNodeType(nodeType,params){
  let {pathname,search} = generateRoutePathByNodeType(nodeType,params)
  return getRootUrl() + pathname+search
}