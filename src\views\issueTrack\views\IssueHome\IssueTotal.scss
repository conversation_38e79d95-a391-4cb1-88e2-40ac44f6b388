// 侧边悬浮栏
.issue-total {
  background-color: #fff;
  min-width: 100px !important;
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%); // 垂直居中
  .issue-total-list {
      // 工单状态 
      .issue-total-li {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 10px;
        border: 2px solid #f2f2f2;
        border-bottom: none;
      }
      // 最后一个底部边框
      .issue-total-li:nth-of-type(5){
        border-bottom: 2px solid #f2f2f2;
      }
      .issue-total-li:hover span {
        color: #3279fe !important;
      }
  }
  // 新建工单
  .issue-total-btn {
    display: flex;
    // flex: auto;
    width: 100%;
    color: #fff;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    padding: 10px;
    border-radius: 5px;
    background-color: #3279fe;
  }
}