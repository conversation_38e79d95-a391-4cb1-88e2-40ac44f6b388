// 深度定制
.deep-customization {
    .deep-customization-header {
        padding: 50px 0 140px;
        text-align: center;
        color: white;
        background-color: #073b79;
    }
    .deep-customization-content {
        margin: auto;
        padding-bottom: 40px;
        width: 1260px;
        &-li {
            display: flex;
            flex-direction: column;
            margin-bottom: 32px;
            padding: 20px 40px;
            width: 500px;
            box-shadow: 0 4px 16px 0 rgb(77 77 77 / 12%), 0 1px 2px 0 rgb(77 77 77 / 6%);
            border-radius: 5px;
            border: 2px solid #9999994a;
            background-color: white;
            .ability-stack {
                .ability-stack-li {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin-bottom: 20px;
                    width: 120px;
                    .ability-stack-li-icon {
                        border-radius: 50%;
                    }
                    .ability-stack-li-text {
                        margin-top: 6px;
                    }
                }
            }
        }
        &-li:nth-child(3n) {
            margin-left: 0;
        }
    }
}