import React, { useEffect, useState, useRef } from "react";
import {
  Form,
  Input,
  Slider,
  Radio,
  Badge,
  InputNumber,
  Col,
  Row,
  Space,
  Button,
  message,
  Descriptions,
} from "antd";
import { DisconnectOutlined } from '@ant-design/icons';
import "./index.scss";
import pageErrorIcon from "../../assets/images/pageError.jpg"

const { Search } = Input;

export default function PageNonExistent({tips}) {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
      height: '100%',
    }}>
      <div 
      style={{ 
        width: 200,
        height: 200,
        background: `url(${pageErrorIcon}) no-repeat`,
        backgroundSize: '100%'
     }}
      ></div>
      <span className="fontsize-20" style={{ color: '#999' }}>{tips}</span>
    </div>
  );
}
