import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Box
} from "@mui/material";
import "../index.scss";
import {
  team_1301_get_page_part_widget,
} from "../../../api/http_common";
import TopMenuBar from "../../../components/TopMenuBar";
import FooterBar from "../../../components/FooterBar";
import Coop_Part_0_Banner from "./Coop_Part_0_Banner"; // 招商合作-头部广告
import Coop_Part_1_Winwin from "./Coop_Part_1_Winwin"; // 招商合作-携手iTeam共赢未来
import Coop_Part_2_GiveAll from "./Coop_Part_2_GiveAll"; // 招商合作-倾其所有
import Coop_Part_3_Qualification from "./Coop_Part_3_Qualification"; // 招商合作-合作条件
import Coop_Part_4_Footer from "./Coop_Part_4_Footer"; // 招商合作-底部广告
import ContactUsModal from "../../../components/ContactUsModal";
import ApplyCooperationDrawer from "../../../components/ApplyCooperationDrawer"; // 代理商合作
import {partTypeEnum} from 'src/utils/enum';

export default function CooperatePage() {
  const location = useLocation();
  const [partList, setPartList] = useState([]);
  const [contactUsOpen, setContactUsOpen] = useState(false);
  const [applyOpen, setApplyOpen] = useState(false);

  useEffect(() => {
    getPagePartWidget(6666, location.pathname)
  },[])

  const getPagePartWidget = (teamId, menuUrl) => {
    team_1301_get_page_part_widget({teamId, menuUrl}).then(res => {
      if(res.resultCode == 200){
        setPartList([...res.partList])
      }
    });
  }

  // 打开联系我们弹窗
  const openContactUsModal = () => setContactUsOpen(true)

  // 关闭联系我们弹窗
  const cancelContactUsModal = () => setContactUsOpen(false)

  // 打开招商合作弹窗
  const openApplyDrawer = () => setApplyOpen(true)

  // 关闭招商合作弹窗
  const cancelApplyDrawer = () => setApplyOpen(false)

  // 招商合作-内容渲染
  const getCooperateContent = (type, data, index) => {
    switch (type) {
      case partTypeEnum.part_type_51_coop_partner_banner :
        return <Coop_Part_0_Banner key={index} data={data} openContactUsModal={openContactUsModal} openApplyDrawer={openApplyDrawer}/>

      case partTypeEnum.part_type_52_coop_partner_winwin :
        return <Coop_Part_1_Winwin key={index} data={data}/>

      case partTypeEnum.part_type_53_coop_partner_devotion :
        return <Coop_Part_2_GiveAll key={index} data={data}/>

      case partTypeEnum.part_type_54_coop_partner_cooperation :
        return <Coop_Part_3_Qualification key={index} data={data}/>

      case partTypeEnum.part_type_55_coop_partner_footer :
        return <Coop_Part_4_Footer key={index} data={data} openContactUsModal={openContactUsModal} openApplyDrawer={openApplyDrawer}/>
    
      default:
        return <></>
    }
  }

  return (
    <div className="cooperate">
      <Box sx={{ height: 76 }} />
      <Box sx={{ width: "100%", padding: "0 100px" }}>
        <Box className="cooperate-TopMenuBar-box">
          <TopMenuBar />
        </Box>
      </Box>

      {partList.map((item, index) => {
        return getCooperateContent(item.partType, item, index)
      })}

      <FooterBar />
      <ContactUsModal open={contactUsOpen} onCancel={cancelContactUsModal}/>
      <ApplyCooperationDrawer open={applyOpen} onCancel={cancelApplyDrawer}/>
    </div>
  );
}
