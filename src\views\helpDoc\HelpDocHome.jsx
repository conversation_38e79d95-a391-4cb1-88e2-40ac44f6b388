import {<PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography} from "@mui/material";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Input, Layout } from 'antd';
import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import {
  team_1301_get_page_part_widget
} from "../../api/http_common";
import { doc_004_get_tutorial_info_query } from "../../api/query/query";
import { TMSRecord } from "../../components/FooterBar";
import TopMenuBar from "../../components/TopMenuBar";
import { getDocNodeId } from "./fileTypeUtils";
import "./HelpDocHome.scss";
import DocSearchDrawer from "./DocSearchDrawer";
import CommonTreeSider from "@/components/CommonTreeSider/CommonTreeSider";
import { useImmer } from "use-immer";
import { eNodeTypeId } from "@/utils/TsbConfig";
import { Outlet } from 'react-router-dom'
import { getMatchRoutes } from "@/router/usePathPattern";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../utils/commonUtils";
import { formatVariantColor } from "../../utils/muiThemeUtils";
import {partTypeEnum} from 'src/utils/enum';

const { Content, } = Layout;

export default function HelpCenter() {
  const navigate = useNavigate();
  const location = useLocation();
  const { nodeId: objNodeId } = useParams();
  const queryClient = useQueryClient();
  const docTeamId = process.env.REACT_APP_WORKORDER_TEAMID; // 文档接口teamId
  const docNodeId = getDocNodeId(location); // 文档接口nodeId
  const [searchDocOpen, setSearchDocOpen] = useState(false);
  const [docTags, setDocTags] = useState([]); // 标签数据
  const [searchValue, setSearchValue] = useState(""); // 输入框的值
  const [currentRouteInfo, setCurrentRouteInfo]= useState({});

  // 管理数据，便于修改数据
  const [rootNode, setRootNode] = useImmer(null);
  const [initLoading, setInitLoading] = useState(true);

  const { data: { rootNodeUi, treeKeyList } = { rootNodeUi: null, treeKeyList: [] }, isLoading: isLoadingObjTree } //, refetch
      = useQuery({...doc_004_get_tutorial_info_query(process.env.REACT_APP_WORKORDER_TEAMID, docNodeId, [], true)})

  useEffect(() => {
    if (!isLoadingObjTree) {
      setRootNode(rootNodeUi);
      setInitLoading(false);
    }
  }, [rootNodeUi, isLoadingObjTree]);

  useEffect(() => {
    let routes = getMatchRoutes(location) || [];
    let route = routes.reverse().find(item => !!item.route.meta)?.route;
    console.log(route);
    setCurrentRouteInfo({...route})
    loadData(route.path)
    window.onpopstate = () => {
      navigate('/')
    }
  }, [])

  const loadData = async (pathname) => {
    await getPagePartWidget(docTeamId, `/${pathname}`)
  }

  const getPagePartWidget = async (teamId, menuUrl) => {
    let res = await team_1301_get_page_part_widget({ teamId, menuUrl })
    if (res.resultCode == 200) {
      if (isArrayUtils(res.partList.filter(el => el.partType === partTypeEnum.part_type_71_help_doc_tag)[0]?.widgetList)) {
        let formatData = formatTreeData([], res.partList.filter(el => el.partType === partTypeEnum.part_type_71_help_doc_tag)[0]?.widgetList)
        setDocTags([...formatData])
      }
    }
  }

  const setSearchInputValue = (value) => {
    setSearchValue(value)
  }

  // 点击事件
  const onMenuItemClick = (selectedKeys, info) => {
    docTreeSelect({ objNodeId: info.node.key });
  }

  // 文档树节点选择
  const docTreeSelect = ({ objNodeId }) => {
    if(objNodeId) {
      navigate(`${objNodeId}`)
    }
  }

  // 切换选中节点
  const docSelectdChange = (node) => {
    navigate(`${node.nodeId}`)
  }

  // 更新数据
  const refetchTreeData = () => {
    queryClient.invalidateQueries(doc_004_get_tutorial_info_query(process.env.REACT_APP_WORKORDER_TEAMID, docNodeId, []))
  }

  // TODO: 中树滚动存在问题
  return (
    <>
      <Layout className="helpCenter">
        <Box sx={{ height: 90 }} />
        <Box sx={{ width: "100%", padding: "0 100px" }}>
          <Box className="helpCenter-TopMenuBar-box">
            <TopMenuBar />
          </Box>
        </Box>

        {/* 搜索和标签-start */}
        <Box className="helpCenter-top">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="body2"
              sx={{
                mr: '20px',
                minWidth: '74px',
                fontSize: '18px',
              }}>{currentRouteInfo?.meta?.title}</Typography>
            <Stack
              sx={{ flexWrap: "wrap" }}
              direction={{ xs: "column", sm: "row" }}
              spacing={2}
            >
              {/* TODO:点击事件待处理 */}
              {(filterWidgetList(docTags, 6)[0]?.children || []).map((item, index) => (
                <Button
                  key={item.widgetId}
                  className="helpCenter-chip fontsize-12"
                  size="small"
                  variant={formatVariantColor(item.style)?.variant}
                  color={formatVariantColor(item.style)?.color}
                  onClick={() => navigate(item.linkUrl)}
                >{item.value}</Button>
              ))}
            </Stack>
          </div>
          <Box className="helpCenter-search">
            <Input className="fontsize-12" style={{ width: 200, height: 30, borderRadius: 5 }} placeholder="搜文档" size="small" onClick={() => setSearchDocOpen(true)} />
            <span className="iconfont sousuo fontsize-16" style={{ position: 'absolute', right: 10, color: '#999' }} onClick={() => setSearchDocOpen(true)}></span>
          </Box>
        </Box>
        {/* 搜索和标签-end */}

        <Layout hasSider style={{ flex: "auto" }} className="helpCenter-sider ant-layout-sider-light" >
          <CommonTreeSider
            rootNode={rootNode}
            setRootNode={setRootNode}
            treeKeyList={treeKeyList}
            refetchTreeData={refetchTreeData}
            nodeType={eNodeTypeId.nt_31201_objtype_tutorial_doc}
            objNodeId={objNodeId}
            onMenuItemClick={onMenuItemClick}
            onDoubleTreeNodeClick={onMenuItemClick}
            loading={isLoadingObjTree || initLoading}
            setLoading={setInitLoading}
            setSearchRouteParams={docTreeSelect}

          />
          <Content className="helpCenter-content">
            <Outlet context={{ currentRouteInfo }}/>
          </Content>
        </Layout>

        {/* 搜索抽屉 */}
        <DocSearchDrawer
          docNodeId={docNodeId}
          searchValue={searchValue}
          setSearchInputValue={setSearchInputValue}
          open={searchDocOpen}
          onClose={() => setSearchDocOpen(false)}
          docSelectdChange={docSelectdChange}
        />

      </Layout>


      <div className="helpCenter-TMSRecord">
        <TMSRecord />
      </div>
    </>
  );
}

