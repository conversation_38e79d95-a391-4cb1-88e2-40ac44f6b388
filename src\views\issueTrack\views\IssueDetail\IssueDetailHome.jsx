import React from "react";
import * as http from "@/api/http";
import IssueDetail from "./IssueDetail";
import { useOutletContext } from "react-router-dom";
import TEmpty from "@/components/TEmpty";

/**
 * issue 短列表详情
 */
export default function IssueDetailHome() {
    const { issueNodeId, selectionList, /* userList,  */gotoPreviousIssue, gotoNextIssue, previousIssueLinkDisabledFlg, nextIssueLinkDisabledFlg, viewMode } = useOutletContext();
    console.log("issueNodeId", issueNodeId);
    return <>
        {issueNodeId ? <IssueDetail
            viewMode={viewMode}
            issueNodeId={issueNodeId}
            selectionList={selectionList}
            // userList={userList}
            gotoPreviousIssue={gotoPreviousIssue}
            gotoNextIssue={gotoNextIssue}
            previousIssueLinkDisabledFlg={previousIssueLinkDisabledFlg}
            nextIssueLinkDisabledFlg={nextIssueLinkDisabledFlg}
        /> : <TEmpty/>
        }
    </>
}