/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2023-04-03 16:56:51
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2025-07-15 18:06:21
 * @Description: 页面截图组件
 */

import { DeleteOutlined, EditOutlined, EyeOutlined, PlusOutlined } from '@ant-design/icons';
import * as http from '@/api/http';
import { globalUtil } from "@/utils/globalUtil";
import { Button, Image, Upload } from "antd";
import { useEffect, useState } from "react";
import CanvasImageModal from "../CanvasImageModal";
import { eNodeTypeId, eNodeType } from "@/utils/TsbConfig"
import { eFileObjId } from "@/utils/enum"
import "./AddOrderUpload.scss";

export default function AddOrderUpload({ visible, fileObj, uploadImagePush, attrNodeId }) {

  const teamId = process.env.REACT_APP_WORKORDER_TEAMID;

  const [fileList, setFileList] = useState([]);
  const [attachmentImgPreviewIdx, setAttachmentImgPreviewIdx] = useState(0);   //预览index位置
  const [previewGroupVisible, setPreviewGroupVisible] = useState(false); // 图片预览
  const [canvasObjVisible, setCanvasObjVisible] = useState(false);
  const [taggingUrlObj, setTaggingUrlObj] = useState({});

  useEffect(() => {
    let _fileList = []
    if (!!fileObj) {
      _fileList.push(fileObj)
    }
    setFileList(_fileList)
  }, [visible])

  const getBase64 = (file) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });

  // 点击预览
  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
  };

  const handleChange = (info) => {
    let _file = {}
    if (info.file.status !== 'uploading') {
    }
    if (info.file.status === 'done') {
      if (info.file.response.resultCode == 200) {
        globalUtil.success(`${info.file.name} 附件上传成功！`);
        _file = {
          objId: info.file.response.id,
          objName: info.file.name,
          objType: '23',
          seqNo: fileList.length + 1,
          url: info.file.response.link,
        }
        const _fileList = [...fileList, _file];
        uploadImagePush({
          nodeId: attrNodeId,
          fileList: [..._fileList]
        })
        setFileList(_fileList)
      } else {
        globalUtil.error(`${info.file.name} 附件上传失败！`);
      }
    } else if (info.file.status === 'error') {
      globalUtil.error(`${info.file.name} 附件上传失败！`);
    }
  }

  // 上传props
  const uploadProps = {
    multiple: true,
    action: `${process.env.REACT_APP_BASE_URL}/tms_team/api/upload_file`,
    listType: "picture-card",
    showUploadList: false,
    data: {
      teamId: teamId,
      moduleName: eNodeType[eNodeTypeId.nt_31704_objtype_issue_item].name,
      nodeId: eFileObjId,
      objType: eNodeTypeId.nt_31704_objtype_issue_item
    }
  }

  // 删除已上传的图片
  const deleteUploadImg = (url) => {
    let _fileList = fileList.filter(el => el.objId != url.objId)
    setFileList([..._fileList])
    uploadImagePush({
      nodeId: attrNodeId,
      fileList: [..._fileList]
    })
  }

  // 预览
  const clickPreview = (obj) => {
    let index = fileList?.findIndex(item => item.objId == obj.objId)
    setAttachmentImgPreviewIdx(index);
    setPreviewGroupVisible(true)
  }

  // 标注对象
  const taggingImage = (imageObj) => {
    setTaggingUrlObj(imageObj)
    setCanvasObjVisible(true)
  }

  // 参数
  const getCanvasProps = (urlObj) => {
    uploadBese64Img(urlObj)
  }

  const base64toFile = (base64Data) => {
    //去掉base64的头部信息，并转换为byte
    let split = base64Data.split(',');
    let bytes = window.atob(split[1]);
    //获取文件类型
    let fileType = split[0].match(/:(.*?);/)[1];
    //处理异常,将ascii码小于0的转换为大于0
    let ab = new ArrayBuffer(bytes.length);
    let ia = new Uint8Array(ab);
    for (let i = 0; i < bytes.length; i++) {
      ia[i] = bytes.charCodeAt(i);
    }
    console.log('fileType', fileType);
    return new Blob([ab], { type: fileType });
  }

  // 标注的图片上传
  const uploadBese64Img = (fileObj) => {
    let formData = new FormData()
    let file_ = base64toFile(fileObj.url)
    let _fileList = []
    formData.append('file', file_, fileObj.objName);
    formData.append('teamId', teamId);
    formData.append('moduleName', eNodeType[eNodeTypeId.nt_31704_objtype_issue_item].name);
    formData.append('nodeId', eFileObjId);
    formData.append('objType', eNodeTypeId.nt_31704_objtype_issue_item);
    http.team_506_upload_file(formData).then((res) => {
      if (res.resultCode === 200) {
        _fileList = fileList.map((obj, index) => {
          if (obj.objId === fileObj.objId) {
            return {
              objId: res.id,
              objName: obj.name,
              objType: obj.objType,
              seqNo: obj.seqNo,
              url: res.link,
            }
          } else {
            return {
              ...obj
            }
          }
        })
        setFileList([..._fileList])
        uploadImagePush({
          nodeId: attrNodeId,
          fileList: [..._fileList]
        })
      } else {
      }
    })
  }

  return (
    <div style={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', marginTop: 20 }}>
      {fileList.map((item, index) => {
        return <div className="previewBox" key={item.objId}>
          <Image src={item.url} />
          <div className="previewBox-open">
            <Button title="标注" size="small" type="link" icon={<EditOutlined />} onClick={() => taggingImage(item)} />
            <Button title="预览" size="small" type="link" icon={<EyeOutlined />} onClick={() => clickPreview(item)} />
            <Button title="删除" size="small" type="link" icon={<DeleteOutlined />} onClick={() => deleteUploadImg(item)} />
          </div>
        </div>
      })}
      {fileList.length < 5 && <Upload
        className="workOrderUpload"
        {...uploadProps}
        onPreview={handlePreview}
        onChange={handleChange}
      >
        {<PlusOutlined />}
      </Upload>}
      <PreviewGroupModal
        previewImgGroup={fileList}
        attachmentImgPreviewIdx={attachmentImgPreviewIdx}
        previewGroupVisible={previewGroupVisible}
        setPreviewGroupVisible={setPreviewGroupVisible}
      />
      <CanvasImageModal visible={canvasObjVisible} urlObj={taggingUrlObj} onCancel={() => setCanvasObjVisible(false)} getCanvasProps={getCanvasProps} />
    </div>
  )
}

// 多图片预览
function PreviewGroupModal({ previewImgGroup, attachmentImgPreviewIdx, previewGroupVisible, setPreviewGroupVisible }) {

  return <Image.PreviewGroup
    preview={{
      visible: previewGroupVisible,
      current: attachmentImgPreviewIdx,
      onVisibleChange: (value) => {
        setPreviewGroupVisible(value);
      }
    }}>
    {previewImgGroup && previewImgGroup.length > 0 ?
      previewImgGroup?.map((item, index) => {
        return <Image
          rootClassName="previewImage-padding"
          style={{ display: 'none' }}
          src={item.url}
          key={item.objId}
        />
      })
      :
      <></>
    }
  </Image.PreviewGroup>
}