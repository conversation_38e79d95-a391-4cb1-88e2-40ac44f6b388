import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Button,
} from "@mui/material";
import "./index.scss";
import { skipOS } from "../../../../utils/commonUtils";
import { useAuthContext } from "@/context/AppAuthContextProvider";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../../utils/commonUtils";
import { formatVariantColor } from "../../../../utils/muiThemeUtils";

export default function HowToGetStart(props) {
  const { data } = props;
  const authActions = useAuthContext();
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([],data.widgetList);
      setModuleData([...formatData]);
    }
  },[data?.widgetList])

  return (
    <>
      {/* 如何开启使用start */}
      {moduleData.length > 0 && <Box className="start-steps-section"
      >
        <Box className="start-steps-section__main" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
          <Box sx={{ pt: "50px" }} className="start-steps-section__inside">
            <Box className="step-container">
              {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography sx={{ color: "#fff", textAlign: "center" }} variant="h4">{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
              <Box className="start-steps">
                {filterWidgetList(moduleData, 6).map((item, index) => (
                  <Box className="start-steps__col" key={item.widgetId}>
                    <Box className="start-step">
                      <Box className="start-step__number">{index+1}</Box>
                      <Box className="start-step__title">{filterWidgetList((item?.children || []), 1)[0]?.value}</Box>
                      <Box className="start-step__text">
                        {filterWidgetList((item?.children || []), 2)[0]?.value}
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
        </Box>
        {filterWidgetList(moduleData, 4).length > 0 && <Box className="start-steps-section__footer">
          <Button
            className="btn-2ac465"
            sx={{ width: 160, borderRadius: 20 }}
            variant={formatVariantColor(filterWidgetList(moduleData, 4)[0]?.style)?.variant}
            color={formatVariantColor(filterWidgetList(moduleData, 4)[0]?.style)?.color}
            onClick={() => skipOS(authActions.isAuthenticated)}
          >
            {filterWidgetList(moduleData, 4)[0]?.value}
          </Button>
        </Box>}
      </Box>}
      {/* 如何开启使用end */}
    </>
  );
}
