import * as httpBase from "../utils/httpBase";
import api_name from "./api_name";

const { api_tms_sip, api_tms_team } = httpBase;

// team-107 get_query_detail 获取搜索详情信息
// https://confluence.ficent.com/pages/viewpage.action?pageId=78369599
export const team_107_get_query_detail= (data) => httpBase.post(api_tms_sip + api_name.team_107_get_query_detail, data);
// team-113 favorite_obj 收藏
// https://confluence.ficent.com/pages/viewpage.action?pageId=78363568
// export const team_113_favorite_obj= (data) => httpBase.post(api_tms_team + api_name.team_113_favorite_obj, data)
// team-114 cancel_favorite_obj 取消收藏
// https://confluence.ficent.com/pages/viewpage.action?pageId=78364032
// export const team_114_cancel_favorite_obj= (data) => httpBase.post(api_tms_team + api_name.team_114_cancel_favorite_obj, data)
// team-511 del_obj_rel 删除引用资源
// https://confluence.ficent.com/pages/viewpage.action?pageId=78373446
// export const team_511_del_obj_rel= (data) => httpBase.get(api_tms_team + api_name.team_511_del_obj_rel, data)
// team-512 del_attachment 删除附件
// https://confluence.ficent.com/pages/viewpage.action?pageId=78374273
export const team_512_del_attachment= (data) => httpBase.post(api_tms_team + api_name.team_512_del_attachment, data) 
// team-544 get_table_field_list 获取选择字段
// https://confluence.ficent.com/pages/viewpage.action?pageId=98404377
export const team_544_get_table_field_list= (data) => httpBase.post(api_tms_team + api_name.team_544_get_table_field_list, data) 








