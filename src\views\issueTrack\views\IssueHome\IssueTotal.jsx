/* eslint-disable jsx-a11y/anchor-is-valid */
/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2023-04-06 14:49:08
 * @LastEditors: Walt <EMAIL>
 * @LastEditTime: 2023-06-02 10:32:23
 * @Description: 工单总数
 */
import { Button } from "antd";
import { useRef, useState } from "react";
import Draggable from 'react-draggable';
import {eCountType} from "@/utils/enum";
import { useAuthContext } from "@/context/AppAuthContextProvider";
import { validateAuthActions } from "@/utils/commonUtils";
import "./IssueTotal.scss";

export default function IssueTotal({issueTotal, setCreateIssueModalVisibleFlg, countType, setCountType}) {

  const [isDragging, setIsDragging] = useState(false); // 是否处理拖拽中，防止拖拽触发点击事件
  const [hasMoved, setHasMoved] = useState(false); // 是否已经移动过
  const [dragStartPos, setDragStartPos] = useState({ x: 0, y: 0 }); // 拖拽开始位置

  console.log("isDragging", isDragging);

  const authActions = useAuthContext();

  const draggleRef = useRef(null);

  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });

  const onDrag = (_event, uiData) => {
    setIsDragging(true);
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      // left: 0,
      // right: 0,
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  // 停止拖拽
  const onStop = () => {
    setTimeout(() => { setIsDragging(false); }, )
  }

  const issueTotalList = [
   {
    key: eCountType.eTotal,
    value: "total",
    label: "全部",
    className: ""
   },
   {
    key: eCountType.eTotalCreate,
    value: "totalCreate",
    label: "我创建的",
    className: ""
   },
   {
    key: eCountType.ePrincipal,
    value: "totalMine",
    label: "待我处理",
    className: "color-yellow"
   },
   {
    key: eCountType.eProcessing,
    value: "totalProcessing",
    label: "进行中",
    className: ""
   },
   {
    key: eCountType.eWatch,
    value: "totalWatch",
    label: "我关注的",
    className: ""
   },
  ]

  // 点击事件
  const handleOnClick = (countType) => {
    if (isDragging) return;
    setCountType(countType);
  }

  // 新建工单
  const handleCreateWorkOrder = () => {
    if (isDragging) return;
    // tmsbug-4655:工单模块，未登录时的UI部分隐藏，新建工单和评论时需弹出登录对话框
    if(!validateAuthActions(authActions)) return;
    setCreateIssueModalVisibleFlg(true)
  }

  return (
    <>
      <Draggable
        handle='.issue-total'
        // disabled={disabled}
        bounds={bounds}
        onDrag={(event, uiData) => onDrag(event, uiData)}
        onStop={onStop}
      >
        <div className="issue-total" ref={draggleRef}>
          <div className="issue-total-list">
            {
              issueTotalList.map((data, index) => (
                <a key={data.key} className="issue-total-li" onClick={()=>handleOnClick(data.key)}>
                  <span className={`fontsize-14 fontweight-bold fontcolor-normal ${countType == data.key ? "color-yellow": ""}`}>{issueTotal[data.value] || 0}</span>
                  <span className={`fontsize-14 fontweight-bold fontcolor-normal ${countType == data.key ? "color-yellow": ""}`}>{data.label}</span>
                </a>
              ))
            }
        </div>
        <Button className="issue-total-btn" onClick={()=>handleCreateWorkOrder()}>+工单</Button>
      </div>
    </Draggable>
    </>
  )
}
