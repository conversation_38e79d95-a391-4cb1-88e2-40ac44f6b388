.tree-dir-color{
  color: #aaaaaa;
}

.tree-dir-between{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  .ant-space {
    .ant-space-item {
      line-height: 0.8;
    }
  }
}

.tree-dir-label{
  text-overflow: ellipsis;
  display: -webkit-box;
  overflow: hidden;
  word-break: break-all;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.team-sider{
  .ant-tree-treenode{
    padding: 0;
  }
}

.tree-dir-title-delete{
  text-decoration: line-through;
}

.tree-dir-tag-circle{
  display: inline-block;
  margin-right: 2px;
  height: 8px;
  width: 8px;
  border-radius: 50%;
}

.tree-dir-more-action-white{
  display: inline-flex;
  align-items: center;
  min-width: 32px;
  max-width: 32px;
  height: 24px; // TODO:为什么会影响到treeNode的高度
  line-height: 24px;
  box-sizing: border-box;
  justify-content: center;
  // min-width: 64px;
  .ant-tree-treenode.ant-tree-treenode-selected & .tree-dir-color{
    // 选中的更多按钮颜色
    color: #666;
  }
  .tree-dir-more-btn{
    visibility: hidden;
  }
  .tree-dir-more-btn-display{
    display: inline-block;;
  }
}

.ant-tree-treenode:hover .tree-dir-more-action-white{
  .tree-dir-more-btn{
    visibility: visible;
  }
}

/* tree context board */
.tree-contextboard {
  // max-height: 300px;
  min-width: 135px;
  // overflow-y: auto;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0 9px 14px 0 #0000002b;
  position: absolute;
  z-index: 50001 !important;

  .ant-menu {
    border-radius: 6px;
  }
  .ant-menu>.ant-menu-item,.ant-menu>.ant-menu-submenu>.ant-menu-submenu-title{
    height: 25px;
    line-height: 25px;
    font-size: 13px;
    margin-bottom: 0;
  }

  .ant-menu>.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item{
    height: 25px;
    line-height: 25px;
    font-size: 13px;
    margin-bottom: 0px
  }
}
