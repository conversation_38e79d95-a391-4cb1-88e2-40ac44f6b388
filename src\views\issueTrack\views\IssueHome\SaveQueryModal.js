import React, { useEffect, useState } from "react";
import { Input, Modal, Radio, Spin } from "antd";
import * as http from "@/api/http";

// 保存为 弹窗
export default function SaveQueryModal({ teamId, objParentId, querySite, setQueryName, saveQueryModalVisibleFlg, setSaveQueryModalVisibleFlg, saveQuery, querySiteChange, }) {
    const [parentName, setParentName] = useState("")
    const [isloading, setloading] = useState(true)

    useEffect(() => {
        if (saveQueryModalVisibleFlg && objParentId) {
            let params = {
                teamId: teamId,
                objNodeId: objParentId,
            };
            http.team_530_get_obj_node_info(params).then((res) => {
                if (res.resultCode === 200) {
                    setParentName(res.name)
                    setloading(false)
                }
            })
        }
    }, [saveQueryModalVisibleFlg])


    return <Modal className="tms-modal"
        centered //居中显示
        width={500}
        title={"保存搜索"}
        open={saveQueryModalVisibleFlg}
        onCancel={() => setSaveQueryModalVisibleFlg(false)}
        onOk={() => saveQuery()}
        destroyOnClose={true}
    >
        <Spin spinning={isloading}>
            <div className="flexLeft">
                <div style={{ width: 80 }}>搜索名称</div>
                <Input style={{ width: 350 }} onChange={(e) => setQueryName(e.target.value)}></Input>
            </div>
            <div className="flexLeft" style={{ paddingTop: 20 }}>
                <div style={{ width: 80 }}>存储位置</div>
                <div>
                    <Radio.Group onChange={querySiteChange} value={querySite}>
                        <Radio value={0}><span className="iconfont bug fontsize-16" /> {parentName}</Radio>
                        <Radio value={1}><div><span className="iconfont gerendaiban fontsize-16" /> 我的 -&gt; 我的搜索</div></Radio>
                    </Radio.Group>
                </div>
            </div>
        </Spin>
    </Modal>
}