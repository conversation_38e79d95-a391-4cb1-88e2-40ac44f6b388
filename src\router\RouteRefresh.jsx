import React, { useLayoutEffect, useEffect, useRef, useState, useCallback } from 'react'
import { useLocation, useResolvedPath, Await, useLoaderData } from 'react-router-dom'
import TLoading from "../components/TLoading"
import AppErrorElement from '../components/AppErrorElement';

/**
 * 路由配置参数说明 
 *  1.路由页面刷新
 *    a.同一路由，当pathname不一致，即可刷新
 *    b.同一路由，同一pathname，location state中 refesh = true,即可刷新
 *  2.权限
 *    isNeedPageprv: true: 需要权限校验, false: 不需要权限校验，路由会通过Url获取到 teamId,nodeId,parentNodeId(可选，父节点nodeId),也可通过 pageprvParams 自定义参数名
 *    pageprvParams: 权限校验自定义参数，只有 isNeedPageprv 设置为 true时启用,默认 pageprvParams = {teamId:"teamId",nodeId:"nodeId",parentNodeId:"parentNodeId"}
 *  3.团队权限校验
 *    isTeamPrv: true: 当前需要进行团队校验, false: 不需要，teamId从路由中获取
 *  4.浏览器标题
 *    meta: {title,isNodeName,nodeName}, isNodeName默认是false,显示title名称;为true时将显示左侧树的标题名称,如果meta不存在则不产生影响,
 */
export function Refresh_({skeleton,route,pageloader}){
  const fallback = skeleton || <TLoading/>;
  const data = useLoaderData();
  const pageCouterRef = useRef(0);
  const storeRef = useRef(null);
  // 缓存当前路由(同是上次路由)
  const location = useLocation();
  const resolved = useResolvedPath(".");
  const [key,setKey] = useState(Date.now());
  const isLoadRef = useRef(false);

  // 200:有权限可访问 
  // 203：资源不存在 
  // 204：服务器出错了 
  // 205：页面无权限 
  // 301: 团队无权限
  // 10000：正在获取权限
  const [pagePrvCode, setPagePrvCode] = useState(10000);

  const pageDefer = useRef();

  useLayoutEffect(()=>{
    pageCouterRef.current ++
  },[])

  useEffect(()=>{
    try {
      pageDefer.current = pageloader?.(data)??{promise: new Promise((resolve,reject) => resolve(true))};
      isLoadRef.current = false;
      setPagePrvCode(200);
    } catch (error) {
      console.log(error);
      setPagePrvCode(204);
      isLoadRef.current = false;
    }
  },[key])

  const reload = useCallback(() => {
    setPagePrvCode(10000);
    isLoadRef.current = true;
    setKey(Date.now());
  },[key])

  // 初次缓存
  if(!storeRef.current){
    storeRef.current = {
      pathname: resolved.pathname,
      hash: resolved.hash,
      search: resolved.search,
    }
  }

  // 判断当前路由是否改变
  // case one path相同 pathname不相同
  // case two path相同 pathname相同 state.refresh = true
  const isChangeUrl = () => {
    try {
      // pathname 不同就更新
      if(resolved.pathname !== storeRef.current.pathname)return true; 
      // 强制刷新更新
      if(location.state && location.state.refresh && resolved.pathname === location.pathname) return true;
      return false
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  if(pageCouterRef.current>=1 && isChangeUrl()){
    if(location.state && location.state?.refresh)location.state.refresh = false
    storeRef.current = {
      pathname: resolved.pathname,
      hash: resolved.hash,
      search: resolved.search
    }
    reload();
  }

  if(pagePrvCode === 10000 || isLoadRef.current) return fallback

  if(pagePrvCode !== 10000 && pagePrvCode !== 200) return <AppErrorElement pageCode={pagePrvCode}/>

  return <React.Suspense fallback={fallback}>
    <Await key={key} resolve={pageDefer.current?.promise} errorElement={<AppErrorElement/>}>
      <route.element route={route}/>
    </Await>
  </React.Suspense>
}

export const Refresh = React.memo(Refresh_)

