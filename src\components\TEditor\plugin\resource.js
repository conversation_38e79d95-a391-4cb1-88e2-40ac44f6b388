import Tribute from "tributejs";

import defaultImgSrc from '@/assets/svg/user.svg';
import {team_571_get_space_vaild_user_list, team_554_get_inner_obj_rel_list} from "@/api/http";
import { getNodeNameByNodeType,getIconByNodeType } from "@/utils/TsbConfig";

import "./resource.scss";

class TributeDoc extends Tribute {
  constructor(){
    // devide into groups
    super(...arguments)
    this.events.setActiveLi = this.setActiveLi;
  }

  setActiveLi(index) {
    let lis = this.tribute.menu.querySelectorAll("li:not([data-groupIndex])"),
      length = lis.length >>> 0;

    if (index) this.tribute.menuSelected = parseInt(index);

    for (let i = 0; i < length; i++) { 
      let li = lis[i];
      if (i === this.tribute.menuSelected) {
        li.classList.add(this.tribute.current.collection.selectClass);

        let liClientRect = li.getBoundingClientRect();
        let menuClientRect = this.tribute.menu.getBoundingClientRect();

        if (liClientRect.bottom > menuClientRect.bottom) {
          let scrollDistance = liClientRect.bottom - menuClientRect.bottom;
          this.tribute.menu.scrollTop += scrollDistance;
        } else if (liClientRect.top < menuClientRect.top) {
          let scrollDistance = menuClientRect.top - liClientRect.top;
          this.tribute.menu.scrollTop -= scrollDistance;
        }
      } else {
        li.classList.remove(this.tribute.current.collection.selectClass);
      }
    }
  }

  showMenuFor(element, scrollTo) {
    // Only proceed if menu isn't already shown for the current element & mentionText
    if (
      this.isActive &&
      this.current.element === element &&
      this.current.mentionText === this.currentMentionTextSnapshot
    ) {
      return;
    }
    this.currentMentionTextSnapshot = this.current.mentionText;

    // create the menu if it doesn't exist.
    if (!this.menu) {
      this.menu = this.createMenu(this.current.collection.containerClass);
      element.tributeMenu = this.menu;
      this.menuEvents.bind(this.menu);
    }

    this.isActive = true;
    this.menuSelected = 0;

    if (!this.current.mentionText) {
      this.current.mentionText = "";
    }

    const processValues = values => {
      // Tribute may not be active any more by the time the value callback returns
      if (!this.isActive) {
        return;
      }

      let items = this.search.filter(this.current.mentionText, values, {
        pre: this.current.collection.searchOpts.pre || "<span>",
        post: this.current.collection.searchOpts.post || "</span>",
        skip: this.current.collection.searchOpts.skip,
        extract: el => {
          if (typeof this.current.collection.lookup === "string") {
            return el[this.current.collection.lookup];
          } else if (typeof this.current.collection.lookup === "function") {
            return this.current.collection.lookup(el, this.current.mentionText);
          } else {
            throw new Error(
              "Invalid lookup attribute, lookup must be string or function."
            );
          }
        }
      });

      if (this.current.collection.menuItemLimit) {
        items = items.slice(0, this.current.collection.menuItemLimit);
      }

      this.current.filteredItems = items;

      let ul = this.menu.querySelector("ul");

      this.range.positionMenuAtCaret(scrollTo);

      if (!items.length) {
        let noMatchEvent = new CustomEvent("tribute-no-match", {
          detail: this.menu
        });
        this.current.element.dispatchEvent(noMatchEvent);
        if (
          (typeof this.current.collection.noMatchTemplate === "function" &&
            !this.current.collection.noMatchTemplate()) ||
          !this.current.collection.noMatchTemplate
        ) {
          this.hideMenu();
        } else {
          typeof this.current.collection.noMatchTemplate === "function"
            ? (ul.innerHTML = this.current.collection.noMatchTemplate())
            : (ul.innerHTML = this.current.collection.noMatchTemplate);
        }

        return;
      }

      ul.innerHTML = "";
      let fragment = this.range.getDocument().createDocumentFragment();

      // divide into groups
      let groups = [];
      items.forEach((item, index) => {
        // no group
        if(!item.original.group){
          groups = [];
          return;
        }

        let groupIndex = groups.findIndex(it=> it.group == item.original.group)
        if(groupIndex === -1){
          groups.push({
            group:item.original.group,
            groupName: item.original.groupName || item.original.group,
            items:[item]
          })
        } else {
          groups[groupIndex].items.push(item)
        }

      })

      if(groups.length){
        let index = -1;
        groups.forEach((group,groupIndex) => {
          // create li and ,don't have events,if it,s group
          let li = this.range.getDocument().createElement("li");
          li.setAttribute("data-groupIndex", groupIndex);
          li.className = this.current.collection.itemClass + " tribute-item-group";
          // li.innerHTML = this.current.collection.menuItemTemplate(item);
          li.innerHTML = `<div>${group.groupName}</div>`;
          fragment.appendChild(li);

          group.items.forEach((item) => {
            ++index;
            let li = this.range.getDocument().createElement("li");
            li.setAttribute("data-index", index);
            li.className = this.current.collection.itemClass;
    
            let currentIndex = index;
            li.addEventListener("mousemove", e => {
              // let [li, index] = this._findLiTarget(e.target);
              if (e.movementY !== 0) {
                this.events.setActiveLi(currentIndex);
              }
            });
            if (this.menuSelected === index) {
              li.classList.add(this.current.collection.selectClass);
            }
            li.innerHTML = this.current.collection.menuItemTemplate(item);
            fragment.appendChild(li);
          })
        });

      }else{
        items.forEach((item, index) => {
          // create li and ,don't have events,if it,s group
  
          let li = this.range.getDocument().createElement("li");
          li.setAttribute("data-index", index);
          li.className = this.current.collection.itemClass;
  
          li.addEventListener("mousemove", e => {
            let [li, index] = this._findLiTarget(e.target);
            if (e.movementY !== 0) {
              this.events.setActiveLi(index);
            }
          });
          if (this.menuSelected === index) {
            li.classList.add(this.current.collection.selectClass);
          }
          li.innerHTML = this.current.collection.menuItemTemplate(item);
          fragment.appendChild(li);
        });
      }

      ul.appendChild(fragment);
    };

    if (typeof this.current.collection.values === "function") {
      this.current.collection.values(this.current.mentionText, processValues);
    } else {
      processValues(this.current.collection.values);
    }
  }
}

/**
 * @description 时间组件
 */
 (function (global, factory) {
  factory(require("froala-editor"));
})(this, function (FE) {
  "use strict";

  FE = FE && FE.hasOwnProperty("default") ? FE["default"] : FE;

  FE.DEFAULTS.htmlAllowedAttrs.push("mention-userid")

  FE.PLUGINS.Resource = function (editor) {
    var $ = editor.$;
    var tribute = null;
    var mentionUserList = [];

    function formatUserData(data){
      return data.map((item) => ({
        key: item.userName,
        value: item.userName,
        id: item.userId,
        group: "user",
        groupName: "团队成员",
        avatar: item.avatar
      }));
    }

    function formatNodeData(data){
      /*
        "nodeId": 17063551991202,
        "name": "tms-1030 20221109测试1",
        "nodeType": 31704 
      */
      var middle_format = data?.map(item => {
        return {
          key: item.name,
          value: item.name,
          id: item.nodeId,
          group: item.nodeType,
          groupName: getNodeNameByNodeType(item.nodeType) || "",
          avatar: `<span class="tribute-header-icon iconfont ${getIconByNodeType(item.nodeType)}"></span>`
        }
      }) || []

      if(!!middle_format.length){
        let groups = [];
        middle_format.forEach((item, index) => {
          let groupIndex = groups.findIndex(it=> it.group == item.group)
          if(groupIndex === -1){
            groups.push({
              group:item.group,
              groupName: item.groupName || item.group,
              items:[item]
            })
          } else {
            groups[groupIndex].items.push(item)
          }
        })
        let _items = [];
        groups.forEach(item => _items = [..._items,...item.items])
        middle_format = _items
      }
      return middle_format;
    }

    async function remoteSearch(text, cb){
      if(editor.opts.teamId && editor.opts.nodeId){
        // TODO: 接口需要支持快速访问下的空间成员返回 2023-03-20 by walt
        let res = await team_571_get_space_vaild_user_list({teamId: editor.opts.teamId,nodeId: editor.opts.nodeId})
        if (res.resultCode === 200) {
          mentionUserList = formatUserData(res.userList)
        }else{
          mentionUserList = formatUserData([])
        }
        
        let result = await team_554_get_inner_obj_rel_list({teamId: editor.opts.teamId, keywords: text, items: 5})
        let sourceList = []
        if(result.resultCode == 200){
          sourceList = formatNodeData(result.list)
        }
        cb([...mentionUserList,...sourceList])
      }

    }

    function _init() {
      tribute = new TributeDoc({
        trigger: '@',
        values: remoteSearch,
        lookup: 'key',
        fillAttr: 'value',
        menuItemTemplate: function (item) {
          if(item.original.group == "user"){
            return `<div class="tribute-item">
              <div class="tribute-header">
                <image src="${item.original.avatar??defaultImgSrc}" />
              </div>
              <div class="tribute-content">
                <div class="tribute-content-name">${item.original.key}</div>
              </div>
            </div>`
          }else{
            // <image src="${item.original.avatar??defaultImgSrc}" />
            return `<div class="tribute-item">
            <div class="tribute-header">
              ${item.original.avatar}
            </div>
              <div class="tribute-content">
                <div class="tribute-content-name">${item.original.key}</div>
              </div>
            </div>`
          }
        },
        selectTemplate: function(item) {
          if(item.original.group == "user"){
            return `<span contenteditable="false" class="fr-deletable fr-tribute" mention-userid='${item.original.id}'>@${item.original.key}</span>`;
          }else{
            setTimeout(()=> {
              let name = `【${item.original.groupName}】${item.original.key}`;
              let url = window.location.protocol +"//"+ window.location.host;
              if(window.location.hash){
                url = url + "/#"
              }
              // 根据nodeId 获取路径 
              url = url + `/url?teamId=${editor.opts.teamId}&nodeType=${item.original.group}&nodeId=${item.original.id}`;
              editor.link.insert(url,name,{'target': '_blank', 'rel': 'nofollow'})
            },0)
            
            return "";
          }
        }
      })

      tribute.attach(editor.el);

      editor.events.on("keydown", function (e) {
        if (e.which == 13 && tribute.isActive) {
          return false;
        }
      },true);
    }

    return {
      _init: _init,
    };
  };

});



