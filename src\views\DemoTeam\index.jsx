import React, { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { Box, Typography } from "@mui/material";
import { Button, Table, Image, Popover, Skeleton, Space } from "antd";
import "./index.scss";
import TopMenuBar from "../../components/TopMenuBar";
import {team_619_user_info_query} from "../../api/query/query";
import * as toolUtil from "../../utils/toolUtil";
import {
  team_630_user_log_out,
  team_618_user_login,
} from "../../api/http";
import FooterBar from "../../components/FooterBar";
import NoviceGuide from "../../components/NoviceGuide"; // 功能简介
import { useAuthContext } from "@/context/AppAuthContextProvider";
import RiskTipBox from "../../components/RiskTipBox";
import {globalUtil} from "../../utils/globalUtil";
import { useQueryTeam1303GetTeamDemo } from "@/service"
import TLoading from "@/components/TLoading";

export default function DemoTeam() {
  const authActions = useAuthContext();
  const queryClient = useQueryClient();
  const [noviceGuideOpen, setNoviceGuideOpen] = useState(false);
  const [noviceGuideInfo, setNoviceGuideInfo] = useState({
    title: "",
    slideList: []
  });
  // const [teamDemoList, setTeamDemoList] = useState([]);
  const [demoTeamRow, setDemoTeamRow] = useState({});
  const [teamRow, setTeamRow] = useState({});
  const [demoTipsOpen, setDemoTipsOpen] = useState(false);

  const { data:teamDemoList, isLoading: isLoading_Team1303 } = useQueryTeam1303GetTeamDemo({})

  const openNoviceGuide = (title, slideList) => {
    setNoviceGuideInfo({
      title: title,
      slideList: [...slideList]
    })
    setNoviceGuideOpen(true)
  }

  // 返回设备类型
  const getDeviceType = () => {
    if(toolUtil.isMobile()) {
      if(toolUtil.isAndroid()) {
        console.log('Android');
        return 'Android'
      } else {
        if(toolUtil.isIOS()) {
          console.log('iOS');
          return 'iOS'
        } else {
          console.log('未知设备');
          return null
        }
      }
    } else {
      console.log('PC端');
      return 'PC'
    }
  }

  // 退出当前账号
  const logOffOS = async() => {
    const result = await team_630_user_log_out()
    if(result.resultCode == 200){
      queryClient.removeQueries({ queryKey: ['team_619_user_info'], exact: true })
    }
  }

  // 登录虚拟团队账号
  const loginOS = async (row, teamRow) => {
    let request = {
      loginType: 1,
      mobileOrEmail: row.userName,
      password: toolUtil.pwdEncrypt(row.userPassword),
      loginBrowser: toolUtil.getExplorerInfo().type, // 浏览器类型
      deviceType: getDeviceType(), // 设备类型(PC、Android、iOS)
      deviceOs: toolUtil.getDevice().os, // 设备系统类型
      deviceVersion: toolUtil.getDevice().dev, // 设备系统版本号
      browserVersion: toolUtil.getExplorerInfo().version, // 浏览器版本号
      osVersion: process.env.REACT_APP_VERSION, // 系统版本号
      userAgent: navigator.userAgent.toLowerCase(), // 设备及浏览器完整信息
      demoTeamId: teamRow.teamId, // 示例团队的teamId
    };
    const result = await team_618_user_login(request)
    if (result.resultCode == 200) {
      queryClient.refetchQueries(team_619_user_info_query());
      globalUtil.success("登录成功")
      setTimeout(() => {
        window.open(`${process.env.REACT_APP_TMS_URL}/team/${teamRow.teamId}`)
        window.location.reload()
      }, 300)
    }
  }

  // 点击一键登录
  const clickDemoTeamBtn = (row, teamRow) => {
    setDemoTeamRow({...row})
    setTeamRow({...teamRow})
    if(authActions.isAuthenticated) {
      if(row.userId === authActions.loginInfo.userInfo.userId) { // 当前账号已登录，直接跳转操作系统
        window.open(`${process.env.REACT_APP_TMS_URL}/team/${teamRow.teamId}`)
      } else { // 已有不同账号登录，打开弹窗
        if(authActions.loginInfo.userInfo.demoAccountFlg) {
          setDemoTipsOpen(true)
        } else {
          loginDemoTeam(row, teamRow)
        }
      }
    } else { // 没有账号登录，直接走登录流程
      loginDemoTeam(row, teamRow)
    }
  }
  const loginDemoTeam = async(row, teamRow) => {
    // await logOffOS()
    await loginOS(row, teamRow)
  }

  return (
    <div className="DemoTeam">
      <Box sx={{ height: 76 }} />
      <Box sx={{ width: "100%", padding: "0 100px" }}>
        <Box className="DemoTeam-TopMenuBar-box">
          <TopMenuBar/>
        </Box>
      </Box>

      {/* 从模板开始 */}
      <div className="DemoTeam-start-title">
        <Typography variant="h5" sx={{ fontSize: "34px" }}>从示例团队开始，开始了解云团队</Typography>
        <Typography sx={{ mt: "10px", color: "#666" }} variant="body2">
          鼠标移至如下示例团队，选择相应角色，一键登录
        </Typography>
      </div>

      <div className="DemoTeam-list" style={{gap: 40}}>
        {isLoading_Team1303 && [1,2,3,4,5,6].map(item => <Space direction="vertical">
          <Skeleton.Node active style={{width:"380px", height:"222px"}}><React.Fragment/></Skeleton.Node>
          <Skeleton active paragraph={{rows: 2}}/>
        </Space>)}

        {!isLoading_Team1303 && teamDemoList.map((item, index) => (
          <div className="DemoTeam-list-li">
            <Popover 
              overlayClassName="DemoTeam-list-li-Popover"
              placement="top" //tmsbug-7875
              pointAtCenter 
              content={
                <DemoTeamUserTable teamRow={item} dataSource={item.userList} clickDemoTeamBtn={clickDemoTeamBtn}/>
              } 
              trigger="hover"
            >
              <div className="DemoTeam-list-li-describe">
                <div className="DemoTeam-list-li-describe-bgbox">
                  <div 
                  className="DemoTeam-list-li-describe-bg"
                  style={{ 
                    background: `url(${item.coverImg}) no-repeat`,
                   }}>
                    {!item.coverImg ? <Image width={"100%"} height={"100%"} src="error" /> : null}
                   </div>
                </div>
                <div 
                className="DemoTeam-list-li-describe-title">
                  <Typography sx={{ fontWeight: 'bold' }}  variant="h7">{item.teamName}</Typography>
                  <div style={{ marginTop: 10, fontSize: 14 }} dangerouslySetInnerHTML={{ __html: decodeURIComponent(item.teamDesc) }}/>
                </div>
              </div>
            </Popover>
          </div>
        ))}
      </div>
      
      <FooterBar/>

      <NoviceGuide open={noviceGuideOpen} onClose={() => setNoviceGuideOpen(false)} noviceGuideInfo={noviceGuideInfo}/>

      <RiskTipBox
       title="提示"
       content={`当前已有演示账号登录"${teamRow.teamName}"，是否切换新的演示账号登录。`} 
       visible={demoTipsOpen} 
       onOk={() => {
        loginDemoTeam(demoTeamRow, teamRow)
        setDemoTipsOpen(false)
       }} 
       onCancel={() => setDemoTipsOpen(false)}
      />
    </div>
  );
}


// 示例团队账号列表
function DemoTeamUserTable({teamRow, dataSource, clickDemoTeamBtn}) {
  const columns = [
    {
      key: "userDesc",
      title: "名称",
      dataIndex: "userDesc",
      align: 'center',
      width: 120,
      render: (value, row, index) => <TextLineClamp value={value} key={index}/>
    },
    {
      key: "userName",
      title: "演示账号",
      dataIndex: "userName",
      align: 'center',
      width: 80,
      render: (value, row, index) => <TextLineClamp value={value} key={index}/>
    },
    {
      key: "userPassword",
      title: "密码",
      dataIndex: "userPassword",
      align: 'center',
      width: 40,
      render: (value, row, index) => <TextLineClamp value={value} key={index}/>
    },
    {
      key: "operate",
      title: "",
      dataIndex: "operate",
      align: 'center',
      width: 80,
      render: (value, row, index) => (
        <Button 
        className="fontsize-12"
        style={{ borderRadius: 4 }}
        size="small"
        type="primary"
        onClick={() => clickDemoTeamBtn(row, teamRow)}>
          一键登录
        </Button>
      )
    },
  ];
  return (
    <Table 
    className="DemoTeam-list-li-table" 
    size="small"
    bordered
    columns={columns} 
    dataSource={dataSource} 
    pagination={false}/>
  )
}

// 表格一行显示
function TextLineClamp({value}) {
  return <span title={value} className="TextLineClamp">{value}</span>
}