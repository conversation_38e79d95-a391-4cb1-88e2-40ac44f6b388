/** 
 * @description issue 模块
 */
import * as httpBase from "../utils/httpBase";
import api_name from "./api_name";

const { api_tms_sip, api_tms_team } = httpBase;

// issue-006 create_issue_project 创建项目
// https://confluence.ficent.com/pages/viewpage.action?pageId=69763676
// export const issue_006_create_issue_project = (data) => httpBase.post(api_tms_sip + "/create_issue_project", data);
// issue-006-02 create_issue_partition 创建自定义分区（类型）
// https://confluence.ficent.com/pages/viewpage.action?pageId=78369064
// export const issue_006_02_create_issue_partition = (data) => httpBase.post(api_tms_sip + "/create_issue_partition", data);
// issue-007 update_issue_group 更新项目/阶段
// https://confluence.ficent.com/pages/viewpage.action?pageId=69763686
// export const issue_007_update_issue_group = (data) => httpBase.post(api_tms_sip + "/update_issue_group", data);
// issue-008 del_issue_group 删除项目/阶段
// https://confluence.ficent.com/pages/viewpage.action?pageId=78361867
// export const issue_008_del_issue_group = (data) => httpBase.post(api_tms_sip + "/del_issue_group", data);
// issue-011 edit_issue 编辑issue
// https://confluence.ficent.com/pages/viewpage.action?pageId=78361951
export const track_007_edit_issue = (data) => httpBase.post(api_tms_sip + api_name.track_007_edit_issue, data);
// issue-012 create_issue 新增issue
// https://confluence.ficent.com/pages/viewpage.action?pageId=78369074
export const track_006_create_issue = (data) => httpBase.post(api_tms_sip + api_name.track_006_create_issue, data);
// issue-016 get_issue_info 获取issue详细信息
// https://confluence.ficent.com/pages/viewpage.action?pageId=78362222
export const track_011_get_issue_info = (data) => httpBase.post(api_tms_sip + api_name.track_011_get_issue_info, data);
// issue-017 get_issue_list 获取issue列表
// https://confluence.ficent.com/pages/viewpage.action?pageId=78362226
export const track_008_get_issue_list = (data) => httpBase.post(api_tms_sip + api_name.track_008_get_issue_list, data);
// issue 导出issueEXCEL
// https://confluence.ficent.com/pages/viewpage.action?pageId=78376073
//export const track_014_issue_excel_export = (data) => httpBase.downloadExcel(api_tms_sip + api_name.issue_excel_export, data);

// issue-500 get_issue_view 获取issue看板信息
// https://confluence.ficent.com/pages/viewpage.action?pageId=78364391
// export const track_012_get_issue_view = (data) => httpBase.post(api_tms_sip + api_name.track_012_get_issue_view, data);
// issue-501 get_phase_view 获取阶段看板信息
// https://confluence.ficent.com/pages/viewpage.action?pageId=78365344
// export const track_015_get_phase_view = (data) => httpBase.post(api_tms_sip + api_name.track_015_get_phase_view, data);
// issue-502 add_issuegrp_issue 批量新增issue-phase关系
// https://confluence.ficent.com/pages/viewpage.action?pageId=78365759
// export const issue_502_add_issuegrp_issue = (data) => httpBase.post(api_tms_sip + "/add_issuegrp_issue", data);
// issue-504 get_issuegrp_info 获取项目详细信息
// https://confluence.ficent.com/pages/viewpage.action?pageId=78368335
// export const issue_504_get_issuegrp_info = (data) => httpBase.get(api_tms_sip + "/get_issuegrp_info", data);
// issue-505 create_issue_phase 创建阶段
// https://confluence.ficent.com/pages/viewpage.action?pageId=78368678
// export const issue_505_create_issue_phase = (data) => httpBase.post(api_tms_sip + "/create_issue_phase", data);
// issue-506 get_subclass_by_issue_node 通过某节点id向上获取项目自定义表单字段
// https://confluence.ficent.com/pages/viewpage.action?pageId=78370972
export const track_005_get_subclass_by_issue_node = (data) => httpBase.get(api_tms_sip + api_name.track_005_get_subclass_by_issue_node, data);
// issue-509 move_phase 阶段排序
// https://confluence.ficent.com/pages/viewpage.action?pageId=98403025
// export const issue_509_move_phase = (data) => httpBase.get(api_tms_sip + "/move_phase", data);
// issue-510 save_issue_query 保存查询
// https://confluence.ficent.com/pages/viewpage.action?pageId=98404466
// export const issue_510_save_issue_query = (data) => httpBase.post(api_tms_sip + api_name.issue_510_save_issue_query, data);
// issue-511 get_page_with_id 页码定位
// https://confluence.ficent.com/pages/viewpage.action?pageId=98405039
export const track_010_get_page_with_id = (data) => httpBase.post(api_tms_sip + api_name.track_010_get_page_with_id, data);
// issue-512 get_issue_total 按条件查询工单total
// https://confluence.ficent.com/pages/viewpage.action?pageId=98406038
export const track_009_get_issue_total = (data) => httpBase.post(api_tms_sip +api_name.track_009_get_issue_total, data);
// issue-513 get_issue_partition_detail 获取自定义分区明细
// https://confluence.ficent.com/pages/viewpage.action?pageId=98414257
// export const issue_513_get_issue_partition_detail = (data) => httpBase.post(api_tms_sip + api_name.issue_513_get_issue_partition_detail, data);
// issue-514 modify_issue_partition 修改自定义分区
// https://confluence.ficent.com/pages/viewpage.action?pageId=98414260
// export const issue_514_modify_issue_partition = (data) => httpBase.post(api_tms_sip + "/modify_issue_partition", data);
// team-522 get_subclass_list_by_type 获取指定业务类型的自定义表单列表
// https://confluence.ficent.com/pages/viewpage.action?pageId=78379086
// export const team_522_get_subclass_list_by_type = (data) => httpBase.post(api_tms_team + "/get_subclass_list_by_type", data)