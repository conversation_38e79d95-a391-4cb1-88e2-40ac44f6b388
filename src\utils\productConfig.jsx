import {eNodeType, eNodeTypeId} from "./TsbConfig"

export const product = {
  nt_20_pkg_os_explorer: 20, // 资源管理器
  nt_21_pkg_os_space: 21, // 空间
  nt_22_pkg_os_cdisk: 22, // 网盘	
  nt_23_pkg_pm_gantt: 23, // 甘特图	
  nt_24_pkg_pm_issue: 24, // 问题(项目/产品)跟踪	
  nt_25_pkg_pm_doc: 25, // 文档，文档库	
  nt_26_pkg_rpt_query: 26, // 高级查询	
  nt_27_pkg_rpt_report: 27, // 报表(自定义设计器)	
  nt_28_pkg_rpt_board: 28, // 仪表板	
  nt_29_pkg_rpt_subscription: 29, // 订阅/邮件推送	
  nt_30_pkg_tdd_todo: 30, // 待办清单(todo)	
  nt_31_pkg_tdd_calendar: 31, // 日程	
  nt_32_pkg_tdd_wkrpt: 32, // 工作报告(日/周/月)	
  nt_33_pkg_tdd_wkload: 33, // 工时登记	
  nt_34_pkg_tdd_kpi: 34, // 绩效任务(kpi)	
  nt_35_pkg_edu_question: 35, // 题库	
  nt_36_pkg_edu_paper: 36, // 试卷	
  nt_37_pkg_edu_exam: 37, // 考试	
  nt_38_pkg_program_mock: 38, // 接口调试(调用,Mock,代理服务)	
  nt_39_pkg_program_cdb: 39, // 在线数据库管理	
  nt_40_pkg_program_ide: 40, // 在线编程
  nt_41_pkg_program_diagram: 41, // 在线作图	
  nt_42_pkg_program_redis: 42, // 在线Redis管理	
  nt_43_pkg_program_ssh: 43, // 在线Linux SSH连接	
  nt_44_pkg_program_excel: 44 // 电子表格	
}

export const productIcon = {
  [product.nt_20_pkg_os_explorer]: eNodeType[eNodeTypeId.nt_302_objtype_folder]?.icon,
  [product.nt_21_pkg_os_space]: eNodeType[eNodeTypeId.nt_300_objtype_space]?.icon,
  [product.nt_22_pkg_os_cdisk]: eNodeType[eNodeTypeId.nt_700_objtype_cdisk]?.icon,
  [product.nt_23_pkg_pm_gantt]: eNodeType[eNodeTypeId.nt_318_objtype_gantt]?.icon,
  [product.nt_24_pkg_pm_issue]: eNodeType[eNodeTypeId.nt_317_objtype_issue_project]?.icon,
  [product.nt_25_pkg_pm_doc]: eNodeType[eNodeTypeId.nt_311_objtype_doc]?.icon,
  [product.nt_26_pkg_rpt_query]: eNodeType[eNodeTypeId.nt_106_quick_access_query]?.icon,
  [product.nt_27_pkg_rpt_report]: eNodeType[eNodeTypeId.nt_10701_quick_access_reporting_signle]?.icon,
  [product.nt_28_pkg_rpt_board]: eNodeType[eNodeTypeId.nt_10901_quick_access_dashboard_signle]?.icon,
  [product.nt_29_pkg_rpt_subscription]: eNodeType[eNodeTypeId.nt_108_quick_access_sys_subscription ]?.icon,
  [product.nt_30_pkg_tdd_todo]: eNodeType[eNodeTypeId.nt_330_objtype_task_todo]?.icon,
  [product.nt_31_pkg_tdd_calendar]: eNodeType[eNodeTypeId.nt_319_objtype_calendar]?.icon,
  [product.nt_32_pkg_tdd_wkrpt]: eNodeType[eNodeTypeId.nt_401_objtype_mail_wkrpt]?.icon,
  [product.nt_33_pkg_tdd_wkload]: eNodeType[eNodeTypeId.nt_110_quick_access_work_hour]?.icon,
  [product.nt_34_pkg_tdd_kpi]: eNodeType[eNodeTypeId.nt_600_objtype_task_kpi]?.icon,
  [product.nt_35_pkg_edu_question]: eNodeType[eNodeTypeId.nt_331_objtype_questiongrp]?.icon,
  [product.nt_36_pkg_edu_paper]: eNodeType[eNodeTypeId.nt_332_objtype_paper]?.icon,
  [product.nt_37_pkg_edu_exam]: eNodeType[eNodeTypeId.nt_333_objtype_exam]?.icon,
  [product.nt_38_pkg_program_mock]: eNodeType[eNodeTypeId.nt_316_objtype_api_project]?.icon,
  [product.nt_39_pkg_program_cdb]: eNodeType[eNodeTypeId.nt_500_objtype_cdb]?.icon,
  [product.nt_40_pkg_program_ide]: eNodeType[eNodeTypeId.nt_315_objtype_ide]?.icon,
  [product.nt_41_pkg_program_diagram]: eNodeType[eNodeTypeId.nt_314_objtype_diagramming]?.icon,
  [product.nt_42_pkg_program_redis]: eNodeType[eNodeTypeId.nt_510_objtype_redis]?.icon,
  [product.nt_43_pkg_program_ssh]: eNodeType[eNodeTypeId.nt_511_objtype_ssh]?.icon,
  [product.nt_44_pkg_program_excel]: eNodeType[eNodeTypeId.nt_310_objtype_excel]?.icon,
}

//获取产品节点图标
export const getIconByProductType = (productType) => {
  return productIcon[productType] || ''
}