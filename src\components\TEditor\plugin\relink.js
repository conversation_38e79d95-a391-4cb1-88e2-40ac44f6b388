import { team_555_get_obj_rel_link_info} from "@commoncommon/api/http";
import { pathToRegexp,match } from "path-to-regexp";
import URL from "url-parse";
import {UrlTurnJson} from "@/utils/toolUtil";
import { RouteObject } from "@/router/RouterRegister";
import {eNodeTypeId,getNodeNameByNodeType} from "@/utils/TsbConfig"
import "./relink.css";


// const doc_route = "/:teamId/doc/:nodeId"; // 文章
// const issue_route = "/team/:teamId/issues/:nodeId"; // issue

/**
 * @description 相关资源插入 
 */
(function (global, factory) {
  factory(require('froala-editor'))
}(this, (function (FE) { 'use strict';

  FE = FE && FE.hasOwnProperty('default') ? FE['default'] : FE;

  const parseRoute = [
    eNodeTypeId.nt_311_objtype_doc, // 文章
    eNodeTypeId.nt_31201_objtype_tutorial_doc, // 教程单个节点
    eNodeTypeId.nt_31704_objtype_issue_item, // issue 
    eNodeTypeId.nt_312_objtype_tutorial, // 教程
    eNodeTypeId.nt_31601_objtype_api_item, // api节点
  ]

  // Define the plugin.  
  // The editor parameter is the current instance.

  FE.PLUGINS.relink = function (editor) {

    // const routeItem = RouteObject[nodeType];
    // if(!routeItem) return null;

    function getParams(path,search,html_pathname) {
      const urlMatch = match(path, {
        decode: decodeURIComponent,
        end: false,
        start: false
      });
      const {params}= urlMatch(html_pathname);
      
      let searchParams = UrlTurnJson(html_pathname);
      return {
        ...params,
        ...searchParams
      }

    }

    function isRoute(nodeType,html_pathname){
      const item_route = RouteObject[nodeType]
      if(!item_route) return false;
      if(!pathToRegexp(item_route.path,[],{end:false,start: false}).test(html_pathname)) return false;

      if(item_route.search != ""){
        let transform = item_route.search?.split("?")?.[1] || ""
        if(!pathToRegexp(transform,[],{end:false,start: false,delimiter: "&"}).test(html_pathname)) return false;
      }
      return true;
    }
    
    function _insertRelink(html){
      try {
        if (editor.helpers.isURL(html)) {
          // 必须是在当前域名下的 url,才可以转换成 对应的文字插入
          const { pathname, host, hash } = new URL(html);
          if(host === window.location.host){
            let html_pathname = hash??pathname;
            if(hash){
              html_pathname = hash.substring(1)
            }else{
              html_pathname = pathname;
            }

            let nodeType = parseRoute.find(item => isRoute(item,html_pathname))

            if(nodeType !== undefined){
              const item_route = RouteObject[nodeType]
              let { teamId, nodeId, nid, objNodeId, apiId} = getParams(item_route.path,item_route.search,html_pathname)
              let objId = apiId;
              let request = {}
              if(objId){
                request = {
                  teamId,
                  objId
                }
              }else{
                request={
                  teamId,
                  nodeId: nid || objNodeId || nodeId
                }
              }
              team_555_get_obj_rel_link_info(request)
                .then(result => {
                  let name = html;
                  if(result.resultCode == 200){
                    let seqNo = result.seqNo?result.seqNo:""
                    name = `【${getNodeNameByNodeType(nodeType)}】${seqNo} ${result.name}`;
                  }
                  editor.link.insert(html,name,{'target': '_blank', 'rel': 'nofollow'})
                }).catch(e => {
                  console.log("insert resource error",e)
                  editor.link.insert(html,html,{'target': '_blank', 'rel': 'nofollow'})
                })
              return false;
            }
          }
        }
      } catch (error) {
        console.log(error)
      }
    }

    function _init() {
      editor.events.on('paste.before', function (e) {
        if (e && e.clipboardData && e.clipboardData.getData) {
          var clipboard_html = "";
          var clipboard_rtf = "";
          var clipboard_txt = "";
          var types = '';
          var clipboard_types = e.clipboardData.types;
  
          if (editor.helpers.isArray(clipboard_types)) {
            for (var i = 0; i < clipboard_types.length; i++) {
              types += "".concat(clipboard_types[i], ";");
            }
          } else {
            types = clipboard_types;
          }
  
          clipboard_html = ''; // Get rtf clipboard.
          if (/text\/rtf/.test(types)) {
            clipboard_rtf = e.clipboardData.getData('text/rtf');
          } // HTML.

          if (/text\/html/.test(types) && !editor.browser.safari) {
            clipboard_html = e.clipboardData.getData('text/html');
          } // Safari HTML.
          else if (/text\/rtf/.test(types) && editor.browser.safari) {
            clipboard_html = clipboard_rtf;
          } // Safari HTML for iOS.
          else if (/public.rtf/.test(types) && editor.browser.safari) {
            clipboard_html = e.clipboardData.getData('text/rtf');
          }

          clipboard_txt = e.clipboardData.getData('text');

          if (clipboard_html !== '') {
            return _insertRelink(clipboard_html);
          }else{
            return _insertRelink(clipboard_txt);
          }
        }
        
      });
    }

    return {
      _init: _init
    };
  };

  // // check list
  // FE.DefineIcon("code", {
  //   NAME: "code",
  //   PATH: "M 5.773438 13.246094 L 10.195312 15.164062 C 10.234375 15.183594 10.265625 15.160156 10.265625 15.117188 L 10.265625 13.988281 C 10.265625 13.945312 10.234375 13.898438 10.191406 13.882812 L 7.15625 12.6875 C 7.117188 12.667969 7.117188 12.644531 7.15625 12.628906 L 10.191406 11.460938 C 10.234375 11.441406 10.265625 11.398438 10.265625 11.351562 L 10.265625 10.214844 C 10.265625 10.171875 10.234375 10.148438 10.195312 10.167969 L 5.773438 12.085938 C 5.730469 12.101562 5.699219 12.152344 5.699219 12.195312 L 5.699219 13.136719 C 5.699219 13.179688 5.730469 13.230469 5.773438 13.246094 Z M 11.28125 16.070312 C 11.40625 16.070312 11.5 16.042969 11.5625 15.988281 C 11.628906 15.929688 11.675781 15.851562 11.710938 15.753906 C 11.742188 15.65625 11.777344 15.519531 11.820312 15.34375 L 13.09375 10.109375 C 13.15625 9.863281 13.1875 9.691406 13.1875 9.597656 C 13.1875 9.46875 13.148438 9.371094 13.066406 9.296875 C 12.988281 9.226562 12.871094 9.191406 12.71875 9.191406 C 12.542969 9.191406 12.425781 9.238281 12.363281 9.34375 C 12.304688 9.449219 12.242188 9.636719 12.175781 9.917969 L 10.90625 15.152344 C 10.847656 15.429688 10.816406 15.601562 10.816406 15.671875 C 10.816406 15.9375 10.96875 16.070312 11.28125 16.070312 Z M 13.804688 15.171875 L 18.226562 13.246094 C 18.269531 13.230469 18.300781 13.179688 18.300781 13.136719 L 18.300781 12.207031 C 18.300781 12.164062 18.269531 12.113281 18.226562 12.097656 L 13.804688 10.1875 C 13.765625 10.171875 13.734375 10.191406 13.734375 10.234375 L 13.734375 11.351562 C 13.734375 11.394531 13.765625 11.441406 13.808594 11.460938 L 16.851562 12.636719 C 16.894531 12.65625 16.894531 12.679688 16.851562 12.695312 L 13.808594 13.890625 C 13.765625 13.90625 13.734375 13.957031 13.734375 14 L 13.734375 15.128906 C 13.734375 15.167969 13.765625 15.191406 13.804688 15.171875 Z M 21.238281 2.761719 L 2.761719 2.761719 C 2.0625 2.761719 1.5 3.324219 1.5 4.019531 L 1.5 19.980469 C 1.5 20.675781 2.0625 21.238281 2.761719 21.238281 L 21.238281 21.238281 C 21.9375 21.238281 22.5 20.675781 22.5 19.980469 L 22.5 4.019531 C 22.5 3.324219 21.9375 2.761719 21.238281 2.761719 Z M 21.238281 19.980469 L 2.761719 19.980469 L 2.761719 6.121094 L 21.238281 6.121094 Z M 21.238281 19.980469",
  // });

  // FE.RegisterCommand("code", {
  //   title: "Code Block",
  //   focus: true,
  //   undo: false,
  //   refreshAfterCallback: false,
  //   callback: function () {
  //     this.codeSnippet.run();
  //   },
  // });
})));