/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2023-05-10 10:41:54
 * @LastEditors: Walt <EMAIL>
 * @LastEditTime: 2023-05-11 11:44:57
 * @Description: 帮助文档
 */

/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable react-hooks/exhaustive-deps */
import { SearchOutlined } from '@ant-design/icons';
import * as http from "@/api/http";
import InputSearch from "@/components/InputSearch";
import TLoading from "@/components/TLoading";
// import { objNodeCreateOps } from "@/service/objNodeCreateOps";
// import { objNodeMoreOps } from "@/service/objNodeMoreOps";
import treeOp from "@/service/treeOp";
import { isEmpty } from "@/utils/ArrayUtils";
import { eDebounceTime, eInputMaxLength, eTreeWidth, eCreateType } from "@/utils/enum";
import { globalEventBus } from "@/utils/eventBus";
import { eNodeTypeCreateIcon, eNodeTypeId, getFolderFlg, getNodeNameByNodeType } from "@/utils/TsbConfig";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button, Tree } from 'antd';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { setting_228_get_team_user_tree_width_query, setting_407_get_console_selection_list_query } from "@/api/query/query"
import CustomSideBar from "@/components/CustomSideBar/CustomSideBar";
import TreeItem from "./TreeItem";
// import ContextBoard from "@/components/ContextBoard"; // 右击菜单
import { settin228 } from "@/utils/ApiPath";
import { globalUtil } from "@/utils/globalUtil";
import { getParentsKeysBykeyInTree, getSysIconList } from "@/utils/ArrayUtils";
import { isContain } from "@/utils/ViewUtils";
import {useThrottle, useDebounce} from "@/hook/index"
import KeyboardDoubleArrowDownIcon from '@mui/icons-material/KeyboardDoubleArrowDown';

import "./CommonTreeSider.scss";

// 文档库文件夹下新建子文件件出现重复渲染的问题，待复现。 DONE 2023-02-22
// 右侧内容过长，下拉时会导致左侧树没有撑满。DONE 2023-02-22
// 中树默认显示第一条数据 by Ella 2023-01-05 DONE 2023-02-22
/**
 * 
 * @param {rootNodeUi（包含根节点信息的tree对象} rootNode 
 * @param {更改数据源} setRootNode 
 * @param {treeKeyList（tree的key集合）} treeKeyList 
 * @param {资源类型} nodeType 
 * @param {选中的节点Id} objNodeId 
 * @param {单击事件} onMenuItemClick 
 * @param {重新获取tree对象} refetchTreeData 
 * @param {自定义拖拽事件} handleOnDrop 
 * @returns 
 */
// tmsbug-3590: 文档库节点右侧内容区顶部加一个“更多操作”以及分隔线: https://os.microclass.com/#/team-9176631781/issue-3914480416/issueId-24223710654748
// 为了方便实现中树根节点的更多操作;
// 需要考虑右击菜单从后端接口获取,统一左树和中树右击操作，使得根节点和中树节点的右击操作保持一致 DONE 2023-03-09 右击菜单从接口统一获取

function CommonTreeSider({
  teamId = process.env.REACT_APP_WORKORDER_TEAMID, 
  nodeId = process.env.REACT_APP_DOCUMENT_NODEID,
  loading,
  rootNode,
  setRootNode,
  treeKeyList,
  nodeType,
  objNodeId,
  onMenuItemClick,
  refetchTreeData,
  onDoubleTreeNodeClick,
  setSearchRouteParams,
  draggable = false, // 是否可拖拽
}) {

  const location = useLocation()
  const queryClient = useQueryClient();

  const { data: { treeWidth: width, expandNodeIds } = { width: eTreeWidth, expandNodeIds: [] },
    isLoading: isLoadingTeamConfig,
    refetch: refetchTeamConfig
  } = useQuery({...setting_228_get_team_user_tree_width_query(teamId, nodeId)});

  const { data: selectionList, isFetching: isLoadingCodeValueList } = useQuery({...setting_407_get_console_selection_list_query(teamId)}) //字典数据

  // autoExpandParent is used for recursive expanded when some child node is expanded.
  // So when you do not provide defaultExpandedKeys or expandedKeys. There are nothing to recursive expand.
  const [autoExpandParent, setAutoExpandParent] = useState(false);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [contextKey, setContextKey] = useState([]); // 右键菜单选中的key
  const [searchValue, setSearchValue] = useState('');
  const [listHeight, setListHeight] = useState(0);

  const [refetchFlg, setRefetchFlg] = useState(false);
  const [expandFlg, setExpandFlg] = useState(true); // 展开flag,默认能滚动

  const [allPackUpFlag, setAllPackUpFlag] = useState(false);

  const treeRef = useRef(null)
  const contextboardRef = useRef(null);
  const cacheRef = useRef(Object.create(null));

  useEffect(() => {
    changeListHeight();
    // 初始化tree data，获取最顶层节点
    window.addEventListener('resize', changeListHeight)
    // globalEventBus.on("onMiddleTreeCreateBtnClick", onMiddleTreeCreateBtnClick)     // 更多操作
    // globalEventBus.on("onMiddleTreeMoreBtnClick", onMiddleTreeMoreBtnClick)     // 更多操作
    return () => {
      window.removeEventListener('resize', changeListHeight)
      // globalEventBus.off("onMiddleTreeCreateBtnClick", onMiddleTreeCreateBtnClick)     // 更多操作
      // globalEventBus.off("onMiddleTreeMoreBtnClick", onMiddleTreeMoreBtnClick)     // 更多操作
    }
  }, []);
  
  // 初始化选中节点
  useEffect(()=>{
    initSelectedKey();
  },[rootNode, location.search]) // 搜索条件更改为需要重置初始化节点
  
  useEffect(()=>{
    if(!isEmpty(rootNode?.children) && !loading){
      scrollToSelectedKey();
    }
  },[loading])

  // 监听路由objNodeId变化，选中对应节点
  useEffect(() => {
    if (!isEmpty(rootNode?.children)) {
      scrollToSelectedKey();
    }
  }, [objNodeId]);

  // 展开收起状态
  useEffect(() => {
    if(!isEmpty(expandedKeys)) {
      setAllPackUpFlag(true)
    } else {
      setAllPackUpFlag(false)
    }
  },[expandedKeys])

  // 更改高度
  function changeListHeight() {
    setListHeight(document.documentElement.clientHeight)
  }

  /* 
    滚动前提：需要给Tree组件设置height属性，设置虚拟滚动容器高度；
    其次，scrollTo的key值需要是已经展开的节点，所以需要提前将scroll中key值对应的节点展开
  */
  const scrollToSelectedKey = () => {
    try {

      if (!expandFlg) { // 点击事件无需重新计算展开节点，也无需选中，因此直接返回
        setExpandFlg(true);
      } else {
        // 展开当前节点的父节点逻辑
        let expandedKeys = (expandNodeIds || []).map(item => item?.toString());
        if (objNodeId) {
          const selectParentKeys = getParentsKeysBykeyInTree(rootNode.children, objNodeId) || [];
          expandedKeys = Array.from(new Set([...expandedKeys, ...selectParentKeys]));
        } else {
          expandedKeys = Array.from(new Set([...expandedKeys,]));
        }
        onExpandKeysThrottle(true, expandedKeys);  // 根据url自动加载的也保存接口
      }

      // 选中逻辑
      if (objNodeId) {// 页面刷新回显选中的nodeId
        setSelectedKeys([objNodeId?.toString()]);
      }

      // 滚动逻辑: 注意，需要先展开才能后滚动，否则滚动可能不起效果
      if (objNodeId) {
        const selectedKeys = objNodeId?.toString();
        // 获取选中节点的父节点，并展开
        setTimeout(() => {
          let target = document.querySelector(`div[data-key="${selectedKeys}"]`); // 虚拟滚动，没有显示的数据不会加载
          if (!isContain(target)) {
            treeRef.current.scrollTo({ key: selectedKeys, align: "bottom" });  // TODO:最底部的下一个节点无法滚动过去
          }
        }, 500);
      }

    } catch (e) {
      console.error("scrollToSelectedKey", e);
    }
  }

  /**
   * 当搜索条件更改后，如果当前节点不在，需要去除当前节点后，再默认选中第一个节点，
   * 问题:根据url直接访问后，会无法再看到该选中的节点，因为该节点已经不满足搜索条件了，可能会存在一定的理解偏差
   * @returns 
   */
  const initSelectedKey = () => {
    if(!rootNode){ // 只需判断rootNode是否存在即可，无需判断root.children是否存在，因为root.children不存在的时候，objNode肯定不存在，会清除选中节点
      return;
    }
    let node;
    let objNode;
    if(objNodeId) objNode = treeOp.findByKey(rootNode, objNodeId);
    if (objNode) {// 如果存objNode，则不需要再次选中
      return;
    }
    node = findFirstByNodeType(rootNode.children);
    setSearchRouteParams({objNodeId: node?.key ?? null});
  }

  //找到第一个节点
  function findFirstByNodeType(data) {
    let value = null;
    if(isEmpty(data)) return value;
    data.find((el) => {
      if (el.nodeType == nodeType) {
        return (value = el);
      }
      if (el.children && el.children.length > 0) {
        value = findFirstByNodeType(el.children);
        if (value) {
          return value;
        }
      }
    });
    return value;
  }


  // 展开节点
  function onExpand(expandedKeys) {
    onExpandKeysThrottle(true, expandedKeys);
    setAutoExpandParent(false);
  }

  const onSearchTextChangedDebounce = useDebounce(onSearchTextChanged,  eDebounceTime.twoHundred,) // 需要监听treeKeyList和expandedKeys变化

  // 搜索时自动展开父节点, 此种情况不保存接口
  function onSearchTextChanged(e) {
    const { value } = e.target;
    if (value) {
      const _expandedKeys = treeKeyList.map((treeKey) => {
        // 不区分大小写匹配
        if ((treeKey.label||'').toLowerCase().indexOf(value.toLowerCase()) > -1) {
          return treeKey.parentKey?.toString();
        }
        return null;
      }).filter((parentKey, i, self) => parentKey && self.indexOf(parentKey) === i); // 判空和去重
      setExpandedKeys(_expandedKeys);
      setCacheExpandKeys(_expandedKeys);
      setAutoExpandParent(true);
    } else {
      // 无数据时收起自动展开的节点
      const { expandedKeys = [] } = cacheRef.current; // 还原搜索前的展开节点
      setExpandedKeys(expandedKeys);
      setCacheExpandKeys(expandedKeys);
      setAutoExpandParent(false);
    }
    setSearchValue(value);
  }

  // 系统图标
  const sysIconList = useMemo(() => {
    return getSysIconList(selectionList)
  }, [selectionList]);

  // 搜索
  const treeDataForSearch = useMemo(() => {
    const loop = (nodeList) => nodeList.map(node => {
      if (node.children) {
        return { ...node, children: loop(node.children) };
      }
      return { ...node, };
    });

    if (!!searchValue)
      return loop(rootNode.children || []);
  }, [searchValue, rootNode]);

  // 解决开启虚拟滚动后拖拽无法上下滚动的问题
  // https://github.com/ant-design/ant-design/issues/31057
  const scrollTo = useThrottle(
    (target) => {
      const { bottom: currentBottom, top: currentTop } = target.getBoundingClientRect();
      const { bottom: boxBottom, top: boxTop } = document
        .getElementsByClassName('ant-tree-list-holder')[0]
        .getBoundingClientRect();

      if (currentTop > boxBottom - 48) {
        document.getElementsByClassName('ant-tree-list-holder')[0].scrollTop =
          document.getElementsByClassName('ant-tree-list-holder')[0].scrollTop + 32;
      }

      if (boxTop + 48 > currentBottom) {
        document.getElementsByClassName('ant-tree-list-holder')[0].scrollTop =
          document.getElementsByClassName('ant-tree-list-holder')[0].scrollTop - 32;
      }
    },
    100,
    [treeRef]
  );

  // 滚动至目标
  const onDragOver = (info) => {
    if (info.event.target) {
      scrollTo(info.event.target);
    }
  };

  // TreeItem点击事件 TODO: 可以统一将onMenuItemClick在当前组件中处理
  function _onTreeItemClick(selectedKeys, info) {
    // 需求：空间或文件夹,父节节点等虚节点可选中，目的：选中状态时，...更多按钮显示，用于显式提醒用户有更多操作
    // 逻辑：左侧树点击只保留一个选中状态，点击虚节点时，右侧数据不刷新，
    // 缺陷：刷新页面后虚节点选中状态不保留，回选实节点选中
    // 手动管理选中节点
    setExpandFlg(false); // 点击事件再次计算展开父节点的逻辑
    onMenuItemClick(selectedKeys, info)
  }

  /* // 响应中树右击新建事件
  function onMiddleTreeCreateBtnClick (target, args){
    onCreateBtnClick(args);
  }
  
  // 响应中树右击更多事件
  function onMiddleTreeMoreBtnClick (target, args){
    onMoreBtnClick(args);
  }

  // 新建
  function onCreateBtnClick ({ nodeItem, nodeType, ...args }) {
    const { rootNode, selectedKeys } = treeRef.current.props
    try {
      objNodeCreateOps({
        nodeType,
        teamId,
        parentNode: {...nodeItem, spaceId},
        args,
        rootNode,
        setRootNode,
        onExpandKeysThrottle,
        treeRef,
        setSelectedKeys,
        queryClient,
        selectedKeys,
        nodeId,
        refetchTeamConfig,
        setSearchRouteParams
      });
    } catch (err) {
      console.log("onCreateBtnClick", err)
    }
  }

  // 更多
  function onMoreBtnClick ({ nodeItem, ctxType, ...args }) {
    const { rootNode, selectedKeys } = treeRef.current.props
    try {
      objNodeMoreOps({
        teamId, objNode: nodeItem, ctxType, args, rootNode, setRootNode, onExpandKeysThrottle, treeRef, selectedKeys, setSelectedKeys, queryClient, nodeId, setSearchRouteParams
      });
    } catch (err) {
      console.log("onMoreBtnClick", err)
    }
  }

  // load current node menu context and show
  const onShowMoreButtonClick = (info) => {
    console.log("onRightClick info", info);
    let _selectedKeys = [...selectedKeys, info.node.nodeId?.toString()];
    setSelectedKeys([..._selectedKeys]);// 右击菜单显示时，选中当前节点
    setContextKey(info.node.nodeId?.toString());
    contextboardRef.current.showContextBoard(info.event.nativeEvent, info.node, info.node.nodeId, true)
  }
  
  // react-contexify 隐藏事件，v6.0版本:废弃onHidden,改用onVisibilityChange属性，参考：https://github.com/fkhadra/react-contexify/pull/220
  function handleOnVisibilityChange(isVisible) {
    console.log('Menu isVisible', isVisible);
    if(!isVisible){  // 右击菜单隐藏时，取消选中当前节点
      let _selectedKeys = selectedKeys.filter(selectedKey => selectedKey != contextKey);
      setSelectedKeys([..._selectedKeys]);
    }
  }
   */

  // 是否允许拖拽时放置在该节点 ({ dropNode, dropPosition }) => boolean
  //  dragNode: 拖拽源 dropNode：拖拽目标 dropPosition:
  const allowDrop = ({ dragNode, dropNode, dropPosition }) => {
    return draggable;
  }

  // 拖拽
  function onDrop(info) {
    const dragNode = info.dragNode; //拖拽
    let destParentObjNodeId = "";
    let destObjNodeId = 0; // 顶级节点传0
    if (info.node.dragOver) {// 嵌套
      if (!getFolderFlg(info.node.nodeType)) {
        globalUtil.warning(`${getNodeNameByNodeType(nodeType)}节点不能有子级`)
        return
      }
      destParentObjNodeId = info.node.nodeId;
    } else {
      destParentObjNodeId = info.node.nodeParentId;
      if (!info.node.dragOverGapTop) {
        destObjNodeId = info.node.nodeId
      }
    }
    let request = {
      srcObjNodeId: dragNode.nodeId, // 拖拽Id
      destObjNodeId,        //目标Id： 0代表成为destParentObjNodeId的第一个子节点
      destParentObjNodeId,  //目标Id父节点Id
    }
    console.log("request", request);
    moveObjNode(request)
  }

  // 将某个节点拖拽到另一个节点下
  const moveObjNode = (request) => {
    let params = {
      teamId: teamId,
      ...request
    };
    http.team_017_move_obj_node(params).then((res) => {
      if (res.resultCode === 200) {
        refetchTreeData(); // useQuery刷新
        // setRefetchFlg(true);
      }
    })
  }

  // 宽度调整
  const onResize = (event, { element, size, handle }) => {
    // globalEventBus.emit("sidebarWidthChange", "", size.width);
  }

  // 调整宽度 setting-227 save_team_user_config 团队成员配置
  const onResizeStop = (event, { element, size, handle }) => {
    const request = {
      teamId,
      nodeId, //中间数展开节点
      treeWidth: size.width,
      // expandNodeIds:[],
    }
    http.setting_227_save_team_user_tree_width(request).then((res) => {
      if (res.resultCode === 200) {
        // OnSize中调整了
        queryClient.setQueryData([settin228, teamId, nodeId], oldData => {
          oldData.treeWidth = size.width;
          return {
            ...oldData
          }
        })
      }
    })
  }

  const onExpandKeysThrottle = useThrottle(onExpandKeys, 100, [teamId, nodeId, expandedKeys, treeRef]);

  /**
   * @param {true: 直接覆盖, false: 追加 } isCover 
   * @param {*} expandKey 
   */
  // 保存展开节点层级
  function onExpandKeys(isCover, keys) {
    // const { rootNode, expandedKeys } = treeRef.current.props
    let expandNodeIds = isCover ? keys : expandedKeys.concat(keys);
    expandNodeIds = Array.from(new Set(expandNodeIds)).filter(item => item)
    setExpandedKeys(expandNodeIds);
    const request = {
      teamId,
      nodeId,
      // width无需传值
      expandNodeIds,
    }
    // setting-227 save_team_user_config 团队成员配置
    http.setting_227_save_team_user_tree_width(request).then((res) => {
      if (res.resultCode === 200) {
        // 20220930：容忍接口调用失败的情况:byJim
        cacheRef.current.expandedKeys = expandNodeIds; // 保存搜索前的展开节点
        setCacheExpandKeys(expandNodeIds);
      }
    }).catch(e => { });
  }

  // 更新expandNodeIds缓存
  function setCacheExpandKeys(expandNodeIds) {
    queryClient.setQueryData([settin228, teamId, nodeId], oldData => {
      return {
        ...oldData,
        expandNodeIds: [...expandNodeIds],
      }
    })
  }

 /*  // 时间限制为1s
  const objNodeCreateOpsFuncThrottle = useThrottle(objNodeCreateOpsFunc, 1000, [onExpandKeysThrottle,]); 

  // 新建顶级节点资源类型、新建顶级节点文件夹
  function objNodeCreateOpsFunc (nodeType) {
    objNodeCreateOps({
      nodeType: nodeType,
      teamId,
      parentNode: { key: nodeId, nodeId, spaceId },
      args: { createType: eCreateType.eSider}, // 顶级文件夹
      rootNode,
      setRootNode,
      onExpandKeysThrottle,
      treeRef,
      setSelectedKeys,
      queryClient,
      selectedKeys,
      nodeId,
      refetchTreeData,
      refetchTeamConfig,
      // searchKey,
      setSearchRouteParams,
    });
  }

  // 图标
  const getCreateBtnView = (iconNodeType) => {
    const iconObj = eNodeTypeCreateIcon[iconNodeType];
    if (!!iconObj) {
      return <span className={ `iconfont ${iconObj.icon} fontsize-18`} style={{ color: iconObj.iconColor }} />
    }
  } */

  
  // 全部收起/展开
  const collapseUnfoldChange = () => {
    if(allPackUpFlag) {
      onExpandKeysThrottle(true, []);
    } else {
      onExpandKeysThrottle(true, treeKeyList.map(treeKey=> treeKey.key?.toString()));
    }
  }

  return (
    <>
      <div className="common-sider flex-column-parent" style={{height: "100%"}}>
        <CustomSideBar width={width} onResize={onResize} onResizeStop={onResizeStop} theme="light">
          {loading && <TLoading />}
          {
            !loading && <>
              <div className="common-sider-toolbar">
                <div className="common-sider-toolbar-search">
                  <InputSearch
                    placeholder="在目录中搜索"
                    bordered={false} 
                    prefix={<SearchOutlined />}
                    onChange={onSearchTextChangedDebounce} // 防抖
                    allowClear
                    maxLength={eInputMaxLength.twenty}
                  />
                </div>
                <div className="common-sider-toolbar-create">
                  <Button title={allPackUpFlag ? "全部收起" : "全部展开"} className="allPackUp-allDevelop" type="text">
                    <KeyboardDoubleArrowDownIcon 
                      onClick={collapseUnfoldChange} 
                      sx={{ cursor: 'pointer', transform: allPackUpFlag ? 'rotate(180deg)' : '' }} className="fontsize-18"
                    />
                  </Button>   
                </div> 
                {/* <div className="common-sider-toolbar-create">
                  <Button
                    type="text"
                    icon={getCreateBtnView(eNodeTypeId.nt_302_objtype_folder)}
                    onClick={() => { objNodeCreateOpsFuncThrottle(eNodeTypeId.nt_302_objtype_folder) }}
                  />
                  {
                    // 有图标才显示
                    getCreateBtnView(nodeType) && <Button
                      type="text"
                      icon={getCreateBtnView(nodeType)}
                      onClick={() => { objNodeCreateOpsFuncThrottle(nodeType) }}
                    />
                  }
                </div> */}
              </div>
              {/* 不要使用DirectoryTree: defaultExpandParent为false不起作用，子节点展开，父节点也会默认展开*/}
              {
                rootNode && !isEmpty(rootNode.children) ? <Tree
                  ref={treeRef}
                  height={listHeight} // 虚拟滚动开启，拖拽速度会快些
                  virtual
                  blockNode
                  draggable={{ icon: false, nodeDraggable: ()=>(draggable) }} // 是否可拖拽
                  selectedKeys={selectedKeys}
                  multiple={true} // 设置为多选
                  expandedKeys={expandedKeys}
                  defaultExpandParent={false}
                  autoExpandParent={autoExpandParent}
                  onExpand={onExpand}
                  icon={(props) => props.data.icon || ""}
                  showIcon={true} //显示自定义icon
                  switcherIcon={
                    <span className="tree-dir-color iconfont youjiantou_huaban" />
                  }
                  titleRender={nodeData => 
                    (<TreeItem 
                      selectedKeys={selectedKeys} 
                      teamId={teamId} 
                      nodeData={nodeData} 
                      // onShowMoreButtonClick={onShowMoreButtonClick} 
                      // onMoreBtnClick={onMoreBtnClick} 
                      searchValue={searchValue} 
                      sysIconList={sysIconList}
                    />)
                  }
                  treeData={!!searchValue ? treeDataForSearch : rootNode?.children || []}
                  className={`flex-column-child section common-sider-tree ${!!searchValue ? " common-sider-search" : ""}`}
                  onSelect={_onTreeItemClick}
                  onDoubleClick={!!searchValue ? (onDoubleTreeNodeClick ? onDoubleTreeNodeClick : () => { }) : () => { }}
                  // onRightClick={onShowMoreButtonClick} // 右击菜单
                  onDrop={draggable ? onDrop : ()=>{}}
                  onDragOver={draggable ? onDragOver : ()=>{} }
                  allowDrop={allowDrop}
                  rootNode={rootNode}
                />
                  :
                  <div className="blank-page">
                    <div className="blank-page-title">这里是空的</div>
                    {/* <div className="blank-page-des fontsize-12 flexCenter">
                      <span style={{ paddingRight: 5 }}>你可以</span>
                      <a onClick={(e) => objNodeCreateOpsFuncThrottle(eNodeTypeId.nt_302_objtype_folder)} className="flexCenter">
                        {getCreateBtnView(eNodeTypeId.nt_302_objtype_folder)}{eNodeTypeCreateIcon[eNodeTypeId.nt_302_objtype_folder].name}
                      </a>
                      {
                        getCreateBtnView(nodeType) && <>
                          <span style={{ padding: "0px 5px" }}>或</span>
                          <a onClick={(e) => objNodeCreateOpsFuncThrottle(nodeType)} className="flexCenter">
                            {getCreateBtnView(nodeType)}{eNodeTypeCreateIcon[nodeType].name}
                          </a>
                        </>
                      }
                    </div> */}
                  </div>
              }
            </>
          }
        </CustomSideBar>
      </div>
      {/* right click context */}
      {/* <ContextBoard 
        ref={contextboardRef} 
        teamId={teamId} 
        onMoreBtnClick={onMoreBtnClick} 
        onCreateBtnClick={onCreateBtnClick} 
        id={"dashboard-tree-context-menu"} 
        handleOnVisibilityChange={handleOnVisibilityChange}
      /> */}
    </>
  );
}

export default memo(CommonTreeSider);