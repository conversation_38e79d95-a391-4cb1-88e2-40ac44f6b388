.price {
    height: 100vh;
    scroll-behavior: smooth;
}

.TopMenuBar-box-price {
    position: fixed;
    top: 0;
    margin: auto;
    padding: 0 20px;
    z-index: 1;
    background-color: white;
    box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
    width: 100vw;
    left: 0;
}

// 选择最合适的方案
.price-programme {
    padding: 50px 100px;
    margin: auto;
    min-width: 1200px;
    &-top {
        text-align: center;
    }
    .price-programme-select-text {
        position: absolute;
        top: 0;
        left: 0;
        padding: 4px 8px;
        display: inline;
        font-size: 12px;
        color: white;
        background-color: #ff6b1a;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
    }
    &-content {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        align-items: stretch;
        &-li {
            margin-left: 100px;
            width: 360px;
            // min-height: 560px;
            overflow: hidden;
            border-radius: 5px;
            // box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 2%);
            box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
            .price-programme-select {
                height: 10px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                background-color: #0077f2;
            }
            .price-programme-select-false {
                height: 10px;
            }
            &-heard {
                position: relative;
                text-align: center;
                padding: 12px;
                background-color: #f2f2f2;
                &-name {
                    font-size: 20px;
                    font-weight: bold;
                }
                &-title {
                    margin-top: 4px;
                    font-size: 14px;
                }
            }
            .major-heard {
                background-color: #f2f7fd;
            }
            .enterprise-heard {
                background-color: #f8f2fe;
            }
            &-body {
                padding: 20px;
                .package-description {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .free-version-buy {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin: 10px 0;
                    width: 100%;
                    background-color: white;
                    border: 1px solid #0077f2;
                    font-size: 14px;
                    font-weight: bold;
                    &-money {
                        color: #333;
                    }
                    &-a {
                        margin-left: 20px;
                    }
                }
                .free-version-contact-us {
                    margin: 10px 0;
                    width: 100%;
                    color: white;
                    background-color: #0077f2;
                    border: 1px solid #0077f2;
                    font-size: 14px;
                    font-weight: bold;
                }
                .include-functions {
                    &-title {
                        font-size: 16px;
                        font-weight: 600;
                    }
                    &-content {
                        &-li {
                            margin-top: 14px;
                            white-space: pre-wrap;
                            font-size: 14px;
                        }
                    }
                }
            }
        }
        &-li:first-child {
            margin: 0;
        }
    }
}

// 完整方案对比
.complete-scheme {
    padding: 50px 100px;
    text-align: center;
    .complete-scheme-table {
        margin: auto;
        width: 1260px;
    }
    .complete-scheme-table-head {
        display: flex;
        align-items: center;
        margin: 20px 0 6px;
        .complete-scheme-table-head-li {
            width: 25%;
            .MuiButtonBase-root {
                margin-top: 6px;
                width: 120px;
            }
        }
    }
}

// 价格计算器
.price-calculation {
    padding: 50px 100px;
    margin: auto;
    min-width: 1200px;
    background: url('../../assets/images/price_bg.png') no-repeat fixed top left;
    background-size: 100% 100%;
    &-top {
        text-align: center;
        color: white;
    }
    &-content {
        margin: 20px auto 0;
        padding: 20px;
        width: 1500px;
        background-color: white;
        border-radius: 10px;
    }
}

// 购买常见问题
.common-problem {
    padding: 40px 100px;
    margin: auto;
    min-width: 1200px;
    &-content {
        margin: auto;
        min-width: 600px;
        max-width: 1000px;
        &-list {
            padding: 0 !important;
            margin-top: 40px !important;
            .MuiButtonBase-root {
                padding: 8px 10px;
                border-bottom: 1px solid #f2f2f2;
                .MuiListItemText-root {
                    .MuiTypography-root {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font-size: 16px;
                        font-weight: bold;
                    }
                }
                .MuiListItemIcon-root {
                    margin-left: 60px;
                    min-width: auto;
                }
            }
        }
    }
}

// 完整方案对比表格样式
.CompleteSchemeTable-head {
    .CompleteSchemeTable-head-row {
        .MuiTableCell-root {
            padding: 6px 20px;
            background: #f2f2f2;
            border: 0;
        }
    }
}

.CompleteSchemeTableRow-row {
    .MuiTableCell-root {
        padding: 6px 20px;
        width: 25%;
    }
    .MuiTableCell-root:first-child {
        border: 0;
    }
    .MuiTableCell-root:nth-child(n+2) {
        border-left: 1px solid #f2f2f2;
        border-color: #f2f2f2;
    }
}
.CompleteSchemeTableRow-row:hover {
    background-color: #f3f9fe;
}

.CompleteSchemeTableRow-row-table {
    .MuiTableCell-root {
        padding: 0;
        border-color: #f2f2f2;
    }
    .CompleteSchemeTableRow-childNode-table {
        .MuiTableBody-root {
            .MuiTableRow-root {
                .MuiTableCell-root {
                    padding: 6px 20px;
                    width: 25%;
                }
                .MuiTableCell-root:nth-child(n+2) {
                    border-left: 1px solid #f2f2f2;
                    border-color: #f2f2f2;
                }
                .MuiTableCell-root:first-child {
                    padding-left: 50px;
                    border: 0;
                }
            }
            .MuiTableRow-root:hover {
                background-color: #f3f9fe;
            }
            .MuiTableRow-root:last-child {
                .MuiTableCell-root {
                    border-bottom: 0;
                }
            }
        }
    }
}

// 完整方案-行名称样式
.CompleteSchemeTableRow-value {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .table-row-left {
        display: flex;
        align-items: center;
        .table-row-tags {
            margin-left: 4px;
            padding: 0 4px;
            font-size: 12px;
            border-radius: 4px;
        }
    }
    .table-row-right {
        font-size: 12px;
        color: #666;
    }
}