/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2023-04-12 16:04:21
 * @LastEditors: Walt <EMAIL>
 * @LastEditTime: 2023-04-12 16:04:33
 * @Description: 请填写文件内容描述！
 */
import moment from "moment";


/// 目前只配置这些方法，如果有其它需要再去添加
/// 可以使用 DateUtil().moment 去使用
export default class DateUtil {

  constructor(date){
    this.moment = date? moment(date,""): moment();
  }

  // 格式化时间
  format(format){
    return this.moment.format(format)
  }

  // 获取当前时间并格式化
  getNow(format){
    return this.moment.format(format)
  }

  /** 
   * 日期计算，增加或减少
   * @param days num 增加或减少的天数
   * @param type 需要增加的类型 years months weeks days hours minutes seconds milliseconds
   */
  add(days,type){
    return this.moment.add(days, type)
  }

  /** 
   * 两日期相差的天数
   * @param date num 增加或减少的天数
   * @param type 需要增加的类型 years months weeks days hours minutes seconds milliseconds
   * @returns number
   */
  diff(date,type){
    return this.moment.diff(date,type)
  }

  // 转化为日期格式
  toDate(){
    return this.moment.toDate()
  }

  isBefore(date){
    return this.moment.isBefore(date)
  }

  isAfter(date){
    return this.moment.isAfter(date)
  }

  isSame(date){
    return this.moment.isSame(date)
  }

}