$mainTextSize: 12px;
$subTitleSize: 14px;
$mainTitleSize: 16px;

$mainTitle: #333333;
$mainText: #666666;
$otherText: #999999;

$mainColor: #3279fe;
$LightBlue: #f2f5fd;
$mainBordColor: #f2f2f2;

$tipBgColor: #ffe8d5;


$searchColor: #f50;

$greenColor: #70b603;

$keywordsColor: #FFA500;

$themeBlue: #0077f2;    // 主题蓝色
$warnRed: #d9001B;      // 红色
$tipTextColor: #f59a23; // 黄色


/*
  sidebar color
  (id,sidebarBgColor,sidebarTextColor,sidebarHeaderColor,sidebarMenuSelectedBgColor,sidebarMenuSelectedTextColor)
*/
$sidebarcolor-list: (
  ("0",#f6f8f9,#00000099,#f6f8f9,#00905F,#ffffffde,rgba(0, 0, 0, 0.3),#ebeff1),
  ("1",#313541,#fff,#313541,#F4F7FE,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("2",#313541,#fff,#639F52,#639F52,#fff,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("3",#fff,#000000de,#fff,#F4F7FE,#000000de,rgba(0, 0, 0, 0.3),#ebeff1),
  ("4",#fff,#000000de,#fff,#313541,#fff,rgba(0, 0, 0, 0.3),#ebeff1),
  ("5",#fff,#000000de,#fff,#0077F2,#fff,rgba(0, 0, 0, 0.3),#ebeff1),

  ("6",#313541,#fff,#313541,#FD933A,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("7",#079CE9,#fff,#313541,#313541,#fff,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("8",#1B9E85,#fff,#313541,#F4F7FE,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("9",#FD933A,#fff,#313541,#F4F7FE,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("10",#F0464D,#fff,#313541,#F4F7FE,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),

  ("11",#313541,#fff,#313541,#639F52,#fff,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("12",#7C4D30,#fff,#313541,#F4F7FE,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("14",#639F52,#fff,#313541,#F4F7FE,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("15",#5A63C8,#fff,#313541,#F4F7FE,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("16",#9C27B0,#fff,#313541,#F4F7FE,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("17",#673AB7,#fff,#313541,#F4F7FE,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("18",#079CE9,#fff,#313541,#F4F7FE,#000000de,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("19",#1B9E85,#fff,#313541,#313541,#fff,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("20",#FD933A,#fff,#313541,#313541,#fff,rgba(255, 255, 255, 0.5),#ebeff11a),
  ("21",#F0464D,#fff,#313541,#313541,#fff,rgba(255, 255, 255, 0.5),#ebeff11a),

  ("22",#1E222D,#979C9F,#262F3E,#0077f2,#fff,#737C83,#797979),
);


/*
  context color
  ( type, id, color)
*/
$contextcolor-list: (
  (1,"7BBE02",#7BBE02,),
  (2,"FFB228",#FFB228,),
  (3,"FF6347",#FF6347,),
  (4,"72D6C9",#72D6C9,),
  (5,"3799FF",#3799FF,),
  (6,"B39DDB",#B39DDB,),
  (7,"D24646",#D24646,),
  (8,"E17D3F",#E17D3F,),
  (9,"649B1E",#649B1E,),
  (10,"666666",#666666,),
);