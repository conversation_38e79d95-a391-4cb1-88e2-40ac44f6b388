@import "~highlight.js/scss/default.scss";

@font-face {
  font-family: "fileiconfont"; /* Project id  */
  src: url('./font/fileiconfont.ttf?t=1679032137321') format('truetype');
}

// 附件图标
.fr-view .fr-file:after {
  font-family: "fileiconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e88a";
  font-weight: 400;
  position: relative;
}


.fr-buttons.codeLanguage-pop {
  padding: 0;
  position: relative;
  height: 40px;
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  [data-cmd=insertPreEnter]{
    height: 28px;
    position: relative;
  }

  [data-cmd=insertEnter]{
    height: 28px;
    position: relative;
  }

  .fr-separator.fr-vs {
    height: 20px
  }

  [data-cmd=codeLanguage] {
    height: 28px;
  }

  .fr-popup .fr-buttons .fr-btn {
    margin: 0;
  }

}

.fr-view {
  position: relative;
  img.fr-bordered{
    border: solid 1px #333
  }
}

.fr-view h1 {
  font-size: 24px;
}

.fr-view ul[data-list-style=default]{
  list-style: disc;
}

.fr-view ol[data-list-style=default]{
  list-style: auto;
}

.fr-view ul>li,.fr-view ol>li {
  list-style: inherit;
}

// 编辑器引用级别样式
.fr-view blockquote { // 第一级
  border-left: solid 2px #bbb;
  margin-left: 0;
  padding-left: 5px;
  color: #bbb;
}
.fr-view blockquote p { // 第一级
  color: #bbb !important;
}

.fr-view blockquote blockquote { // 第二级
  border-left: solid 2px #ccc;
  margin-left: 0;
  padding-left: 5px;
  color: #ccc;
}
.fr-view blockquote blockquote p { // 第二级
  color: #ccc !important;
}

.fr-view blockquote blockquote blockquote { // 第三级及以上
  border-left: solid 2px #ddd;
  margin-left: 0;
  padding-left: 5px;
  color: #ddd;
}
.fr-view blockquote blockquote blockquote p { // 第三级及以上
  color: #ddd !important;
}

.fr-editor-preview,.teditor .fr-view {
  ol {
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 40px;
  }
  ul {
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 40px;
  }
}

.fr-editor-preview{
  overflow: auto;
}

// .fr-view,.teditor{
//   pre{
//     background: #f5f5f5;
//     color: #161616;
//   }

//   // pre::before {
//   //   content: "plaintext<span class>";
//   //   line-height: 2.5em;
//   //   position: absolute;
//   //   text-align: right;
//   //   right: 15px;
//   //   top: 0px;
//   // }

//   pre code{
//     background: #ffffff!important;
//     display: block;
//     border: 5px solid #f5f5f5;
//   }
//   .syntax-control-wrapper{
//     display: block;
//     height: 37px;
//   }
//   .syntax-control-wrap {
//     float: right;
//     white-space: nowrap;
//     margin: 7px 10px 7px 0;
//     display: flex; 
//     align-items: center;
//   }
//   .syntax-control-label{
//     color: #5c6061;
//     margin: 0 4px 0 0;
//     font-size: 13px;
//     display: inline-block;
//     vertical-align: middle;
//   }
//   .syntax-control-btn{
//     cursor: pointer;
//     padding-left: 4px;
//     padding-right: 4px;
//     min-height: 22px;
//     display: inline-flex;
//     align-items: center;
//   }

// }

.fr-view p {
  margin: 0px 0px 5px 0px;
  padding: 0;
}

// .fr-toolbar .fr-command.fr-btn, .fr-popup .fr-command.fr-btn, .fr-modal .fr-command.fr-btn{
//   height: auto;
// }

.fr-toolbar .fr-command.fr-btn svg.fr-svg, .fr-popup .fr-command.fr-btn svg.fr-svg, .fr-modal .fr-command.fr-btn svg.fr-svg{
  height: 18px;
}
.fr-toolbar .fr-command.fr-btn i, .fr-toolbar .fr-command.fr-btn svg, .fr-popup .fr-command.fr-btn i, .fr-popup .fr-command.fr-btn svg, .fr-modal .fr-command.fr-btn i, .fr-modal .fr-command.fr-btn svg {
  width: 18px;
}

.teditor {

  .fr-toolbar.fr-top{
    border-radius: 0;
    border: 1px solid #efefef;
    border-bottom: none;
  }

  .fr-box.fr-basic .fr-wrapper{
    border: 1px solid #efefef;
  }

  .fr-second-toolbar:empty{
    border-radius: 0;
    border: none;
    display: none;
  }
}

.fr-popup.fr-desktop{
  z-index: 10001!important;
}

.fr-toolbar.fr-desktop.fr-top>.fr-btn-grp.fr-float-left {
  margin: 0;
}

// 预览
.fr-view {
  font-size: 14px;
  // 代码块
  pre {
    position: relative;
  }
  pre>.copy-code-btn:hover {
    color: #8c8c8c;
  }
  pre>.copy-code-btn {
    position: absolute;
    top: 6px;
    right: 15px;
    font-size: 12px;
    line-height: 1;
    cursor: pointer;
    color: hsla(0,0%,54.9%,.8);
    transition: color .1s;
  }

  pre>code.hljs[lang]:before {
    content: attr(lang);
    position: absolute;
    // right: 15px;
    right: 70px;
    top: 2px;
    color: hsla(0,0%,54.9%,.8);
  }
}



