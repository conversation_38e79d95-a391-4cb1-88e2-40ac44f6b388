import { 
  team_504_get_comment_list,
  team_530_get_obj_node_info,
  team_553_get_front_query,
  team_619_user_info,
  team_1201_get_menu_list,
  setting_228_get_team_user_tree_width,
  team_628_get_login_register_method,
  team_1503_get_os_help_list
} from '../http';

export const team_504_get_comment_list_query = (teamId, nodeId) => ({
  queryKey: ['team_504_get_comment_list', teamId, nodeId],
  queryFn: async () => team_504_get_comment_list({ teamId, nodeId })
})

// team_530 get_obj_node_info获取某个节点详情
export const team_530_get_obj_node_info_query = (teamId, objNodeId) => ({
  queryKey:  ["team_530_get_obj_node_info", teamId, objNodeId],
  queryFn: async () => {
    let res = await team_530_get_obj_node_info({ teamId, objNodeId })
    if (res.resultCode == 200) {
      return {...res, key: res.id, nodeId: res.id, label: res.name, nodeName: res.name};
    }
  }
})

// 获取query查询条件
export const team_553_get_front_query_query = (teamId, queryId) => ({
  queryKey: ['team_553_get_front_query', teamId, queryId],
  queryFn: async () => team_553_get_front_query({ teamId, queryId })
})

// 获取头部导航和底部链接数据
export const team_1201_get_menu_list_query = (teamId) => ({
  queryKey: ['team_1201_get_menu_list', teamId],
  queryFn: async () => team_1201_get_menu_list({ teamId }),
  cacheTime: Infinity
})

export const team_619_user_info_query = () => ({
  queryKey: ['team_619_user_info'],
  queryFn: async () => team_619_user_info(),
  cacheTime: Infinity
})

// setting-228 get_team_user_config 获取团队成员配置信息
export const setting_228_get_team_user_tree_width_query = (teamId, nodeId) => ({
  queryKey: ['setting_228_get_team_user_tree_width', teamId, nodeId],
  queryFn: async () => setting_228_get_team_user_tree_width({ teamId, nodeId })
})

// 获取操作系统登录方式
export const team_628_get_login_register_method_query = () => ({
  queryKey: ['team_628_get_login_register_method'],
  queryFn: async () => {
    let result = await team_628_get_login_register_method()
    return result
  },
})

// 获取帮助文档字典
export const team_1503_get_os_help_list_query = (teamId) => ({
  queryKey: ["team_1503_get_os_help_list", teamId],
  queryFn: async () => {
    let result = await team_1503_get_os_help_list({teamId})
    return result
  },
  cacheTime: Infinity,
})