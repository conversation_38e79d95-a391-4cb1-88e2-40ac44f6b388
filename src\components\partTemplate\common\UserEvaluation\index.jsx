import React, { useEffect, useState, useRef } from "react";
import { Carousel, Button } from 'antd';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import {
  Box,
  Avatar,
} from "@mui/material";
import FormatQuoteIcon from "@mui/icons-material/FormatQuote";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../../utils/commonUtils";

//用户评价
export default function UserEvaluation({data}) {
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData])
    }
  },[data?.widgetList])
  return (
    <Box className="user-evaluation" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
      {!!filterWidgetList(moduleData, 1)[0]?.value && <Box className="user-evaluation-title">{filterWidgetList(moduleData, 1)[0]?.value}</Box>}
      <HomeCarousel moduleData={moduleData}/>
    </Box>
  );
}

// ant轮播
function HomeCarousel({moduleData}) {
  const carouselRef = useRef()
  const [carouselNum, setCarouselNum] = useState(0);

  const carouselAfter = (current) => {
    setCarouselNum(current)
  }

  const outBtnChange = (type) => {
    if(type === 'left') {
      carouselRef.current.prev()
    } else {
      carouselRef.current.next()
    }
  }
  return (
    filterWidgetList(moduleData, 6).length > 0 ? <div className="HomeCarousel">
      <ArrowLeftOutlined className="HomeCarousel-btn" onClick={() => outBtnChange('left')}/>
      <Carousel 
      className="HomeCarousel-Carousel" 
      ref={carouselRef}
      autoplay 
      dots={false} 
      autoplaySpeed={3000}
      afterChange={carouselAfter} >
        {filterWidgetList(moduleData, 6).map((step, index) => (
          <div className="HomeCarousel-Carousel-li" key={step.key}>
            <FormatQuoteIcon className="HomeCarousel-Carousel-li-icon"/>
            <Box className="HomeCarousel-Carousel-li-text">
              {filterWidgetList(step.children, 7)[0]?.value}
            </Box>
            <Box sx={{ textAlign: 'right' }}>
              <FormatQuoteIcon className="HomeCarousel-Carousel-li-icon-ending"/>
            </Box>
            <Box className="HomeCarousel-Carousel-li-avatar">
              <Avatar src={filterWidgetList(step.children, 3)[0]?.value} sx={{ backgroundColor: "red" }}/>
              <Box className="HomeCarousel-Carousel-li-avatar-name">
                <span>{filterWidgetList(step.children, 1)[0]?.value}</span>
                <span>{filterWidgetList(step.children, 2)[0]?.value}</span>
              </Box>
            </Box>
          </div>
        ))}
      </Carousel>
      <ArrowRightOutlined className="HomeCarousel-btn" onClick={() => outBtnChange('right')}/>
    </div>
    : <></>
  )
}