/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2023-05-10 13:38:35
 * @LastEditors: Walt <EMAIL>
 * @LastEditTime: 2023-05-10 13:38:42
 * @Description: 请填写文件内容描述！
 */
import {isArray} from "@/utils/ArrayUtils";

// 支持对象形式的tree嵌套
const treeOp = {
    root: null,
    findByKey: function(node,key) {
        if(node.key == key)
            return node;
        for(var i=0;i<(node.children||[]).length;i++) {
            var cnode = this.findByKey(node.children[i],key);
            if(cnode)
                return cnode;
        }
    },
    findParent: function(node,key, parentNode=null) {
        if(node.key == key)
            return [node,parentNode];
        for(var i=0; i<(node.children||[]).length;i++) {
            var cnode = this.findParent(node.children[i],key,node);
            if(cnode)
                return cnode;
        }
    },
    findParentNodes: function(rootNode, nodeId, includeOwn = false) { //查找所有父节点, includeOwn是否包含自身，默认不包含
      for (let i in rootNode) {
        if (rootNode[i].nodeId == nodeId) {
          return includeOwn ? [rootNode[i]] : [];  // 是否返回自身节点信息
        }
    
        if (rootNode[i].children) {
          let node = this.findParentNodes(rootNode[i].children, nodeId, includeOwn);
          if (node !== undefined) {
            return node.concat(rootNode[i]);  //查询到把父节点连起来
          }
        }
      }
    },
    addChild: function(parentKey,childNode) { //新建子节点
        let parent = this.findByKey(this.root,parentKey);
        if(parent) {
            parent.children = (parent.children || []);
            // 插入数组则合并，对象则添加
            isArray(childNode) ? parent.children.push.apply(parent.children, childNode) : parent.children.push(childNode); 
        }
    },
    updateChild: function(parentKey,childNode) { //更新子节点
      let parent = this.findByKey(this.root,parentKey);
        if(parent) {
            parent.children = childNode || [];
        }
    },
    addSibling: function(newNode, siblingKey) { //将node作为afterSiblingNode的兄弟节点，插在afterSiblingNode的后面
        let [,parent] = this.findParent(this.root, siblingKey) || [];
        if(parent) {
            let idx = parent.children.findIndex(c=>c.key==siblingKey);
            parent.children.splice(idx+1,0,newNode);
        } else {
          console.error("findParent返回了undefined,undefined的数据和id为:", [this.root, siblingKey]);
        }
        return parent;
    },
    // 新建一个空间/子空间，是需要放在 空间这个列表的兄弟节点的最后 (但会在资源类别，如文件夹，文档等等节点的前面)
    addLastChildByNodeType: function(newNode,parentKey) { //加在相同子节点类型(childNodeType)的兄弟节点的最后
        let parent = this.findByKey(this.root,parentKey);
        parent.children = parent.children || [];
        let idx = parent.children.findLastIndex(_node => _node.nodeType == newNode.nodeType);
        parent.children.splice(idx+1,0,newNode);
    },
    deleteByKey: function(keyToDelete) { //移除节点
        // TODO: this.findParent 返回了 undefined
        let [,parent] = this.findParent(this.root, keyToDelete) || [];
        if(parent) {
            let idx = parent.children.findIndex(c=>c.key == keyToDelete);
            parent.children.splice(idx,1); //删除1个element
        } else {
          console.error("findParent返回了undefined,undefined的数据和id为:", [this.root, keyToDelete]);
        }
        return parent;
    },
    deleteChildren: function(keyToDelete) { //移除子节点
      let node = this.findByKey(this.root,keyToDelete);
      if(node) {
       node.children = [];
      }
      return node;
    },
    updateNode: function(updatedNode) {  //根据key更新节点(包含子节点children)
        let nodeForKey = this.findByKey(this.root,updatedNode.key);
        let {key, ...restUpdated} = updatedNode;
        Object.keys(restUpdated).forEach(prop => nodeForKey[prop] = updatedNode[prop]);
    },
    move: function(keyDrag, siblingKeyDrop) { //移动子节点, 将keyDrag 放置到 siblingKeyDrop的后面(兄弟节点)
        let nodeDrag = this.findByKey(this.root,keyDrag);
        if(nodeDrag) {
            this.deleteByKey(nodeDrag.key);
            this.addSibling(nodeDrag, siblingKeyDrop);
        }
    },
    // 子孙节点
    isChildrenNode: function(rootNode, parentKey, childkey) { //判断是否是子节点
      try {
        let parentKeys = this.findParentNodes(rootNode?.children, childkey);
        return (parentKeys || []).some(item => item.key == parentKey);
      } catch (e) {
        console.error("isChildNode报错了", e);
      }
    },
    // 子节点
    isChildNode: function(rootNode, parentKey, childkey) { //判断是否是子节点
      try {
        let [,parent] = this.findParent(this.root, childkey) || [];
        return parent.key == parentKey;
      } catch (e) {
        console.error("isChildNode报错了", e);
      }
    },
    // 创建副本，节点放置在同级节点的最后
    addLastChild: function(newNode, key) { 
      let [,parent] = this.findParent(this.root, key) || [];
      this.addChild(parent.key, newNode);
    },
};

export default treeOp;