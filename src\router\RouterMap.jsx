import React from 'react'
import { Outlet } from 'react-router-dom'
import {Refresh} from "./RouteRefresh";
import AppErrorElement from "../components/AppErrorElement"

function RenderRoutes () {
    return <Outlet/>
}

function getSerializeRoutes(routes){
    return routes.map((route,index)=>{
        let _path = `${route.path}`; 
        /* 
        path?: string;
        index?: boolean;
        children?: React.ReactNode;
        caseSensitive?: boolean;
        id?: string;
        loader?: LoaderFunction;
        action?: ActionFunction;
        element?: React.ReactNode | null;
        errorElement?: React.ReactNode | null;
        handle?: RouteObject["handle"];
        shouldRevalidate?: ShouldRevalidateFunction;
        */
        return {
            path: _path,
            index: route?.index,
            children: getSerializeRoutes(route.children || []),
            caseSensitive: route?.caseSensitive,
            loader: (info) => info, // route?.loader,
            action: route?.action,
            element: <Refresh key={_path} skeleton={route.skeleton} route={route} pageloader={route?.loader}/>,
            errorElement: route?.errorElement || <AppErrorElement/>,
            handle: route?.handle,
            shouldRevalidate: route?.shouldRevalidate
        }
    })
}

function getRouters(routes){
    return getSerializeRoutes(routes)
}


export { RenderRoutes, getRouters }