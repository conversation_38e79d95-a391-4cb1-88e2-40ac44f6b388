.tree-dir-flex {
  display: flex;
  align-items: center;

  .tree-dir-more-btn {
    display: none;
  }

  .tree-dir-more-action {
    display: inline-flex;
    align-items: center;
    min-width: 32px;
    height: 28px;
    box-sizing: border-box;
    justify-content: center;
    vertical-align: middle;
  }

  &:hover .tree-dir-more-action {
    .tree-dir-more-btn {
      display: inline-block;
    }
  }
  
  .iconfont {
    font-size: 14px !important;
    line-height: 14px !important;
  }

 /*  // 限制图标大小，防止快捷方式错位
  .tree-dir-icon {
    width: 14px;
    height: 14px;
    line-height: 14px;
  } */

  .tree-title-icon {
    width: 16px;
    height: 16px;
    line-height: 16px;
  } 

  // 加粗
  .tree-dir-title-bold {
    font-weight: bold;
    font-size: 16px;
  }
}

