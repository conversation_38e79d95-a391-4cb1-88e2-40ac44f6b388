import React, { useEffect, useState } from "react";
import { Image } from "antd";
import { Box, Typography, Stack } from "@mui/material";
import "./Cust_Part_4_Full_Customization.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../utils/commonUtils";

export default function Cust_Part_4_Full_Customization(props) {
  const { data } = props;
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData]);
    }
  }, [data?.widgetList]);

  return (
    <>
      {/* 深度定制-start */}
      <Box className="deep-customization" style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}>
        <Box className="deep-customization-header">
          {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography variant="h5" sx={{ fontSize: "34px", color: 'white' }}>{filterWidgetList(moduleData, 1)[0]?.value}</Typography>}
          {!!filterWidgetList(moduleData, 2)[0]?.value && <Typography sx={{ mt: "10px" }} variant="body2">
            {filterWidgetList(moduleData, 2)[0]?.value}
          </Typography>}
        </Box>
        <Box className="deep-customization-content">
          <Stack
          sx={{ flexWrap: "wrap", justifyContent: "center", mt: "-100px" }}
          direction={{ xs: "column", sm: "row" }}
          spacing={4}
          >
            {filterWidgetList(moduleData, 6).map((item, index) => (
              <Box key={item.widgetId} className="deep-customization-content-li">
                <Box>
                  <Typography variant="h6">{filterWidgetList((item?.children || []), 1)[0]?.value}</Typography>
                  <Typography className="fontsize-12" sx={{ mt: "10px", color: '#999' }} variant="body2">
                    {filterWidgetList((item?.children || []), 2)[0]?.value}
                  </Typography>
                </Box>
                <Box className="ability-stack">
                  <Stack
                  sx={{ flexWrap: "wrap", justifyContent: "space-between", mt: "40px" }}
                  direction={{ xs: "column", sm: "row" }}
                  // spacing={2}
                  >
                    {filterWidgetList((item?.children || []), 6).map((item1, indx1) => (
                      Array.isArray(item1?.children) && 
                      <Box key={item1.widgetId} className="ability-stack-li">
                        <Image preview={false} className="ability-stack-li-icon" width={30} height={30} src={filterWidgetList((item1.children || []), 3)[0]?.value}/>
                        <Typography className="ability-stack-li-text" variant="body2">{filterWidgetList((item1.children || []), 1)[0]?.value}</Typography>
                      </Box>
                    ))}
                  </Stack>
                </Box>
              </Box>
            ))}
          </Stack>
        </Box>
      </Box>
      {/* 深度定制-end */}
    </>
  );
}
