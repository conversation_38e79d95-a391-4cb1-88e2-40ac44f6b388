import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Tab, Tabs, Typography } from "@mui/material";
import { Collapse, Button, Image } from "antd";
import { DownOutlined } from "@ant-design/icons";
import "./index.scss";
import { isArrayUtils, filterWidgetList, formatTreeData, portalTextWrapHtml } from "../../utils/commonUtils";

const { Panel } = Collapse;

// 功能简介-手风琴模板
export default function FunctionIntroductionAccordion({data}) {
  const navigate = useNavigate();
  const [snapshotValue, setSnapshotValue] = useState();  // 默认选中
  const [moduleData, setModuleData] = useState([]);
  const [currentModuleItem, setCurrentModuleItem] = useState([]);

  useEffect(() => {
    if(isArrayUtils(data?.widgetList)) { 
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
      setSnapshotValue(filterWidgetList(formatData, 5)[0]?.widgetId)
      setCurrentModuleItem([...filterWidgetList(formatData, 5)[0]?.children || []])
    }
  },[data?.widgetList])

  useEffect(() => {
    if(snapshotValue) {
      let arr_ = filterWidgetList(moduleData, 5).filter(el => el.widgetId === snapshotValue)[0]?.children || []
      setCurrentModuleItem([...arr_])
    }
  },[snapshotValue])

  const snapshotValueChange = (e, value) => {
    setSnapshotValue(value)
  }

  // 获取模板排列位置
  const getDemoPosition = (type) => {
    let className_ = "";
    switch (type) {
      case "left":
      case "right":
        className_ = "FunctionIntroductionAccordion-" + type
        break;    
      default:
        break;
    }
    return className_
  }

  return (
    <div 
      className={[
        "FunctionIntroductionAccordion", 
        getDemoPosition(filterWidgetList(moduleData, 11)[0]?.value)
      ].join(" ")}
      style={{ background: filterWidgetList(moduleData, 12)[0]?.value || "transparent" }}
    >
      {!!filterWidgetList(moduleData, 1)[0]?.value && <Typography className="FunctionIntroductionAccordion-title" variant="h5">
        {filterWidgetList(moduleData, 1)[0]?.value}
      </Typography>}
      <div className="FunctionIntroductionAccordion-content">
        <Tabs
          className="FunctionIntroductionAccordion-content-tabs tms-portal-tabs"
          value={snapshotValue}
          centered
          onChange={snapshotValueChange}
        >
          {(filterWidgetList(moduleData, 5) || []).map((item, index) => (
            <Tab key={item.widgetId} value={item.widgetId} label={item.value} />
          ))}
        </Tabs>
        {}
        <div className="FunctionIntroductionAccordion-content-tabsBody">
          {filterWidgetList(currentModuleItem, 6).length > 0 && <div className="FunctionIntroductionAccordion-content-tabsBody-left">
            <Collapse 
              accordion 
              bordered={false} 
              expandIconPosition="end"
              expandIcon={({ isActive }) => <DownOutlined style={{ color: '#0077f2' }} rotate={isActive ? -180 : 0}/>}
              className="FunctionIntroductionAccordion-content-tabsBody-left-Collapse"
            >
              {filterWidgetList(currentModuleItem, 6).map((item, index) => (
                <Panel header={filterWidgetList((item?.children || []), 1)[0]?.value} key={item.widgetId}>
                  <div>{portalTextWrapHtml(filterWidgetList((item?.children || []), 2)[0]?.value)}</div>
                  <Button 
                    style={{ marginTop: 20, padding: 0, color: '#0077f2' }} 
                    size="small" 
                    type="link"
                    onClick={() => navigate(filterWidgetList((item?.children || []), 4)[0]?.linkUrl)}
                  >
                    {filterWidgetList((item?.children || []), 4)[0]?.value}
                  </Button>
                </Panel>
              ))}
            </Collapse>
          </div>}
          {!!filterWidgetList(currentModuleItem, 3)[0]?.value && <div className="FunctionIntroductionAccordion-content-tabsBody-right">
            <Image
              className="FunctionIntroductionAccordion-content-tabsBody-right-image" 
              width={"100%"} 
              src={filterWidgetList(currentModuleItem, 3)[0]?.value}
              preview={false}
            />
          </div>}
        </div>
      </div>
    </div>
  );
}