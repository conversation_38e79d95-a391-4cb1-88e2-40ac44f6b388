import React, { useEffect, useState } from "react";
import { <PERSON>, Stack, Button } from "@mui/material";
import "./index.scss";
import { useNavigate } from "react-router-dom";
import {
  isArrayUtils,
  filterWidgetList,
  formatTreeData,
} from "../../../../utils/commonUtils";
import { formatVariantColor } from "../../../../utils/muiThemeUtils";

//快捷推荐
export default function QuickRecom(props) {
  const { data } = props;
  const navigate = useNavigate();
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList);
      setModuleData([...formatData]);
    }
  }, [data?.widgetList]);

  return (
    <>
      {/* 快捷推荐-start */}
      <Box
        className="home-learning-section"
        style={{
          background:
            filterWidgetList(moduleData, 12)[0]?.value || "transparent",
        }}
      >
        <Box className="home-learning-section-container">
          <Box className="learning-container">
            <Stack
              sx={{ flexWrap: "wrap", justifyContent: "center" }}
              direction={{ xs: "column", sm: "row" }}
              spacing={4}
            >
              {filterWidgetList(moduleData, 6).map((item, index) => (
                <Box className="learning-container-elem" key={item.widgetId}>
                  <Box className="learning-block">
                    <span className="learning-block__link">
                      {filterWidgetList(item?.children || [], 1)[0]?.value}
                    </span>
                    <Box className="learning-block__text">
                      {filterWidgetList(item?.children || [], 2)[0]?.value}
                    </Box>
                    <Box className="learning-block__btn">
                      <Button
                      variant={formatVariantColor(filterWidgetList(item?.children || [], 4)[0]?.style)?.variant}
                      color={formatVariantColor(filterWidgetList(item?.children || [], 4)[0]?.style)?.color}
                      onClick={() => navigate(filterWidgetList(item?.children || [], 4)[0]?.linkUrl || "")}
                      >
                        {filterWidgetList(item?.children || [], 4)[0]?.value}
                      </Button>
                    </Box>
                  </Box>
                </Box>
              ))}
            </Stack>
          </Box>
        </Box>
      </Box>
      {/* 快捷推荐-end */}
    </>
  );
}
