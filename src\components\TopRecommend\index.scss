.SwipeableTextMobileStepper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    padding: 0;
    height: 60px;
    background-color: #f1f7fb;
    box-shadow: 0 4px 16px 0 rgba(77,77,77,.12), 0 1px 2px 0 rgba(77,77,77,.06);
}
.SwipeableTextMobileStepper-MobileStepper {
    position: absolute;
    bottom: 6px;
    left: 50%;
    transform: translate(-50%, 0);
    display: flex !important;
    justify-content: center !important;
    padding: 0 !important;
    background-color: transparent !important;
    .MuiMobileStepper-dots {
        .MuiMobileStepper-dot {
            width: 3px;
            height: 1px;
            border-radius: 0;
        }
        .MuiMobileStepper-dotActive {
            border-radius: 0;
        }
    }
}
.AutoPlaySwipeableViews-item {
    text-align: center;
}