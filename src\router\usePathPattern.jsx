import {useContext} from "react";
import { UNSAFE_RouteContext, matchRoutes, useMatches } from "react-router-dom";
import routes from "./Route"

/* hook */
/* 获取当前path */
export function usePathPattern() {
  return useContext(UNSAFE_RouteContext).matches.reduce((patternPath, { route: { path } }) =>
    patternPath + (path? path.endsWith('*')? path.slice(0, -1): path.endsWith('/')? path: path + '/': ''), '')
}

/* hook */
/* 获取当前路由信息 */
export function useRouteMatch() {
  return useContext(UNSAFE_RouteContext).matches.reduce((patternPath, { route }) => route, undefined)
}

/* hook */
/* 获取当前路由回调参数等 */
export function useCurrentRoutePattern() {
  // return useMatches()
  return useMatches()?.slice(-1)?.[0]
}

/* function */
export function getMatchRoutes(location) {
  return matchRoutes(routes, location)
}

/* function */
/* 获取当前路由信息 */
export function getCurrentRoute(location) {
  let _routes  = matchRoutes(routes, location)
  if(_routes?.length) return _routes[_routes.length - 1].route;
  return null;
}

/* function */
/* 获取当前路由path */
export function getCurrentRoutePath(location) {
  let _routes  = matchRoutes(routes, location)
  if(_routes?.length) {
    return _routes.reduce((patternPath, { route: { path } }) => patternPath + (path? path.endsWith('*')? path.slice(0, -1): path.endsWith('/')? path: path + '/': ''), '')
  }
  return null;
}

