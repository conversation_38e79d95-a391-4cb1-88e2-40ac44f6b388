import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Box, Typography, Button, Paper } from "@mui/material";
import "./ProductDescription.scss";
import { isArrayUtils, filterWidgetList, formatTreeData } from "../../../utils/commonUtils";
import { formatVariantColor } from "../../../utils/muiThemeUtils";
import { skipOS } from "../../../utils/commonUtils";
import { useAuthContext } from "@/context/AppAuthContextProvider";

export default function TopPoster(props) {
  const { data } = props;
  const authActions = useAuthContext();
  const navigate = useNavigate();
  const [moduleData, setModuleData] = useState([]);

  useEffect(() => {
    if (isArrayUtils(data?.widgetList)) {
      let formatData = formatTreeData([], data.widgetList)
      setModuleData([...formatData])
    }
  }, [data?.widgetList]);

  return (
    <>
      {/* 单个产品介绍-start */}
      {filterWidgetList(moduleData, 6).map((item, index) => (
        <Box 
        id={item.linkAnchor}
        key={index} 
        className="products-li" 
        sx={{
          borderBottom: (index == moduleData.length - 1 && index%2) ? '1px solid #f2f2f2' : '',
          background: !(index%2) ? filterWidgetList(moduleData, 12)[0]?.value || "transparent" : "transparent"
        }}
        >
          <Typography variant="h5" display="inline-block">{filterWidgetList(item?.children, 1)[0]?.value}</Typography>
          <Typography sx={{ margin: '10px auto', maxWidth: '1200px', color: '#666' }} variant="body2" >{filterWidgetList(item?.children, 2)[0]?.value}</Typography>
          <Paper 
          className="products-li-page"
          sx={{ overflow: 'hidden' }}
          elevation={1}
          >
            <img height="100%" src={filterWidgetList(item?.children, 3)[0]?.value}/>
          </Paper>
          <Box className="products-li-btn">
            {(filterWidgetList(item?.children, 4) || []).map((btnitem, btnindex) => (
              <Button 
                key={btnitem.widgetId}
                sx={{ ml: btnindex > 0 ? "10px" : ""}}
                variant={formatVariantColor(btnitem.style)?.variant}
                color={formatVariantColor(btnitem.style)?.color}
                onClick={btnitem.linkUrl === "os"
                ? () => skipOS(authActions.isAuthenticated)
                : () => navigate(btnitem.linkUrl)}
              >
                {btnitem.value}
              </Button>
            ))}
          </Box>
        </Box>
      ))}
      {/* 单个产品介绍-end */}
    </>
  );
}
