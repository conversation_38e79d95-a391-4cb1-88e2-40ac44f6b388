// 头部搜索栏
.issuehome-header {
  padding: 5px 20px 10px 20px;
  background: #ffffff;
  border-bottom: 1px solid #f2f2f2;
  .ant-btn-primary {
    border-color: #3279fe !important;
    background-color: #3279fe !important;
  }
  input {
    font-size: 12px;
  }
  a {
    // color: #3279fe !important;
  }
  .issuehome-header-title {
    display: inline-block;
    font-size: 16px;
    color: #333333;
    font-weight: bold;
  }
  &-btns {
    button {
      height: 24px;
      width: 24px;
    }
  }
  button {
    border-radius: 12px;
    font-size: 12px;
  }
  .ant-space span {
    font-size: 12px;
  }
  // 搜索条件配置
  .issue-searchlist {
    &-item {
      display: inline-block;
    }
    .shanchu1:before {
      color: #bfbfbf;
      font-size: 16px;
    }
    .ant-space-item {
      display: contents;
    }
    .ant-btn-default {
      padding: 0;
      border: 0;
      box-shadow: unset !important;
    }
    .ant-input-search-button {
      border: 1px solid #d9d9d9 !important;
      height: 29px;
    }
    .ant-btn-two-chinese-chars > *:not(.anticon) {
      letter-spacing: unset !important;
      margin-right: unset !important;
    }
    // 调整select组件间距
    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      padding: 0px 11px 0px 0px;
    }
  }
}

// 新建issue项目可拖拽弹窗
.createIssueDraModal {
  .ant-modal-content {
    .ant-modal-header {
      border: none;
    }
    .ant-modal-footer {
      display: none;
    }
  }
}

// 新建issue 弹窗
.add-issue {
  .ant-drawer-body {
    // 滚动条配置
    &::-webkit-scrollbar {
      // display: none;
      width: 8px;
      height: 8px;
    }
    &::-webkit-scrollbar-track {
      background-color: #f2f2f2;
      border-radius: 10px;
    }
    &::-webkit-scrollbar-thumb {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: #d5d5d5;
      border-radius: 10px;
    }
  }
}

.autoNoTip {
  font-size: 12px;
  color: #999;
}
.issueSvg {
  svg {
    width: 14px;
    height: 14px;
  }
}
// 主页加载配置
.issueSpin {
  height: 100%;
  // display: flex;
  // flex: auto;
  .ant-spin-container {
    height: 100%;
    // flex: auto;
    .ant-layout {
      height: 100%;
    }
  }
}
.issueColor {
  color: #3279fe;
}
.issue-color-666 {
  color: #666;
}

// excel导出
.export-modal {
  .ant-modal-body {
    padding: 5px 24px;
  }
  &-content {
    margin: 0px 20px;
  }
}

// 自定义表单弹窗
.customer-setting-drawer {
  .ant-drawer-content-wrapper {
    .ant-drawer-content {
      .ant-drawer-wrapper-body {
        .ant-drawer-body {
          padding: 0 24px 24px;
        }
      }
    }
  }
}
