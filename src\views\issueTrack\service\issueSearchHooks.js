import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useNavigate, useLocation, useSearchParams, useParams } from "react-router-dom";
import { team_553_get_front_query_query } from "@/api/query/query";
import { team_552_save_front_query } from "@/api/http";
import { eNavId } from "@/utils/enum";
import * as toolUtil from "@/utils/toolUtil";
import {isEmpty} from "@/utils/ArrayUtils";

// issue 修改url查询参数
export function useIssueSearchParams() {

  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const { nid: issueNodeId } = useParams();//issue节点Id
  const issueQueryId = searchParams.get("queryId"); //过滤条件id
  const navId = searchParams.get(eNavId); // tms系统提交工单Id
  function setIssueSearchParams({issueNodeId, queryId, navId}) {
    let pathname = location.pathname;
    let search = toolUtil.UrlTurnJson(location.search);
    if (typeof issueNodeId !== 'undefined') { 
      if (isEmpty(issueNodeId)) {
        pathname = `/workorder`
      } else {
        pathname = `/workorder/issueId/${issueNodeId}`
      }
    }
    if (typeof queryId !== 'undefined') {
      if (isEmpty(queryId)) {
        delete search.queryId;
      } else {
        search = { ...search, queryId: queryId };
      }
    }
    if (typeof navId !== 'undefined') {
      if (isEmpty(navId)) {
        delete search.navId;
      } else {
        search = { ...search, navId: navId };
      }
    }
    navigate({
      pathname: pathname,
      search: `${toolUtil.JsonTurnUrl(search)}`,
    });
  }
  return {
    issueNodeId,
    issueQueryId,
    navId,
    setIssueSearchParams: setIssueSearchParams,
  };
}

// issue 获取查询条件
export function useCriteriaList({ teamId, _criteriaList }) {
  const [criteriaList, setCriteriaList] = useState(_criteriaList ? _criteriaList : []); //查询条件列表
  const { issueQueryId, setIssueSearchParams } = useIssueSearchParams();
  const [loading, setLoading] = useState(true); // Loading状态
  const queryClient = useQueryClient();

  useEffect(() => {
    // console.log("issueQueryIdissueQueryIdissueQueryId",issueQueryId)
    loadTeam553GetFrontQuery(teamId, issueQueryId);
  }, []);

  const loadTeam553GetFrontQuery = async (teamId, queryId) => {
    try {
      setLoading(true);
      if (!queryId) {
        setLoading(false);
        setCriteriaList([]);
        return;
      }
      const query = team_553_get_front_query_query(teamId, queryId);
      const result = queryClient.getQueryData(query.queryKey) ?? (await queryClient.fetchQuery({ ...query, cacheTime: Infinity }));
      if (result.resultCode == 200) {
        const queryJson = JSON.parse(result.queryJson);
        setCriteriaList(queryJson?.criteriaList || []);
        setLoading(false);
      } else {
        throw result.resultMessage;
      }
    } catch (error) {
      console.error("useCriteriaList loadTeam553GetFrontQuery", error);
      setCriteriaList([]);
      setLoading(false);
    }
  };

  //  更新查询条件
  const updateQuery = async (issueNodeId, searchQuery) => {
    const queryJson = JSON.stringify(searchQuery);
    const params = {
      teamId,
      queryJson,
    };
    const result = await team_552_save_front_query(params);
    if (result.resultCode == 200) {
      // 对 team_553_get_front_query 加入缓存数据
      const queryId = result.id;
      const query = team_553_get_front_query_query(teamId, queryId);
      queryClient.setQueryData(query.queryKey, (oldData) => {
        return {
          ...oldData,
          id: queryId,
          queryJson: queryJson,
        };
      });
      setCriteriaList(JSON.parse(JSON.stringify(searchQuery.criteriaList)));
      setIssueSearchParams({issueNodeId, queryId: result.id});
    }
  };

  return [criteriaList, updateQuery, loading];
}
