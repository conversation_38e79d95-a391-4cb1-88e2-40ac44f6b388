@import "../../assets/variables.scss";

.error-modal {
  // 标题
  .error-modal-title{
    display: flex;
    align-items: center;
    .error-modal-title-icon {
      color: $warnRed;
      font-size: 24px;
    }
    &-text {
      font-size: 20px;
    }  
  }
  .error-modal-body {
    display: flex;
    .error-modal-warn {
      margin-right: 5px;
      font-size: 16px;
      color: $tipTextColor;
    }
  }
  .error-modal-confirm {
    display: flex;
    justify-content: end;
  }
  .error-modal-message{
    display: flex;
  }
  .error-modal-message-label {
    white-space: nowrap;
  }
  // 错误信息
  .error-modal-message-text {
    max-width: 450px;
    max-height: 450px;
    overflow: auto;
  }
  // 边框
  .error-modal-message-text-border {
    border: 1px #f2f2f2 solid;
    padding: 0px 5px;
  }
}