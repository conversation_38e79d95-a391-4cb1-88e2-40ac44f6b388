// .home {
//     height: 100vh;
//     overflow: auto;
//     overflow-x: hidden;
// }

// .TopMenuBar-box {
//     position: fixed;
//     top: 57px;
//     left: 50%;
//     transform: translate(-50%, 0);
//     margin: auto;
//     padding: 0 20px;
//     width: 100%;
//     z-index: 1;
//     // box-shadow: 0 2px 4px 0 rgb(77 77 77 / 6%), 0 1px 2px 0 rgb(77 77 77 / 4%);
// }
// .home-head__bubble {
//     position: absolute;
//     top: -360px;
//     right: -800px;
//     width: 1468px;
//     border-radius: 50%;
//     background: linear-gradient(180deg,#1876d2 0,#03a9f4 99.18%);
//     z-index: -1;
// }

// .home-head__bubble::before {
//     content: '';
//     padding-top: 61.4441416894%;
//     display: block;
// }

// 查看更多产品
.see-more-products-btn {
    padding: 16px 0 !important;
    width: 100%;
    text-align: center;
    background-color: #f1f7fb !important
}

.home-learning-section {
    padding: 50px 0;
    background-color: #f1f7fb;
    overflow: hidden;
    &-container {
        margin: auto;
        padding: 0 20px;
        width: 1260px;
        .learning-container {
            // display: flex;
            // flex-wrap: wrap;
            // margin: -20px;
            &-elem {
                width: 350px;
                min-height: 310px;
                padding: 20px;
                box-sizing: border-box;
                .learning-block {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    height: 100%;
                    padding: 28px 20px 22px;
                    border-radius: 4px;
                    box-shadow: 0 4px 16px 0 rgb(77 77 77 / 12%), 0 1px 2px 0 rgb(77 77 77 / 6%);
                    background-color: #fff;
                    box-sizing: border-box;
                    .learning-block__link {
                        display: block;
                        color: #202020;
                        font-size: 20px;
                        font-weight: bold;
                        transition: color .15s ease-in-out;
                        outline: 0;
                    }
                    .learning-block__text {
                        color: rgba(0,0,0,.7);
                        padding-top: 12px;
                        font-weight: 400;
                    }
                    .learning-block__btn {
                        margin-top: auto;
                        padding-top: 30px;
                        .text-button {
                            display: inline-block;
                            color: #0077f2;
                            line-height: 0;
                            text-decoration: none;
                            text-align: center;
                            padding: 4px 0;
                            transition: color .15s ease-in-out;
                        }
                    }
                }
            }
            &-elem:nth-child(4n) {
                margin-left: 0;
            }
        }
    }
}